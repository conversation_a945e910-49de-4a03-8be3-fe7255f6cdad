// Accessibility utilities and helpers

// Generate unique IDs for form elements
export const generateId = (prefix = 'element') => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// ARIA label helpers
export const getAriaLabel = (label, required = false, error = null) => {
  let ariaLabel = label;
  if (required) {
    ariaLabel += ', required';
  }
  if (error) {
    ariaLabel += `, error: ${error}`;
  }
  return ariaLabel;
};

// Focus management
export const focusElement = (elementId, delay = 0) => {
  setTimeout(() => {
    const element = document.getElementById(elementId);
    if (element) {
      element.focus();
    }
  }, delay);
};

// Keyboard navigation helpers
export const handleKeyboardNavigation = (event, actions = {}) => {
  const { key } = event;
  
  switch (key) {
    case 'Enter':
      if (actions.onEnter) {
        event.preventDefault();
        actions.onEnter(event);
      }
      break;
    case 'Escape':
      if (actions.onEscape) {
        event.preventDefault();
        actions.onEscape(event);
      }
      break;
    case 'ArrowDown':
      if (actions.onArrowDown) {
        event.preventDefault();
        actions.onArrowDown(event);
      }
      break;
    case 'ArrowUp':
      if (actions.onArrowUp) {
        event.preventDefault();
        actions.onArrowUp(event);
      }
      break;
    case 'Tab':
      if (actions.onTab) {
        actions.onTab(event);
      }
      break;
    default:
      break;
  }
};

// Screen reader announcements
export const announceToScreenReader = (message, priority = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.setAttribute('class', 'sr-only');
  announcement.style.position = 'absolute';
  announcement.style.left = '-10000px';
  announcement.style.width = '1px';
  announcement.style.height = '1px';
  announcement.style.overflow = 'hidden';
  
  document.body.appendChild(announcement);
  announcement.textContent = message;
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

// Color contrast helpers
export const getContrastRatio = (color1, color2) => {
  // Simplified contrast ratio calculation
  // In a real app, you'd use a proper color contrast library
  const getLuminance = (color) => {
    // This is a simplified version
    const rgb = color.match(/\d+/g);
    if (!rgb) return 0;
    
    const [r, g, b] = rgb.map(c => {
      c = parseInt(c) / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };
  
  const l1 = getLuminance(color1);
  const l2 = getLuminance(color2);
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
};

// Form validation announcements
export const announceFormError = (fieldName, error) => {
  announceToScreenReader(`Error in ${fieldName}: ${error}`, 'assertive');
};

export const announceFormSuccess = (message) => {
  announceToScreenReader(message, 'polite');
};

// Skip link functionality
export const createSkipLink = (targetId, text = 'Skip to main content') => {
  const skipLink = document.createElement('a');
  skipLink.href = `#${targetId}`;
  skipLink.textContent = text;
  skipLink.className = 'skip-link';
  skipLink.style.cssText = `
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    transition: top 0.3s;
  `;
  
  skipLink.addEventListener('focus', () => {
    skipLink.style.top = '6px';
  });
  
  skipLink.addEventListener('blur', () => {
    skipLink.style.top = '-40px';
  });
  
  return skipLink;
};

// Responsive design helpers
export const getBreakpoint = () => {
  const width = window.innerWidth;
  if (width < 600) return 'xs';
  if (width < 900) return 'sm';
  if (width < 1200) return 'md';
  if (width < 1536) return 'lg';
  return 'xl';
};

export const isMobile = () => {
  return window.innerWidth < 900;
};

export const isTablet = () => {
  return window.innerWidth >= 600 && window.innerWidth < 1200;
};

export const isDesktop = () => {
  return window.innerWidth >= 1200;
};

// Touch device detection
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Reduced motion preference
export const prefersReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// High contrast preference
export const prefersHighContrast = () => {
  return window.matchMedia('(prefers-contrast: high)').matches;
};

// Dark mode preference
export const prefersDarkMode = () => {
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
};

// Accessibility testing helpers
export const checkAccessibility = (element) => {
  const issues = [];
  
  // Check for missing alt text on images
  const images = element.querySelectorAll('img');
  images.forEach(img => {
    if (!img.alt && !img.getAttribute('aria-label')) {
      issues.push('Image missing alt text');
    }
  });
  
  // Check for missing form labels
  const inputs = element.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    const hasLabel = input.labels && input.labels.length > 0;
    const hasAriaLabel = input.getAttribute('aria-label');
    const hasAriaLabelledBy = input.getAttribute('aria-labelledby');
    
    if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
      issues.push(`Form input missing label: ${input.type || input.tagName}`);
    }
  });
  
  // Check for missing heading hierarchy
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let lastLevel = 0;
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > lastLevel + 1) {
      issues.push(`Heading hierarchy skip: ${heading.tagName} after h${lastLevel}`);
    }
    lastLevel = level;
  });
  
  return issues;
};

export default {
  generateId,
  getAriaLabel,
  focusElement,
  handleKeyboardNavigation,
  announceToScreenReader,
  announceFormError,
  announceFormSuccess,
  createSkipLink,
  getBreakpoint,
  isMobile,
  isTablet,
  isDesktop,
  isTouchDevice,
  prefersReducedMotion,
  prefersHighContrast,
  prefersDarkMode,
  checkAccessibility,
};
