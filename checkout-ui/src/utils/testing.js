// Testing utilities for the SwiftCheckout application

// Mock data generators
export const generateMockProduct = (overrides = {}) => ({
  id: `prod-${Date.now()}`,
  name: 'Sample Product',
  description: 'This is a sample product description for testing purposes.',
  price: 999,
  category: 'Electronics',
  stock: 50,
  sku: `SKU-${Date.now()}`,
  weight: 0.5,
  dimensions: '10x10x5 cm',
  image: 'https://via.placeholder.com/300x300?text=Product',
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const generateMockOrder = (overrides = {}) => ({
  id: `order-${Date.now()}`,
  orderNumber: `ORD-${Date.now()}`,
  customerName: '<PERSON>',
  customerEmail: '<EMAIL>',
  customerPhone: '+************',
  amount: 1999,
  status: 'pending',
  paymentStatus: 'pending',
  items: [
    {
      productId: 'prod-1',
      productName: 'Sample Product',
      quantity: 2,
      price: 999,
      total: 1998,
    },
  ],
  shippingAddress: {
    street: '123 Main Street',
    city: 'Mumbai',
    state: 'Maharashtra',
    pincode: '400001',
    country: 'India',
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const generateMockSeller = (overrides = {}) => ({
  id: `seller-${Date.now()}`,
  name: 'Test Seller',
  email: '<EMAIL>',
  phone: '+************',
  businessName: 'Test Business',
  businessType: 'retail',
  gstNumber: '27**********1Z5',
  panNumber: '**********',
  bankDetails: {
    accountNumber: '**********',
    ifscCode: 'HDFC0000123',
    accountHolderName: 'Test Seller',
    bankName: 'HDFC Bank',
  },
  upiId: 'testseller@paytm',
  isVerified: true,
  createdAt: new Date().toISOString(),
  ...overrides,
});

// API response mocks
export const mockApiResponse = (data, success = true, message = 'Success') => ({
  success,
  message,
  data,
  timestamp: new Date().toISOString(),
});

export const mockApiError = (message = 'An error occurred', status = 500) => ({
  response: {
    status,
    data: {
      success: false,
      error: message,
      timestamp: new Date().toISOString(),
    },
  },
});

// Form testing helpers
export const fillForm = (form, data) => {
  Object.keys(data).forEach(key => {
    const input = form.querySelector(`[name="${key}"]`);
    if (input) {
      if (input.type === 'checkbox' || input.type === 'radio') {
        input.checked = data[key];
      } else {
        input.value = data[key];
      }
      
      // Trigger change event
      const event = new Event('change', { bubbles: true });
      input.dispatchEvent(event);
    }
  });
};

export const submitForm = (form) => {
  const submitButton = form.querySelector('[type="submit"]');
  if (submitButton) {
    submitButton.click();
  } else {
    const event = new Event('submit', { bubbles: true });
    form.dispatchEvent(event);
  }
};

// Component testing helpers
export const waitForElement = (selector, timeout = 5000) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const checkElement = () => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
      } else if (Date.now() - startTime > timeout) {
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      } else {
        setTimeout(checkElement, 100);
      }
    };
    
    checkElement();
  });
};

export const simulateClick = (element) => {
  const event = new MouseEvent('click', {
    bubbles: true,
    cancelable: true,
    view: window,
  });
  element.dispatchEvent(event);
};

export const simulateKeyPress = (element, key, options = {}) => {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
  element.dispatchEvent(event);
};

// Performance testing
export const measurePerformance = (name, fn) => {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  console.log(`${name} took ${end - start} milliseconds`);
  return result;
};

export const measureAsyncPerformance = async (name, fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  
  console.log(`${name} took ${end - start} milliseconds`);
  return result;
};

// Accessibility testing
export const checkAccessibility = (element) => {
  const issues = [];
  
  // Check for missing alt text
  const images = element.querySelectorAll('img');
  images.forEach(img => {
    if (!img.alt && !img.getAttribute('aria-label')) {
      issues.push('Image missing alt text');
    }
  });
  
  // Check for missing form labels
  const inputs = element.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    const hasLabel = input.labels && input.labels.length > 0;
    const hasAriaLabel = input.getAttribute('aria-label');
    const hasAriaLabelledBy = input.getAttribute('aria-labelledby');
    
    if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
      issues.push(`Form input missing label: ${input.type || input.tagName}`);
    }
  });
  
  // Check for proper heading hierarchy
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let lastLevel = 0;
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > lastLevel + 1) {
      issues.push(`Heading hierarchy skip: ${heading.tagName} after h${lastLevel}`);
    }
    lastLevel = level;
  });
  
  return issues;
};

// Local storage testing
export const mockLocalStorage = () => {
  const storage = {};
  
  return {
    getItem: (key) => storage[key] || null,
    setItem: (key, value) => {
      storage[key] = value.toString();
    },
    removeItem: (key) => {
      delete storage[key];
    },
    clear: () => {
      Object.keys(storage).forEach(key => delete storage[key]);
    },
    get length() {
      return Object.keys(storage).length;
    },
    key: (index) => {
      const keys = Object.keys(storage);
      return keys[index] || null;
    },
  };
};

// Network testing
export const mockNetworkDelay = (ms = 1000) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const mockNetworkError = () => {
  throw new Error('Network error');
};

// Test data cleanup
export const cleanupTestData = () => {
  // Clear local storage
  localStorage.clear();
  
  // Clear session storage
  sessionStorage.clear();
  
  // Reset any global state if needed
  console.log('Test data cleaned up');
};

export default {
  generateMockProduct,
  generateMockOrder,
  generateMockSeller,
  mockApiResponse,
  mockApiError,
  fillForm,
  submitForm,
  waitForElement,
  simulateClick,
  simulateKeyPress,
  measurePerformance,
  measureAsyncPerformance,
  checkAccessibility,
  mockLocalStorage,
  mockNetworkDelay,
  mockNetworkError,
  cleanupTestData,
};
