// Global error handler for API responses
export const handleApiError = (error) => {
  console.error('API Error:', error);

  // Network error
  if (!error.response) {
    return {
      message: 'Network error. Please check your internet connection.',
      type: 'network',
      status: null,
    };
  }

  // HTTP error responses
  const { status, data } = error.response;

  switch (status) {
    case 400:
      return {
        message: data?.error || 'Invalid request. Please check your input.',
        type: 'validation',
        status: 400,
        details: data?.details || null,
      };

    case 401:
      return {
        message: 'Authentication failed. Please login again.',
        type: 'auth',
        status: 401,
      };

    case 403:
      return {
        message: 'Access denied. You don\'t have permission for this action.',
        type: 'permission',
        status: 403,
      };

    case 404:
      return {
        message: 'Resource not found.',
        type: 'notfound',
        status: 404,
      };

    case 409:
      return {
        message: data?.error || 'Conflict. Resource already exists.',
        type: 'conflict',
        status: 409,
      };

    case 422:
      return {
        message: data?.error || 'Validation failed.',
        type: 'validation',
        status: 422,
        details: data?.details || null,
      };

    case 429:
      return {
        message: 'Too many requests. Please try again later.',
        type: 'ratelimit',
        status: 429,
      };

    case 500:
      return {
        message: 'Server error. Please try again later.',
        type: 'server',
        status: 500,
      };

    case 503:
      return {
        message: 'Service temporarily unavailable. Please try again later.',
        type: 'service',
        status: 503,
      };

    default:
      return {
        message: data?.error || 'An unexpected error occurred.',
        type: 'unknown',
        status,
      };
  }
};

// Format validation errors for display
export const formatValidationErrors = (details) => {
  if (!details || !Array.isArray(details)) {
    return null;
  }

  return details.reduce((acc, error) => {
    if (error.path) {
      acc[error.path] = error.msg || 'Invalid value';
    }
    return acc;
  }, {});
};

// Check if error is a network error
export const isNetworkError = (error) => {
  return !error.response && error.code === 'NETWORK_ERROR';
};

// Check if error requires re-authentication
export const requiresReauth = (error) => {
  return error.response?.status === 401;
};

// Retry logic for failed requests
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  let lastError;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors (4xx)
      if (error.response?.status >= 400 && error.response?.status < 500) {
        throw error;
      }

      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
  }

  throw lastError;
};

// Toast notification helper
export const showErrorToast = (error, toast) => {
  const errorInfo = handleApiError(error);
  
  if (toast) {
    toast.error(errorInfo.message, {
      duration: 5000,
      position: 'top-right',
    });
  }
  
  return errorInfo;
};

// Success notification helper
export const showSuccessToast = (message, toast) => {
  if (toast) {
    toast.success(message, {
      duration: 3000,
      position: 'top-right',
    });
  }
};
