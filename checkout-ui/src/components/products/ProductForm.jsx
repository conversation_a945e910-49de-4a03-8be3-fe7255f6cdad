import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Grid,
  Card,
  CardContent,
  IconButton,
  Alert,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import {
  CloudUpload,
  Delete,
  Image as ImageIcon,
  CurrencyRupee,
  Inventory,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useMutation } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import { productService } from '../../services/productService';
import { useNotification } from '../../contexts/NotificationContext';
import { handleApiError } from '../../utils/errorHandler';

// Validation schema
const productSchema = yup.object({
  name: yup.string().required('Product name is required').min(2, 'Name must be at least 2 characters'),
  description: yup.string().required('Description is required').min(10, 'Description must be at least 10 characters'),
  price: yup.number().required('Price is required').min(0.01, 'Price must be greater than 0'),
  stock: yup.number().required('Stock is required').min(0, 'Stock cannot be negative'),
  category: yup.string().required('Category is required'),
  sku: yup.string(),
  weight: yup.number().min(0, 'Weight cannot be negative'),
  dimensions: yup.string(),
});

const ProductForm = ({ open, onClose, product, onSuccess }) => {
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(product?.image || '');
  const [uploadingImage, setUploadingImage] = useState(false);
  const { showSuccess, showError } = useNotification();

  const isEditing = Boolean(product);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm({
    resolver: yupResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      price: '',
      stock: '',
      category: '',
      sku: '',
      weight: '',
      dimensions: '',
      isActive: true,
    },
  });

  // Watch isActive for the switch
  const isActive = watch('isActive');

  // Reset form when product changes
  useEffect(() => {
    if (product) {
      reset({
        name: product.name || '',
        description: product.description || '',
        price: product.price || '',
        stock: product.inventory?.quantity || '',
        category: product.category || '',
        sku: product.sku || '',
        weight: product.weight || '',
        dimensions: product.dimensions || '',
        isActive: product.isActive ?? true,
      });
      setImagePreview(product.image || '');
    } else {
      reset({
        name: '',
        description: '',
        price: '',
        stock: '',
        category: '',
        sku: '',
        weight: '',
        dimensions: '',
        isActive: true,
      });
      setImagePreview('');
    }
    setImageFile(null);
  }, [product, reset]);

  // Create/Update product mutation
  const productMutation = useMutation({
    mutationFn: async (data) => {
      let imageUrl = imagePreview;

      // Upload image if new file selected
      if (imageFile) {
        setUploadingImage(true);
        try {
          const uploadResult = await productService.uploadImage(imageFile);
          imageUrl = uploadResult.imageUrl;
        } finally {
          setUploadingImage(false);
        }
      }

      const productData = {
        ...data,
        image: imageUrl,
        inventory: {
          quantity: parseInt(data.stock) || 0,
          trackInventory: true,
          lowStockThreshold: 5
        }
      };

      if (isEditing) {
        return productService.updateProduct(product.id, productData);
      } else {
        return productService.createProduct(productData);
      }
    },
    onSuccess: (data) => {
      showSuccess(
        isEditing ? 'Product updated successfully!' : 'Product created successfully!',
        { title: 'Success' }
      );
      onSuccess();
      handleClose();
    },
    onError: (error) => {
      const errorInfo = handleApiError(error);
      showError(errorInfo.message, {
        title: 'Failed to save product',
        duration: 6000
      });
    },
  });

  const handleClose = () => {
    reset();
    setImageFile(null);
    setImagePreview('');
    onClose();
  };

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreview('');
  };

  const onSubmit = (data) => {
    productMutation.mutate(data);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>
        <Typography variant="h5" sx={{ fontWeight: 600 }}>
          {isEditing ? 'Edit Product' : 'Add New Product'}
        </Typography>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          <Grid container spacing={3}>
            {/* Image Upload */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                Product Image
              </Typography>
              <Card sx={{ borderRadius: 2, border: '2px dashed', borderColor: 'divider' }}>
                <CardContent sx={{ textAlign: 'center', py: 4 }}>
                  {imagePreview ? (
                    <Box sx={{ position: 'relative', display: 'inline-block' }}>
                      <img
                        src={imagePreview}
                        alt="Product preview"
                        style={{
                          maxWidth: '200px',
                          maxHeight: '200px',
                          borderRadius: '8px',
                        }}
                      />
                      <IconButton
                        onClick={handleRemoveImage}
                        sx={{
                          position: 'absolute',
                          top: -8,
                          right: -8,
                          backgroundColor: 'error.main',
                          color: 'white',
                          '&:hover': {
                            backgroundColor: 'error.dark',
                          },
                        }}
                        size="small"
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Box>
                  ) : (
                    <Box>
                      <ImageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="body1" color="text.secondary" gutterBottom>
                        Upload product image
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Recommended: 800x800px, max 5MB
                      </Typography>
                    </Box>
                  )}
                  
                  <Box sx={{ mt: 2 }}>
                    <input
                      accept="image/*"
                      style={{ display: 'none' }}
                      id="image-upload"
                      type="file"
                      onChange={handleImageChange}
                    />
                    <label htmlFor="image-upload">
                      <Button
                        variant="outlined"
                        component="span"
                        startIcon={<CloudUpload />}
                        sx={{ borderRadius: 2 }}
                      >
                        {imagePreview ? 'Change Image' : 'Upload Image'}
                      </Button>
                    </label>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Basic Information */}
            <Grid item xs={12} md={6}>
              <TextField
                {...register('name')}
                label="Product Name"
                fullWidth
                error={!!errors.name}
                helperText={errors.name?.message}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                {...register('category')}
                label="Category"
                fullWidth
                error={!!errors.category}
                helperText={errors.category?.message}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                {...register('description')}
                label="Description"
                fullWidth
                multiline
                rows={3}
                error={!!errors.description}
                helperText={errors.description?.message}
                sx={{ mb: 2 }}
              />
            </Grid>

            {/* Pricing and Inventory */}
            <Grid item xs={12} md={6}>
              <TextField
                {...register('price')}
                label="Price"
                type="number"
                fullWidth
                error={!!errors.price}
                helperText={errors.price?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <CurrencyRupee fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                {...register('stock')}
                label="Stock Quantity"
                type="number"
                fullWidth
                error={!!errors.stock}
                helperText={errors.stock?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Inventory fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 2 }}
              />
            </Grid>

            {/* Additional Details */}
            <Grid item xs={12} md={6}>
              <TextField
                {...register('sku')}
                label="SKU (Optional)"
                fullWidth
                error={!!errors.sku}
                helperText={errors.sku?.message}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                {...register('weight')}
                label="Weight (kg)"
                type="number"
                fullWidth
                error={!!errors.weight}
                helperText={errors.weight?.message}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                {...register('dimensions')}
                label="Dimensions (L x W x H)"
                fullWidth
                placeholder="e.g., 10 x 5 x 3 cm"
                error={!!errors.dimensions}
                helperText={errors.dimensions?.message}
                sx={{ mb: 2 }}
              />
            </Grid>

            {/* Status */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isActive}
                    onChange={(e) => setValue('isActive', e.target.checked)}
                  />
                }
                label="Product is active and available for sale"
              />
            </Grid>
          </Grid>

          {/* Error Display */}
          {productMutation.error && (
            <Alert severity="error" sx={{ mt: 2, borderRadius: 2 }}>
              {productMutation.error.message}
            </Alert>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleClose} sx={{ borderRadius: 2 }}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={productMutation.isLoading || uploadingImage}
            sx={{ borderRadius: 2, minWidth: 120 }}
          >
            {productMutation.isLoading || uploadingImage ? (
              <CircularProgress size={20} />
            ) : (
              isEditing ? 'Update Product' : 'Add Product'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ProductForm;
