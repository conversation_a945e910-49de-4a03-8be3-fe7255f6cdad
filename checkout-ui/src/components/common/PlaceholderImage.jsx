import React from 'react';
import { Box, Typography } from '@mui/material';
import { Image as ImageIcon } from '@mui/icons-material';

const PlaceholderImage = ({ width = '100%', height = 200, text = 'No Image' }) => {
  return (
    <Box
      sx={{
        width,
        height,
        backgroundColor: 'grey.100',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'grey.300',
      }}
    >
      <ImageIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
      <Typography variant="caption" color="grey.500">
        {text}
      </Typography>
    </Box>
  );
};

export default PlaceholderImage;
