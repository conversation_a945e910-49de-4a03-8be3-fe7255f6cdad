import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  CardContent,
  <PERSON><PERSON>er,
  <PERSON><PERSON>,
  <PERSON>ertTitle 
} from '@mui/material';
import { 
  ErrorOutline, 
  Refresh, 
  Home,
  BugReport 
} from '@mui/icons-material';
import { motion } from 'framer-motion';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Report error to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // You can integrate with error reporting services like Sentry here
      console.log('Error reported to monitoring service');
    }
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    });
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  render() {
    if (this.state.hasError) {
      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <Container maxWidth="md" sx={{ py: 8 }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card 
              sx={{ 
                borderRadius: 3,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                overflow: 'hidden'
              }}
            >
              <CardContent sx={{ p: 6, textAlign: 'center' }}>
                <ErrorOutline 
                  sx={{ 
                    fontSize: 80, 
                    color: 'error.main', 
                    mb: 3 
                  }} 
                />
                
                <Typography 
                  variant="h4" 
                  sx={{ fontWeight: 700, mb: 2 }}
                >
                  Oops! Something went wrong
                </Typography>
                
                <Typography 
                  variant="body1" 
                  color="text.secondary" 
                  sx={{ mb: 4, maxWidth: 500, mx: 'auto' }}
                >
                  We're sorry for the inconvenience. An unexpected error occurred 
                  while loading this page. Please try refreshing or go back to the dashboard.
                </Typography>

                {isDevelopment && this.state.error && (
                  <Alert 
                    severity="error" 
                    sx={{ 
                      mb: 4, 
                      textAlign: 'left',
                      '& .MuiAlert-message': {
                        width: '100%'
                      }
                    }}
                  >
                    <AlertTitle>Development Error Details</AlertTitle>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 2 }}>
                      <strong>Error:</strong> {this.state.error.toString()}
                    </Typography>
                    {this.state.errorInfo && (
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontFamily: 'monospace',
                          whiteSpace: 'pre-wrap',
                          fontSize: '0.75rem',
                          maxHeight: 200,
                          overflow: 'auto',
                          backgroundColor: 'rgba(0,0,0,0.05)',
                          p: 1,
                          borderRadius: 1
                        }}
                      >
                        {this.state.errorInfo.componentStack}
                      </Typography>
                    )}
                  </Alert>
                )}

                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    startIcon={<Refresh />}
                    onClick={this.handleRetry}
                    sx={{ borderRadius: 2, px: 3 }}
                  >
                    Try Again
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<Home />}
                    onClick={this.handleGoHome}
                    sx={{ borderRadius: 2, px: 3 }}
                  >
                    Go to Dashboard
                  </Button>
                </Box>

                {this.state.errorId && (
                  <Typography 
                    variant="caption" 
                    color="text.secondary" 
                    sx={{ mt: 4, display: 'block' }}
                  >
                    Error ID: {this.state.errorId}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </Container>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  return function WithErrorBoundaryComponent(props) {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};

export default ErrorBoundary;
