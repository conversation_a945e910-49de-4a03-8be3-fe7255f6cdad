import api from './authService';

export const analyticsService = {
  // Get dashboard overview
  async getDashboardOverview(period = '30d') {
    try {
      console.log('📊 API: Fetching dashboard overview', period);
      const response = await api.get('/analytics/overview', {
        params: { period }
      });
      console.log('✅ API: Dashboard overview fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch dashboard overview', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch dashboard overview'
      );
    }
  },

  // Get sales analytics
  async getSalesAnalytics(period = '30d', granularity = 'day') {
    try {
      console.log('📈 API: Fetching sales analytics', { period, granularity });
      const response = await api.get('/analytics/sales', {
        params: { period, granularity }
      });
      console.log('✅ API: Sales analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch sales analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch sales analytics'
      );
    }
  },

  // Get product performance
  async getProductPerformance(period = '30d', limit = 10) {
    try {
      console.log('📦 API: Fetching product performance', { period, limit });
      const response = await api.get('/analytics/products', {
        params: { period, limit }
      });
      console.log('✅ API: Product performance fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch product performance', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch product performance'
      );
    }
  },

  // Get customer analytics
  async getCustomerAnalytics(period = '30d') {
    try {
      console.log('👥 API: Fetching customer analytics', period);
      const response = await api.get('/analytics/customers', {
        params: { period }
      });
      console.log('✅ API: Customer analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch customer analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch customer analytics'
      );
    }
  },

  // Get revenue analytics
  async getRevenueAnalytics(period = '30d', breakdown = 'daily') {
    try {
      console.log('💰 API: Fetching revenue analytics', { period, breakdown });
      const response = await api.get('/analytics/revenue', {
        params: { period, breakdown }
      });
      console.log('✅ API: Revenue analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch revenue analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch revenue analytics'
      );
    }
  },

  // Get order analytics
  async getOrderAnalytics(period = '30d') {
    try {
      console.log('📋 API: Fetching order analytics', period);
      const response = await api.get('/analytics/orders', {
        params: { period }
      });
      console.log('✅ API: Order analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch order analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch order analytics'
      );
    }
  },

  // Get traffic analytics
  async getTrafficAnalytics(period = '30d') {
    try {
      console.log('🌐 API: Fetching traffic analytics', period);
      const response = await api.get('/analytics/traffic', {
        params: { period }
      });
      console.log('✅ API: Traffic analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch traffic analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch traffic analytics'
      );
    }
  },

  // Export analytics report
  async exportReport(type, period = '30d', format = 'csv') {
    try {
      console.log('📤 API: Exporting analytics report', { type, period, format });
      const response = await api.get('/analytics/export', {
        params: { type, period, format },
        responseType: 'blob'
      });
      console.log('✅ API: Analytics report exported successfully');
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to export analytics report', error);
      throw new Error(
        error.response?.data?.error || 'Failed to export report'
      );
    }
  },

  // Get real-time metrics
  async getRealTimeMetrics() {
    try {
      console.log('⚡ API: Fetching real-time metrics');
      const response = await api.get('/analytics/realtime');
      console.log('✅ API: Real-time metrics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch real-time metrics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch real-time metrics'
      );
    }
  },

  // Get conversion funnel
  async getConversionFunnel(period = '30d') {
    try {
      console.log('🔄 API: Fetching conversion funnel', period);
      const response = await api.get('/analytics/funnel', {
        params: { period }
      });
      console.log('✅ API: Conversion funnel fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch conversion funnel', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch conversion funnel'
      );
    }
  },

  // Get geographic analytics
  async getGeographicAnalytics(period = '30d') {
    try {
      console.log('🗺️ API: Fetching geographic analytics', period);
      const response = await api.get('/analytics/geographic', {
        params: { period }
      });
      console.log('✅ API: Geographic analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch geographic analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch geographic analytics'
      );
    }
  }
};
