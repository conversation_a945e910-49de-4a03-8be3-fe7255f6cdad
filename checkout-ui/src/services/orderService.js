import { api } from './authService';

export const orderService = {
  // Get all orders for the seller
  async getOrders(page = 1, limit = 10, filters = {}) {
    try {
      console.log('📋 API: Fetching orders', { page, limit, filters });
      const response = await api.get('/orders', {
        params: { page, limit, ...filters }
      });
      console.log('✅ API: Orders fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch orders', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch orders'
      );
    }
  },

  // Get single order by ID
  async getOrder(id) {
    try {
      console.log('📋 API: Fetching order', id);
      const response = await api.get(`/orders/${id}`);
      console.log('✅ API: Order fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch order', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch order'
      );
    }
  },

  // Update order status
  async updateOrderStatus(id, status, notes = '') {
    try {
      console.log('📋 API: Updating order status', { id, status, notes });
      const response = await api.patch(`/orders/${id}/status`, {
        status,
        notes
      });
      console.log('✅ API: Order status updated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to update order status', error);
      throw new Error(
        error.response?.data?.error || 'Failed to update order status'
      );
    }
  },

  // Get order analytics
  async getOrderAnalytics(period = '30d') {
    try {
      console.log('📊 API: Fetching order analytics', period);
      const response = await api.get('/orders/analytics', {
        params: { period }
      });
      console.log('✅ API: Order analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch order analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch order analytics'
      );
    }
  },

  // Export orders
  async exportOrders(filters = {}, format = 'csv') {
    try {
      console.log('📤 API: Exporting orders', { filters, format });
      const response = await api.get('/orders/export', {
        params: { ...filters, format },
        responseType: 'blob'
      });
      console.log('✅ API: Orders exported successfully');
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to export orders', error);
      throw new Error(
        error.response?.data?.error || 'Failed to export orders'
      );
    }
  },

  // Bulk update order status
  async bulkUpdateStatus(orderIds, status, notes = '') {
    try {
      console.log('📋 API: Bulk updating order status', { orderIds, status, notes });
      const response = await api.patch('/orders/bulk-status', {
        orderIds,
        status,
        notes
      });
      console.log('✅ API: Orders status bulk updated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to bulk update order status', error);
      throw new Error(
        error.response?.data?.error || 'Failed to bulk update order status'
      );
    }
  },

  // Send order notification
  async sendNotification(orderId, type, message) {
    try {
      console.log('📧 API: Sending order notification', { orderId, type, message });
      const response = await api.post(`/orders/${orderId}/notify`, {
        type,
        message
      });
      console.log('✅ API: Order notification sent successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to send order notification', error);
      throw new Error(
        error.response?.data?.error || 'Failed to send notification'
      );
    }
  },

  // Generate invoice
  async generateInvoice(orderId) {
    try {
      console.log('🧾 API: Generating invoice for order', orderId);
      const response = await api.post(`/orders/${orderId}/invoice`, {}, {
        responseType: 'blob'
      });
      console.log('✅ API: Invoice generated successfully');
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to generate invoice', error);
      throw new Error(
        error.response?.data?.error || 'Failed to generate invoice'
      );
    }
  },

  // Get order timeline
  async getOrderTimeline(orderId) {
    try {
      console.log('⏰ API: Fetching order timeline', orderId);
      const response = await api.get(`/orders/${orderId}/timeline`);
      console.log('✅ API: Order timeline fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch order timeline', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch order timeline'
      );
    }
  },

  // Add order note
  async addOrderNote(orderId, note, isInternal = false) {
    try {
      console.log('📝 API: Adding order note', { orderId, note, isInternal });
      const response = await api.post(`/orders/${orderId}/notes`, {
        note,
        isInternal
      });
      console.log('✅ API: Order note added successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to add order note', error);
      throw new Error(
        error.response?.data?.error || 'Failed to add order note'
      );
    }
  },

  // Process refund
  async processRefund(orderId, amount, reason) {
    try {
      console.log('💰 API: Processing refund', { orderId, amount, reason });
      const response = await api.post(`/orders/${orderId}/refund`, {
        amount,
        reason
      });
      console.log('✅ API: Refund processed successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to process refund', error);
      throw new Error(
        error.response?.data?.error || 'Failed to process refund'
      );
    }
  }
};
