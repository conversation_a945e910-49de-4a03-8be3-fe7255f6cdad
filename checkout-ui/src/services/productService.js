import api from './authService';

export const productService = {
  // Get all products for the seller
  async getProducts(page = 1, limit = 10, search = '') {
    try {
      console.log('📦 API: Fetching products', { page, limit, search });
      const response = await api.get('/products', {
        params: { page, limit, search }
      });
      console.log('✅ API: Products fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch products', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch products'
      );
    }
  },

  // Get single product by ID
  async getProduct(id) {
    try {
      console.log('📦 API: Fetching product', id);
      const response = await api.get(`/products/${id}`);
      console.log('✅ API: Product fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch product', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch product'
      );
    }
  },

  // Create new product
  async createProduct(productData) {
    try {
      console.log('📦 API: Creating product', productData);
      const response = await api.post('/products', productData);
      console.log('✅ API: Product created successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to create product', error);
      throw new Error(
        error.response?.data?.error || 'Failed to create product'
      );
    }
  },

  // Update product
  async updateProduct(id, productData) {
    try {
      console.log('📦 API: Updating product', id, productData);
      const response = await api.put(`/products/${id}`, productData);
      console.log('✅ API: Product updated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to update product', error);
      throw new Error(
        error.response?.data?.error || 'Failed to update product'
      );
    }
  },

  // Delete product
  async deleteProduct(id) {
    try {
      console.log('📦 API: Deleting product', id);
      const response = await api.delete(`/products/${id}`);
      console.log('✅ API: Product deleted successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to delete product', error);
      throw new Error(
        error.response?.data?.error || 'Failed to delete product'
      );
    }
  },

  // Upload product image
  async uploadImage(file) {
    try {
      console.log('📷 API: Uploading image', file.name);
      const formData = new FormData();
      formData.append('image', file);
      
      const response = await api.post('/products/upload-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      console.log('✅ API: Image uploaded successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to upload image', error);
      throw new Error(
        error.response?.data?.error || 'Failed to upload image'
      );
    }
  },

  // Generate checkout link for product
  async generateCheckoutLink(id) {
    try {
      console.log('🔗 API: Generating checkout link for product', id);
      const response = await api.post(`/products/${id}/checkout-link`);
      console.log('✅ API: Checkout link generated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to generate checkout link', error);
      throw new Error(
        error.response?.data?.error || 'Failed to generate checkout link'
      );
    }
  },

  // Toggle product status (active/inactive)
  async toggleStatus(id) {
    try {
      console.log('🔄 API: Toggling product status', id);
      const response = await api.patch(`/products/${id}/toggle-status`);
      console.log('✅ API: Product status toggled successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to toggle product status', error);
      throw new Error(
        error.response?.data?.error || 'Failed to toggle product status'
      );
    }
  },

  // Bulk operations
  async bulkDelete(productIds) {
    try {
      console.log('🗑️ API: Bulk deleting products', productIds);
      const response = await api.delete('/products/bulk', {
        data: { productIds }
      });
      console.log('✅ API: Products bulk deleted successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to bulk delete products', error);
      throw new Error(
        error.response?.data?.error || 'Failed to bulk delete products'
      );
    }
  },

  async bulkUpdateStatus(productIds, status) {
    try {
      console.log('🔄 API: Bulk updating product status', productIds, status);
      const response = await api.patch('/products/bulk-status', {
        productIds,
        status
      });
      console.log('✅ API: Products status bulk updated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to bulk update product status', error);
      throw new Error(
        error.response?.data?.error || 'Failed to bulk update product status'
      );
    }
  }
};
