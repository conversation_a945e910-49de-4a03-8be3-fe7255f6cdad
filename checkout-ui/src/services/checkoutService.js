import api from './authService';

export const checkoutService = {
  // Generate checkout link for product
  async generateCheckoutLink(productId, options = {}) {
    try {
      console.log('🔗 API: Generating checkout link', { productId, options });
      const response = await api.post(`/checkout/generate`, {
        productId,
        ...options
      });
      console.log('✅ API: Checkout link generated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to generate checkout link', error);
      throw new Error(
        error.response?.data?.error || 'Failed to generate checkout link'
      );
    }
  },

  // Get all checkout links
  async getCheckoutLinks(page = 1, limit = 10, filters = {}) {
    try {
      console.log('🔗 API: Fetching checkout links', { page, limit, filters });
      const response = await api.get('/checkout/links', {
        params: { page, limit, ...filters }
      });
      console.log('✅ API: Checkout links fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch checkout links', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch checkout links'
      );
    }
  },

  // Get single checkout link details
  async getCheckoutLink(linkId) {
    try {
      console.log('🔗 API: Fetching checkout link', linkId);
      const response = await api.get(`/checkout/links/${linkId}`);
      console.log('✅ API: Checkout link fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch checkout link', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch checkout link'
      );
    }
  },

  // Update checkout link
  async updateCheckoutLink(linkId, updates) {
    try {
      console.log('🔗 API: Updating checkout link', { linkId, updates });
      const response = await api.put(`/checkout/links/${linkId}`, updates);
      console.log('✅ API: Checkout link updated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to update checkout link', error);
      throw new Error(
        error.response?.data?.error || 'Failed to update checkout link'
      );
    }
  },

  // Delete checkout link
  async deleteCheckoutLink(linkId) {
    try {
      console.log('🔗 API: Deleting checkout link', linkId);
      const response = await api.delete(`/checkout/links/${linkId}`);
      console.log('✅ API: Checkout link deleted successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to delete checkout link', error);
      throw new Error(
        error.response?.data?.error || 'Failed to delete checkout link'
      );
    }
  },

  // Generate QR code for checkout link
  async generateQRCode(linkId, options = {}) {
    try {
      console.log('📱 API: Generating QR code', { linkId, options });
      const response = await api.post(`/checkout/links/${linkId}/qr`, options, {
        responseType: 'blob'
      });
      console.log('✅ API: QR code generated successfully');
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to generate QR code', error);
      throw new Error(
        error.response?.data?.error || 'Failed to generate QR code'
      );
    }
  },

  // Get checkout link analytics
  async getCheckoutAnalytics(linkId, period = '30d') {
    try {
      console.log('📊 API: Fetching checkout analytics', { linkId, period });
      const response = await api.get(`/checkout/links/${linkId}/analytics`, {
        params: { period }
      });
      console.log('✅ API: Checkout analytics fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch checkout analytics', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch checkout analytics'
      );
    }
  },

  // Toggle checkout link status
  async toggleLinkStatus(linkId) {
    try {
      console.log('🔄 API: Toggling checkout link status', linkId);
      const response = await api.patch(`/checkout/links/${linkId}/toggle`);
      console.log('✅ API: Checkout link status toggled successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to toggle checkout link status', error);
      throw new Error(
        error.response?.data?.error || 'Failed to toggle link status'
      );
    }
  },

  // Bulk operations
  async bulkUpdateLinks(linkIds, updates) {
    try {
      console.log('🔗 API: Bulk updating checkout links', { linkIds, updates });
      const response = await api.patch('/checkout/links/bulk', {
        linkIds,
        updates
      });
      console.log('✅ API: Checkout links bulk updated successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to bulk update checkout links', error);
      throw new Error(
        error.response?.data?.error || 'Failed to bulk update links'
      );
    }
  },

  async bulkDeleteLinks(linkIds) {
    try {
      console.log('🗑️ API: Bulk deleting checkout links', linkIds);
      const response = await api.delete('/checkout/links/bulk', {
        data: { linkIds }
      });
      console.log('✅ API: Checkout links bulk deleted successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to bulk delete checkout links', error);
      throw new Error(
        error.response?.data?.error || 'Failed to bulk delete links'
      );
    }
  },

  // Share checkout link
  async shareCheckoutLink(linkId, method, recipient) {
    try {
      console.log('📤 API: Sharing checkout link', { linkId, method, recipient });
      const response = await api.post(`/checkout/links/${linkId}/share`, {
        method,
        recipient
      });
      console.log('✅ API: Checkout link shared successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to share checkout link', error);
      throw new Error(
        error.response?.data?.error || 'Failed to share checkout link'
      );
    }
  },

  // Get checkout link performance
  async getLinkPerformance(period = '30d') {
    try {
      console.log('📈 API: Fetching link performance', period);
      const response = await api.get('/checkout/performance', {
        params: { period }
      });
      console.log('✅ API: Link performance fetched successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Failed to fetch link performance', error);
      throw new Error(
        error.response?.data?.error || 'Failed to fetch link performance'
      );
    }
  }
};
