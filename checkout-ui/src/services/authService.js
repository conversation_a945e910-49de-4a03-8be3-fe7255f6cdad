import axios from 'axios';

// Base URL for API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 second timeout
});

// Add token to requests if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authService = {
  // Health check
  async healthCheck() {
    try {
      console.log('🏥 API: Health check to', `${API_BASE_URL}/health`);
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
      const data = await response.text();
      console.log('✅ API: Health check response:', data);
      return data;
    } catch (error) {
      console.error('❌ API: Health check failed:', error);
      throw error;
    }
  },

  // Check user type by phone number
  async checkUserType(phone) {
    try {
      console.log('🔍 API: Checking user type for', phone);
      const response = await api.post('/auth/check-user-type', { phone });
      console.log('✅ API: User type check response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: User type check error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to check user type'
      );
    }
  },

  // Request OTP
  async requestOTP(phone) {
    try {
      console.log('🌐 API: Sending OTP request to', `${API_BASE_URL}/auth/request-otp`);
      console.log('📱 API: Phone number:', phone);

      const response = await api.post('/auth/request-otp', { phone });
      console.log('✅ API: OTP request response:', response.data);

      return response.data;
    } catch (error) {
      console.error('❌ API: OTP request error:', error);
      console.error('❌ API: Error response:', error.response?.data);
      console.error('❌ API: Error status:', error.response?.status);

      throw new Error(
        error.response?.data?.error || 'Failed to send OTP'
      );
    }
  },

  // Verify OTP
  async verifyOTP(phone, otp, name) {
    try {
      console.log('🌐 API: Sending OTP verification to', `${API_BASE_URL}/auth/verify-otp`);
      console.log('📱 API: Phone:', phone);
      console.log('🔢 API: OTP:', otp);
      console.log('👤 API: Name:', name);

      const payload = { phone, otp, name };
      console.log('📦 API: Request payload:', payload);

      const response = await api.post('/auth/verify-otp', payload);
      console.log('✅ API: OTP verification response:', response.data);

      return response.data;
    } catch (error) {
      console.error('❌ API: OTP verification error:', error);
      console.error('❌ API: Error response:', error.response?.data);
      console.error('❌ API: Error status:', error.response?.status);

      throw new Error(
        error.response?.data?.error || 'Invalid OTP'
      );
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      const response = await api.get('/auth/me');
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || 'Failed to get user data'
      );
    }
  },

  // Update user profile
  async updateProfile(userData) {
    try {
      const response = await api.put('/auth/profile', userData);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || 'Failed to update profile'
      );
    }
  },

  // Logout
  async logout() {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // Ignore logout errors
      console.warn('Logout error:', error);
    }
  },
};

export default api;
