import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('userType');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const buyerService = {
  // Profile Management
  async getProfile() {
    try {
      console.log('🔍 API: Getting buyer profile');
      const response = await api.get('/buyer/profile');
      console.log('✅ API: Profile response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Get profile error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to get profile'
      );
    }
  },

  async updateProfile(profileData) {
    try {
      console.log('🔄 API: Updating buyer profile:', profileData);
      const response = await api.put('/buyer/profile', profileData);
      console.log('✅ API: Profile update response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Update profile error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to update profile'
      );
    }
  },

  // Address Management
  async getAddresses() {
    try {
      console.log('🔍 API: Getting buyer addresses');
      const response = await api.get('/buyer/addresses');
      console.log('✅ API: Addresses response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Get addresses error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to get addresses'
      );
    }
  },

  async addAddress(addressData) {
    try {
      console.log('➕ API: Adding new address:', addressData);
      const response = await api.post('/buyer/addresses', addressData);
      console.log('✅ API: Add address response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Add address error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to add address'
      );
    }
  },

  async updateAddress(addressId, addressData) {
    try {
      console.log('🔄 API: Updating address:', addressId, addressData);
      const response = await api.put(`/buyer/addresses/${addressId}`, addressData);
      console.log('✅ API: Update address response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Update address error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to update address'
      );
    }
  },

  async deleteAddress(addressId) {
    try {
      console.log('🗑️ API: Deleting address:', addressId);
      const response = await api.delete(`/buyer/addresses/${addressId}`);
      console.log('✅ API: Delete address response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Delete address error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to delete address'
      );
    }
  },

  async setDefaultAddress(addressId) {
    try {
      console.log('⭐ API: Setting default address:', addressId);
      const response = await api.patch(`/buyer/addresses/${addressId}/default`);
      console.log('✅ API: Set default address response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Set default address error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to set default address'
      );
    }
  },

  // Order Management
  async getOrders() {
    try {
      console.log('🔍 API: Getting buyer orders');
      const response = await api.get('/buyer/orders');
      console.log('✅ API: Orders response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Get orders error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to get orders'
      );
    }
  },

  async getOrder(orderId) {
    try {
      console.log('🔍 API: Getting order details:', orderId);
      const response = await api.get(`/buyer/orders/${orderId}`);
      console.log('✅ API: Order details response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Get order error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to get order details'
      );
    }
  },

  // Preferences Management
  async updatePreferences(preferences) {
    try {
      console.log('🔄 API: Updating buyer preferences:', preferences);
      const response = await api.put('/buyer/preferences', preferences);
      console.log('✅ API: Preferences update response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Update preferences error:', error);
      throw new Error(
        error.response?.data?.error || 'Failed to update preferences'
      );
    }
  },

  // Utility methods
  async healthCheck() {
    try {
      console.log('🏥 API: Health check to buyer service');
      const response = await api.get('/buyer/health');
      console.log('✅ API: Health check response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API: Health check failed:', error);
      throw error;
    }
  }
};

export default buyerService;
