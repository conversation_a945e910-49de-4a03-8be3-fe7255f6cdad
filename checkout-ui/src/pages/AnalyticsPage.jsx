import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Skeleton,
  Alert,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  ShoppingCart,
  People,
  Visibility,
  Download,
  Refresh,
  DateRange,
  <PERSON>Chart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
// import { LineChart, Line, AreaChart, Area, BarChart as RechartsBarChart, Bar, PieChart as RechartsPie<PERSON>hart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Responsive<PERSON>ontainer, <PERSON> } from 'recharts';
import { format, subDays, startOfDay } from 'date-fns';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import { analyticsService } from '../services/analyticsService';

// Chart colors
const CHART_COLORS = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];

const AnalyticsPage = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [activeTab, setActiveTab] = useState(0);
  const [chartType, setChartType] = useState('line');

  // Mock data for testing
  const mockOverview = {
    totalRevenue: 125000,
    revenueGrowth: 12.5,
    totalOrders: 1234,
    ordersGrowth: 8.2,
    totalCustomers: 456,
    customersGrowth: 15.3,
    conversionRate: 3.2,
    conversionGrowth: -0.5,
    averageOrderValue: 2850,
    aovGrowth: 5.8,
  };

  const mockSalesData = Array.from({ length: 30 }, (_, i) => {
    const date = subDays(new Date(), 29 - i);
    return {
      date: format(date, 'MMM dd'),
      revenue: Math.floor(Math.random() * 5000) + 2000,
      orders: Math.floor(Math.random() * 20) + 10,
      customers: Math.floor(Math.random() * 15) + 5,
    };
  });

  const mockProductPerformance = [
    { name: 'Premium T-Shirt', revenue: 45000, orders: 180, growth: 12.5 },
    { name: 'Wireless Headphones', revenue: 38000, orders: 95, growth: 8.3 },
    { name: 'Smart Watch', revenue: 32000, orders: 40, growth: -2.1 },
    { name: 'Running Shoes', revenue: 28000, orders: 85, growth: 15.7 },
    { name: 'Laptop Stand', revenue: 22000, orders: 110, growth: 6.9 },
  ];

  const mockOrderStatusData = [
    { name: 'Delivered', value: 65, color: '#22c55e' },
    { name: 'Shipped', value: 20, color: '#3b82f6' },
    { name: 'Processing', value: 10, color: '#f59e0b' },
    { name: 'Pending', value: 5, color: '#ef4444' },
  ];

  const mockTrafficSources = [
    { source: 'Direct', visitors: 2450, percentage: 45 },
    { source: 'Social Media', visitors: 1680, percentage: 31 },
    { source: 'Search Engine', visitors: 890, percentage: 16 },
    { source: 'Email', visitors: 435, percentage: 8 },
  ];

  // Fetch analytics data
  const {
    data: overviewData,
    isLoading: overviewLoading,
    error: overviewError,
  } = useQuery({
    queryKey: ['analytics-overview', selectedPeriod],
    queryFn: () => analyticsService.getDashboardOverview(selectedPeriod),
    initialData: mockOverview,
  });

  const {
    data: salesData,
    isLoading: salesLoading,
  } = useQuery({
    queryKey: ['analytics-sales', selectedPeriod],
    queryFn: () => analyticsService.getSalesAnalytics(selectedPeriod),
    initialData: mockSalesData,
  });

  const {
    data: productData,
    isLoading: productLoading,
  } = useQuery({
    queryKey: ['analytics-products', selectedPeriod],
    queryFn: () => analyticsService.getProductPerformance(selectedPeriod),
    initialData: mockProductPerformance,
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(1)}%`;
  };

  const getGrowthColor = (growth) => {
    return growth >= 0 ? 'success.main' : 'error.main';
  };

  const getGrowthIcon = (growth) => {
    return growth >= 0 ? TrendingUp : TrendingDown;
  };

  const MetricCard = ({ title, value, growth, icon: Icon, format = 'number' }) => (
    <Card sx={{ borderRadius: 2, height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {title}
          </Typography>
          <Avatar sx={{ backgroundColor: 'primary.50', color: 'primary.main', width: 40, height: 40 }}>
            <Icon fontSize="small" />
          </Avatar>
        </Box>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
          {format === 'currency' ? formatCurrency(value) :
           format === 'percentage' ? `${value}%` :
           value.toLocaleString()}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {React.createElement(getGrowthIcon(growth), {
            fontSize: 'small',
            sx: { color: getGrowthColor(growth) }
          })}
          <Typography
            variant="body2"
            sx={{ color: getGrowthColor(growth), fontWeight: 600 }}
          >
            {formatPercentage(growth)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            vs last period
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <DashboardLayout>
      <Box>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                Analytics & Reports
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Track your business performance and insights
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Period</InputLabel>
                <Select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  label="Period"
                >
                  <MenuItem value="7d">Last 7 days</MenuItem>
                  <MenuItem value="30d">Last 30 days</MenuItem>
                  <MenuItem value="90d">Last 90 days</MenuItem>
                  <MenuItem value="1y">Last year</MenuItem>
                </Select>
              </FormControl>
              <Button
                variant="outlined"
                startIcon={<Download />}
                sx={{ borderRadius: 2 }}
              >
                Export
              </Button>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                sx={{ borderRadius: 2 }}
              >
                Refresh
              </Button>
            </Box>
          </Box>
        </motion.div>

        {/* Error State */}
        {overviewError && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {overviewError.message}
          </Alert>
        )}

        {/* Key Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} lg={2.4}>
              {overviewLoading ? (
                <Skeleton variant="rectangular" height={140} sx={{ borderRadius: 2 }} />
              ) : (
                <MetricCard
                  title="Total Revenue"
                  value={overviewData?.totalRevenue || 0}
                  growth={overviewData?.revenueGrowth || 0}
                  icon={AttachMoney}
                  format="currency"
                />
              )}
            </Grid>
            <Grid item xs={12} sm={6} lg={2.4}>
              {overviewLoading ? (
                <Skeleton variant="rectangular" height={140} sx={{ borderRadius: 2 }} />
              ) : (
                <MetricCard
                  title="Total Orders"
                  value={overviewData?.totalOrders || 0}
                  growth={overviewData?.ordersGrowth || 0}
                  icon={ShoppingCart}
                />
              )}
            </Grid>
            <Grid item xs={12} sm={6} lg={2.4}>
              {overviewLoading ? (
                <Skeleton variant="rectangular" height={140} sx={{ borderRadius: 2 }} />
              ) : (
                <MetricCard
                  title="Total Customers"
                  value={overviewData?.totalCustomers || 0}
                  growth={overviewData?.customersGrowth || 0}
                  icon={People}
                />
              )}
            </Grid>
            <Grid item xs={12} sm={6} lg={2.4}>
              {overviewLoading ? (
                <Skeleton variant="rectangular" height={140} sx={{ borderRadius: 2 }} />
              ) : (
                <MetricCard
                  title="Conversion Rate"
                  value={overviewData?.conversionRate || 0}
                  growth={overviewData?.conversionGrowth || 0}
                  icon={TrendingUp}
                  format="percentage"
                />
              )}
            </Grid>
            <Grid item xs={12} sm={6} lg={2.4}>
              {overviewLoading ? (
                <Skeleton variant="rectangular" height={140} sx={{ borderRadius: 2 }} />
              ) : (
                <MetricCard
                  title="Avg Order Value"
                  value={overviewData?.averageOrderValue || 0}
                  growth={overviewData?.aovGrowth || 0}
                  icon={AttachMoney}
                  format="currency"
                />
              )}
            </Grid>
          </Grid>
        </motion.div>

        {/* Charts Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {/* Sales Trend Chart */}
            <Grid item xs={12} lg={8}>
              <Card sx={{ borderRadius: 2, height: 400 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Sales Trend
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Line Chart">
                        <IconButton
                          size="small"
                          onClick={() => setChartType('line')}
                          sx={{ color: chartType === 'line' ? 'primary.main' : 'text.secondary' }}
                        >
                          <ShowChart />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Area Chart">
                        <IconButton
                          size="small"
                          onClick={() => setChartType('area')}
                          sx={{ color: chartType === 'area' ? 'primary.main' : 'text.secondary' }}
                        >
                          <BarChart />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  {salesLoading ? (
                    <Skeleton variant="rectangular" height={300} />
                  ) : (
                    <Box
                      sx={{
                        height: 300,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'grey.50',
                        borderRadius: 2,
                        border: '2px dashed',
                        borderColor: 'grey.300',
                      }}
                    >
                      <Box sx={{ textAlign: 'center' }}>
                        <BarChart sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          Sales Trend Chart
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Interactive charts will be displayed here
                        </Typography>
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Order Status Distribution */}
            <Grid item xs={12} lg={4}>
              <Card sx={{ borderRadius: 2, height: 400 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                    Order Status Distribution
                  </Typography>
                  <ResponsiveContainer width="100%" height={250}>
                    <RechartsPieChart>
                      <Pie
                        data={mockOrderStatusData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {mockOrderStatusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                  <Box sx={{ mt: 2 }}>
                    {mockOrderStatusData.map((item, index) => (
                      <Box key={index} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              backgroundColor: item.color,
                            }}
                          />
                          <Typography variant="body2">{item.name}</Typography>
                        </Box>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {item.value}%
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>

        {/* Product Performance & Traffic Analytics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Grid container spacing={3}>
            {/* Top Products */}
            <Grid item xs={12} lg={8}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                    Top Performing Products
                  </Typography>
                  {productLoading ? (
                    [...Array(5)].map((_, index) => (
                      <Skeleton key={index} variant="rectangular" height={60} sx={{ mb: 2, borderRadius: 1 }} />
                    ))
                  ) : (
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Product</TableCell>
                            <TableCell align="right">Revenue</TableCell>
                            <TableCell align="right">Orders</TableCell>
                            <TableCell align="right">Growth</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {productData?.map((product, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                  <Avatar sx={{ width: 32, height: 32 }}>
                                    {product.name.charAt(0)}
                                  </Avatar>
                                  <Typography variant="subtitle2">
                                    {product.name}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell align="right">
                                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                                  {formatCurrency(product.revenue)}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                <Typography variant="body2">
                                  {product.orders}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 0.5 }}>
                                  {React.createElement(getGrowthIcon(product.growth), {
                                    fontSize: 'small',
                                    sx: { color: getGrowthColor(product.growth) }
                                  })}
                                  <Typography
                                    variant="body2"
                                    sx={{ color: getGrowthColor(product.growth), fontWeight: 600 }}
                                  >
                                    {formatPercentage(product.growth)}
                                  </Typography>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Traffic Sources */}
            <Grid item xs={12} lg={4}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                    Traffic Sources
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {mockTrafficSources.map((source, index) => (
                      <Box key={index}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {source.source}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {source.visitors.toLocaleString()} ({source.percentage}%)
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={source.percentage}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: 'grey.200',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: CHART_COLORS[index % CHART_COLORS.length],
                              borderRadius: 4,
                            },
                          }}
                        />
                      </Box>
                    ))}
                  </Box>

                  {/* Real-time Metrics */}
                  <Box sx={{ mt: 4, p: 2, backgroundColor: 'primary.50', borderRadius: 2 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                      Real-time Activity
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Active visitors
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        24
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Page views (today)
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        1,247
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">
                        Bounce rate
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        32.5%
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>
      </Box>
    </DashboardLayout>
  );
};

export default AnalyticsPage;
