import React, { useState } from 'react';
import {
  Box,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  Alert,
  Skeleton,
  Pagination,
  Switch,
  FormControlLabel,
  Fab,
} from '@mui/material';
import {
  Search,
  Add,
  MoreVert,
  Link as LinkIcon,
  QrCode,
  Share,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  ContentCopy,
  Download,
  Refresh,
  TrendingUp,
  TrendingDown,
  OpenInNew,
  WhatsApp,
  Email,
  Telegram,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import { checkoutService } from '../services/checkoutService';

const CheckoutLinksPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedLinks, setSelectedLinks] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedLink, setSelectedLink] = useState(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showQRDialog, setShowQRDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showAnalyticsDialog, setShowAnalyticsDialog] = useState(false);
  const [page, setPage] = useState(1);

  const queryClient = useQueryClient();

  // Mock data for testing
  const mockLinks = [
    {
      id: 'link-001',
      productName: 'Premium T-Shirt',
      productId: 'prod-001',
      url: 'https://swiftcheckout.com/buy/abc123',
      shortCode: 'abc123',
      isActive: true,
      clicks: 245,
      conversions: 12,
      revenue: 15588,
      createdAt: new Date('2024-01-10T10:30:00'),
      lastUsed: new Date('2024-01-15T14:20:00'),
    },
    {
      id: 'link-002',
      productName: 'Wireless Headphones',
      productId: 'prod-002',
      url: 'https://swiftcheckout.com/buy/def456',
      shortCode: 'def456',
      isActive: true,
      clicks: 189,
      conversions: 8,
      revenue: 39992,
      createdAt: new Date('2024-01-12T15:45:00'),
      lastUsed: new Date('2024-01-15T11:30:00'),
    },
    {
      id: 'link-003',
      productName: 'Smart Watch',
      productId: 'prod-003',
      url: 'https://swiftcheckout.com/buy/ghi789',
      shortCode: 'ghi789',
      isActive: false,
      clicks: 67,
      conversions: 2,
      revenue: 17998,
      createdAt: new Date('2024-01-08T09:15:00'),
      lastUsed: new Date('2024-01-14T16:45:00'),
    },
  ];

  // Fetch checkout links
  const {
    data: linksData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['checkout-links', page, searchTerm, statusFilter],
    queryFn: () => checkoutService.getCheckoutLinks(page, 10, {
      search: searchTerm,
      status: statusFilter !== 'all' ? statusFilter : undefined,
    }),
    keepPreviousData: true,
    initialData: {
      links: mockLinks.filter(link => {
        const matchesSearch = !searchTerm || 
          link.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          link.shortCode.toLowerCase().includes(searchTerm.toLowerCase());
        
        const matchesStatus = statusFilter === 'all' || 
          (statusFilter === 'active' && link.isActive) ||
          (statusFilter === 'inactive' && !link.isActive);
        
        return matchesSearch && matchesStatus;
      }),
      total: mockLinks.length,
      page: 1,
      totalPages: 1,
    },
  });

  // Toggle link status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: checkoutService.toggleLinkStatus,
    onSuccess: () => {
      queryClient.invalidateQueries(['checkout-links']);
    },
  });

  // Delete link mutation
  const deleteLinkMutation = useMutation({
    mutationFn: checkoutService.deleteCheckoutLink,
    onSuccess: () => {
      queryClient.invalidateQueries(['checkout-links']);
      setSelectedLink(null);
    },
  });

  const links = linksData?.links || [];
  const totalLinks = linksData?.total || 0;

  // Calculate summary stats
  const summaryStats = {
    totalLinks: links.length,
    activeLinks: links.filter(l => l.isActive).length,
    totalClicks: links.reduce((sum, link) => sum + link.clicks, 0),
    totalRevenue: links.reduce((sum, link) => sum + link.revenue, 0),
    conversionRate: links.length > 0 
      ? ((links.reduce((sum, link) => sum + link.conversions, 0) / links.reduce((sum, link) => sum + link.clicks, 0)) * 100).toFixed(1)
      : 0,
  };

  const handleMenuOpen = (event, link) => {
    setAnchorEl(event.currentTarget);
    setSelectedLink(link);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedLink(null);
  };

  const handleCopyLink = async (url) => {
    try {
      await navigator.clipboard.writeText(url);
      // Show success message
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleToggleStatus = () => {
    if (selectedLink) {
      toggleStatusMutation.mutate(selectedLink.id);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedLink) {
      deleteLinkMutation.mutate(selectedLink.id);
    }
    handleMenuClose();
  };

  const handleShowQR = () => {
    setShowQRDialog(true);
    handleMenuClose();
  };

  const handleShowShare = () => {
    setShowShareDialog(true);
    handleMenuClose();
  };

  const handleShowAnalytics = () => {
    setShowAnalyticsDialog(true);
    handleMenuClose();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusChip = (isActive) => (
    <Chip
      label={isActive ? 'Active' : 'Inactive'}
      color={isActive ? 'success' : 'default'}
      size="small"
      variant="outlined"
    />
  );

  return (
    <DashboardLayout>
      <Box>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                Checkout Links
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage and track your product checkout links
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowCreateDialog(true)}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: 3,
                py: 1.5,
              }}
            >
              Create Link
            </Button>
          </Box>
        </motion.div>

        {/* Summary Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                    {summaryStats.totalLinks}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Links
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                    {summaryStats.activeLinks}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Links
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                    {summaryStats.totalClicks.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Clicks
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                    {summaryStats.conversionRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Conversion Rate
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                    {formatCurrency(summaryStats.totalRevenue)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card sx={{ mb: 3, borderRadius: 2 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <TextField
                    placeholder="Search links, products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      label="Status"
                    >
                      <MenuItem value="all">All Status</MenuItem>
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={() => refetch()}
                    fullWidth
                    sx={{ borderRadius: 2, height: 56 }}
                  >
                    Refresh
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error.message}
          </Alert>
        )}

        {/* Links Table */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card sx={{ borderRadius: 2 }}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Product</TableCell>
                    <TableCell>Link</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Clicks</TableCell>
                    <TableCell>Conversions</TableCell>
                    <TableCell>Revenue</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading ? (
                    [...Array(5)].map((_, index) => (
                      <TableRow key={index}>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                      </TableRow>
                    ))
                  ) : links.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center" sx={{ py: 8 }}>
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          No checkout links found
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                          {searchTerm || statusFilter !== 'all'
                            ? 'Try adjusting your search or filters'
                            : 'Create your first checkout link to start selling'}
                        </Typography>
                        <Button
                          variant="contained"
                          startIcon={<Add />}
                          onClick={() => setShowCreateDialog(true)}
                          sx={{ borderRadius: 2 }}
                        >
                          Create Link
                        </Button>
                      </TableCell>
                    </TableRow>
                  ) : (
                    links.map((link, index) => (
                      <motion.tr
                        key={link.id}
                        component={TableRow}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'action.hover',
                          },
                        }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ width: 32, height: 32 }}>
                              {link.productName.charAt(0)}
                            </Avatar>
                            <Typography variant="subtitle2">
                              {link.productName}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              /{link.shortCode}
                            </Typography>
                            <Tooltip title="Copy Link">
                              <IconButton
                                size="small"
                                onClick={() => handleCopyLink(link.url)}
                              >
                                <ContentCopy fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Open Link">
                              <IconButton
                                size="small"
                                onClick={() => window.open(link.url, '_blank')}
                              >
                                <OpenInNew fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                        <TableCell>
                          {getStatusChip(link.isActive)}
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {link.clicks.toLocaleString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {link.conversions}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              ({((link.conversions / link.clicks) * 100).toFixed(1)}%)
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {formatCurrency(link.revenue)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {format(new Date(link.createdAt), 'MMM dd, yyyy')}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            onClick={(e) => handleMenuOpen(e, link)}
                            size="small"
                          >
                            <MoreVert />
                          </IconButton>
                        </TableCell>
                      </motion.tr>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Pagination */}
            {links.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <Pagination
                  count={Math.ceil(totalLinks / 10)}
                  page={page}
                  onChange={(e, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            )}
          </Card>
        </motion.div>

        {/* Floating Add Button */}
        <Fab
          color="primary"
          onClick={() => setShowCreateDialog(true)}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1000,
          }}
        >
          <Add />
        </Fab>

        {/* Link Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handleShowAnalytics}>
            <TrendingUp sx={{ mr: 1 }} fontSize="small" />
            View Analytics
          </MenuItem>
          <MenuItem onClick={handleShowQR}>
            <QrCode sx={{ mr: 1 }} fontSize="small" />
            Generate QR Code
          </MenuItem>
          <MenuItem onClick={handleShowShare}>
            <Share sx={{ mr: 1 }} fontSize="small" />
            Share Link
          </MenuItem>
          <MenuItem onClick={() => handleCopyLink(selectedLink?.url)}>
            <ContentCopy sx={{ mr: 1 }} fontSize="small" />
            Copy Link
          </MenuItem>
          <MenuItem onClick={handleToggleStatus}>
            {selectedLink?.isActive ? (
              <>
                <VisibilityOff sx={{ mr: 1 }} fontSize="small" />
                Deactivate
              </>
            ) : (
              <>
                <Visibility sx={{ mr: 1 }} fontSize="small" />
                Activate
              </>
            )}
          </MenuItem>
          <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
            <Delete sx={{ mr: 1 }} fontSize="small" />
            Delete
          </MenuItem>
        </Menu>

        {/* Create Link Dialog */}
        <Dialog
          open={showCreateDialog}
          onClose={() => setShowCreateDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Create Checkout Link</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Select Product</InputLabel>
                <Select label="Select Product">
                  <MenuItem value="prod-001">Premium T-Shirt</MenuItem>
                  <MenuItem value="prod-002">Wireless Headphones</MenuItem>
                  <MenuItem value="prod-003">Smart Watch</MenuItem>
                </Select>
              </FormControl>
              <TextField
                label="Custom Short Code (Optional)"
                fullWidth
                placeholder="e.g., tshirt-sale"
                helperText="Leave empty to auto-generate"
                sx={{ mb: 3 }}
              />
              <FormControlLabel
                control={<Switch defaultChecked />}
                label="Link is active"
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowCreateDialog(false)}>Cancel</Button>
            <Button variant="contained">Create Link</Button>
          </DialogActions>
        </Dialog>

        {/* QR Code Dialog */}
        <Dialog
          open={showQRDialog}
          onClose={() => setShowQRDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>QR Code - {selectedLink?.productName}</DialogTitle>
          <DialogContent sx={{ textAlign: 'center', py: 4 }}>
            <Box
              sx={{
                width: 250,
                height: 250,
                backgroundColor: 'grey.100',
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 3,
              }}
            >
              <QrCode sx={{ fontSize: 150, color: 'grey.400' }} />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Customers can scan this QR code to access your checkout link
            </Typography>
            <Typography variant="caption" sx={{ fontFamily: 'monospace' }}>
              {selectedLink?.url}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowQRDialog(false)}>Close</Button>
            <Button variant="contained" startIcon={<Download />}>
              Download QR
            </Button>
          </DialogActions>
        </Dialog>

        {/* Share Dialog */}
        <Dialog
          open={showShareDialog}
          onClose={() => setShowShareDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Share Checkout Link</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Share via:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<WhatsApp />}
                    sx={{ borderRadius: 2, py: 1.5 }}
                    color="success"
                  >
                    WhatsApp
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Email />}
                    sx={{ borderRadius: 2, py: 1.5 }}
                  >
                    Email
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Telegram />}
                    sx={{ borderRadius: 2, py: 1.5 }}
                    color="info"
                  >
                    Telegram
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ContentCopy />}
                    sx={{ borderRadius: 2, py: 1.5 }}
                    onClick={() => handleCopyLink(selectedLink?.url)}
                  >
                    Copy Link
                  </Button>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3, p: 2, backgroundColor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Link URL:
                </Typography>
                <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                  {selectedLink?.url}
                </Typography>
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowShareDialog(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        {/* Analytics Dialog */}
        <Dialog
          open={showAnalyticsDialog}
          onClose={() => setShowAnalyticsDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Link Analytics - {selectedLink?.productName}</DialogTitle>
          <DialogContent>
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={6} md={3}>
                <Card sx={{ textAlign: 'center', borderRadius: 2 }}>
                  <CardContent>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {selectedLink?.clicks || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Clicks
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} md={3}>
                <Card sx={{ textAlign: 'center', borderRadius: 2 }}>
                  <CardContent>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {selectedLink?.conversions || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Conversions
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} md={3}>
                <Card sx={{ textAlign: 'center', borderRadius: 2 }}>
                  <CardContent>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                      {selectedLink ? ((selectedLink.conversions / selectedLink.clicks) * 100).toFixed(1) : 0}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Conversion Rate
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} md={3}>
                <Card sx={{ textAlign: 'center', borderRadius: 2 }}>
                  <CardContent>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                      {selectedLink ? formatCurrency(selectedLink.revenue) : '₹0'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Revenue
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowAnalyticsDialog(false)}>Close</Button>
            <Button variant="contained" startIcon={<Download />}>
              Export Report
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </DashboardLayout>
  );
};

export default CheckoutLinksPage;
