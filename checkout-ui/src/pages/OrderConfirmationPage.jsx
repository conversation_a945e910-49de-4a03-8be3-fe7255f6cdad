import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Avatar,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  CheckCircle,
  Phone,
  Email,
  LocationOn,
  Receipt,
  Home,
  Share,
  FiberManualRecord,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';

// API service for order confirmation
const orderAPI = {
  async getOrder(orderId) {
    const response = await fetch(`http://localhost:3000/api/public/order/${orderId}`);
    if (!response.ok) {
      throw new Error('Order not found');
    }
    return response.json();
  },
};

const OrderConfirmationPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();

  // Fetch order data
  const {
    data: orderData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['order', orderId],
    queryFn: () => orderAPI.getOrder(orderId),
    retry: 1,
  });

  const order = orderData?.order;

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'confirmed': return 'info';
      case 'processing': return 'primary';
      case 'shipped': return 'secondary';
      case 'delivered': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return 'Order Placed';
      case 'confirmed': return 'Order Confirmed';
      case 'processing': return 'Processing';
      case 'shipped': return 'Shipped';
      case 'delivered': return 'Delivered';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'grey.50',
        }}
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error || !order) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'grey.50',
        }}
      >
        <Container maxWidth="sm">
          <Card sx={{ textAlign: 'center', p: 4 }}>
            <Typography variant="h4" gutterBottom>
              Order Not Found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              The order you're looking for doesn't exist or may have been removed.
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/')}
              sx={{ borderRadius: 2 }}
            >
              Go Home
            </Button>
          </Card>
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'grey.50', py: 4 }}>
      <Container maxWidth="lg">
        {/* Success Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card sx={{ mb: 4, borderRadius: 2, textAlign: 'center', py: 4 }}>
            <CheckCircle sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
              Order Confirmed!
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              Thank you for your purchase
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Order ID: <strong>{order.orderId}</strong>
            </Typography>
          </Card>
        </motion.div>

        <Grid container spacing={4}>
          {/* Order Details */}
          <Grid item xs={12} md={8}>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {/* Order Status */}
              <Card sx={{ mb: 3, borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      Order Status
                    </Typography>
                    <Chip
                      label={getStatusText(order.status)}
                      color={getStatusColor(order.status)}
                      variant="filled"
                    />
                  </Box>

                  {/* Order Timeline */}
                  <List>
                    {order.timeline?.map((event, index) => (
                      <ListItem key={index} sx={{ py: 1 }}>
                        <ListItemIcon>
                          <FiberManualRecord
                            color={getStatusColor(event.status)}
                            sx={{ fontSize: 12 }}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {getStatusText(event.status)}
                            </Typography>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {event.message}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {format(new Date(event.timestamp), 'MMM dd, yyyy - hh:mm a')}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>

              {/* Product Details */}
              <Card sx={{ mb: 3, borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 3 }}>
                    Product Details
                  </Typography>
                  
                  <Grid container spacing={3}>
                    <Grid item xs={3}>
                      <Avatar
                        src={order.product.image}
                        alt={order.product.name}
                        variant="rounded"
                        sx={{ width: 80, height: 80 }}
                      />
                    </Grid>
                    <Grid item xs={9}>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                        {order.product.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Quantity: {order.pricing.quantity}
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main' }}>
                        ₹{order.pricing.productPrice.toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Delivery Information */}
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 3 }}>
                    Delivery Information
                  </Typography>
                  
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Phone color="primary" />
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {order.buyer.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {order.buyer.phone}
                          </Typography>
                        </Box>
                      </Box>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Email color="primary" />
                        <Typography variant="body2">
                          {order.buyer.email}
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                        <LocationOn color="primary" />
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                            Delivery Address
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {order.buyer.address.street}<br />
                            {order.buyer.address.city}, {order.buyer.address.state}<br />
                            {order.buyer.address.pincode}, {order.buyer.address.country}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* Order Summary */}
          <Grid item xs={12} md={4}>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card sx={{ borderRadius: 2, position: 'sticky', top: 24 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 3 }}>
                    Order Summary
                  </Typography>

                  {/* Pricing Breakdown */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Subtotal</Typography>
                      <Typography>₹{order.pricing.subtotal.toLocaleString()}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Shipping</Typography>
                      <Typography>
                        {order.pricing.shippingCharges === 0 ? 'FREE' : `₹${order.pricing.shippingCharges}`}
                      </Typography>
                    </Box>
                    {order.pricing.codCharges > 0 && (
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography>COD Charges</Typography>
                        <Typography>₹{order.pricing.codCharges}</Typography>
                      </Box>
                    )}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Platform Fee</Typography>
                      <Typography>₹{order.pricing.platformFee}</Typography>
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                      <Typography variant="h6" sx={{ fontWeight: 700 }}>
                        Total Paid
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main' }}>
                        ₹{order.pricing.total.toLocaleString()}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Payment Method */}
                  <Paper sx={{ p: 2, mb: 3, backgroundColor: 'grey.50' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      Payment Method
                    </Typography>
                    <Typography variant="body2">
                      {order.payment.method === 'cod' ? 'Cash on Delivery' : 'Online Payment'}
                    </Typography>
                    <Chip
                      label={order.payment.status}
                      color={order.payment.status === 'completed' ? 'success' : 'warning'}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </Paper>

                  {/* Actions */}
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Button
                      variant="contained"
                      fullWidth
                      startIcon={<Receipt />}
                      sx={{ borderRadius: 2 }}
                    >
                      Download Invoice
                    </Button>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<Share />}
                      sx={{ borderRadius: 2 }}
                    >
                      Share Order
                    </Button>
                    <Button
                      variant="text"
                      fullWidth
                      startIcon={<Home />}
                      onClick={() => navigate('/')}
                      sx={{ borderRadius: 2 }}
                    >
                      Continue Shopping
                    </Button>
                  </Box>

                  {/* Seller Contact */}
                  {order.seller && (
                    <Paper sx={{ p: 2, mt: 3, backgroundColor: 'primary.50' }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Seller Contact
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        {order.seller.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {order.seller.phone}
                      </Typography>
                    </Paper>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default OrderConfirmationPage;
