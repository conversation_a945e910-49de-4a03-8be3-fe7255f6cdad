import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Avatar,
  IconButton,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
} from '@mui/material';
import {
  Person,
  Business,
  Payment,
  Notifications,
  Security,
  Verified,
  Edit,
  CloudUpload,
  Phone,
  Email,
  LocationOn,
  AccountBalance,
  CreditCard,
  QrCode,
  Save,
  Cancel,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import { useAuth } from '../contexts/AuthContext';

// Validation schemas
const profileSchema = yup.object({
  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().required('Phone is required'),
  businessName: yup.string().required('Business name is required'),
  businessType: yup.string().required('Business type is required'),
  address: yup.string().required('Address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  pincode: yup.string().required('Pincode is required').matches(/^\d{6}$/, 'Invalid pincode'),
  gstNumber: yup.string(),
});

const paymentSchema = yup.object({
  upiId: yup.string().required('UPI ID is required'),
  bankName: yup.string().required('Bank name is required'),
  accountNumber: yup.string().required('Account number is required'),
  ifscCode: yup.string().required('IFSC code is required').matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code'),
  accountHolderName: yup.string().required('Account holder name is required'),
});

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [editingProfile, setEditingProfile] = useState(false);
  const [editingPayment, setEditingPayment] = useState(false);
  const [showUpiDialog, setShowUpiDialog] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [notifications, setNotifications] = useState({
    orderUpdates: true,
    paymentAlerts: true,
    marketingEmails: false,
    smsNotifications: true,
  });

  const { user, updateUser } = useAuth();
  const queryClient = useQueryClient();

  // Mock user data
  const mockUserData = {
    name: user?.name || 'John Doe',
    email: '<EMAIL>',
    phone: user?.phone || '+************',
    businessName: 'SwiftStore',
    businessType: 'retail',
    address: '123 Business Street',
    city: 'Mumbai',
    state: 'Maharashtra',
    pincode: '400001',
    gstNumber: '27AAAAA0000A1Z5',
    isVerified: true,
    verificationStatus: 'verified',
    upiId: 'john@paytm',
    bankName: 'HDFC Bank',
    accountNumber: '****1234',
    ifscCode: 'HDFC0000123',
    accountHolderName: 'John Doe',
  };

  const profileForm = useForm({
    resolver: yupResolver(profileSchema),
    defaultValues: mockUserData,
  });

  const paymentForm = useForm({
    resolver: yupResolver(paymentSchema),
    defaultValues: mockUserData,
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data) => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return data;
    },
    onSuccess: (data) => {
      updateUser(data);
      setEditingProfile(false);
    },
  });

  // Update payment mutation
  const updatePaymentMutation = useMutation({
    mutationFn: async (data) => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return data;
    },
    onSuccess: () => {
      setEditingPayment(false);
    },
  });

  const handleProfileSubmit = (data) => {
    updateProfileMutation.mutate(data);
  };

  const handlePaymentSubmit = (data) => {
    updatePaymentMutation.mutate(data);
  };

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImage(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleNotificationChange = (key) => (event) => {
    setNotifications(prev => ({
      ...prev,
      [key]: event.target.checked,
    }));
  };

  const getVerificationChip = (status) => {
    const configs = {
      verified: { label: 'Verified', color: 'success', icon: Verified },
      pending: { label: 'Pending', color: 'warning', icon: null },
      rejected: { label: 'Rejected', color: 'error', icon: null },
    };

    const config = configs[status] || configs.pending;

    return (
      <Chip
        icon={config.icon ? <config.icon fontSize="small" /> : undefined}
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <DashboardLayout>
      <Box>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            Settings
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Manage your account, business, and payment settings
          </Typography>
        </motion.div>

        {/* Settings Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card sx={{ borderRadius: 2 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={activeTab}
                onChange={(e, newValue) => setActiveTab(newValue)}
                aria-label="settings tabs"
              >
                <Tab icon={<Person />} label="Profile" />
                <Tab icon={<Business />} label="Business" />
                <Tab icon={<Payment />} label="Payment" />
                <Tab icon={<Notifications />} label="Notifications" />
                <Tab icon={<Security />} label="Security" />
              </Tabs>
            </Box>

            {/* Profile Tab */}
            <TabPanel value={activeTab} index={0}>
              <Grid container spacing={3}>
                {/* Profile Picture */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 3 }}>
                    <Avatar
                      src={profileImage}
                      sx={{ width: 80, height: 80 }}
                    >
                      {mockUserData.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {mockUserData.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {mockUserData.email}
                      </Typography>
                      {getVerificationChip(mockUserData.verificationStatus)}
                    </Box>
                    <Box sx={{ ml: 'auto' }}>
                      <input
                        accept="image/*"
                        style={{ display: 'none' }}
                        id="profile-image-upload"
                        type="file"
                        onChange={handleImageChange}
                      />
                      <label htmlFor="profile-image-upload">
                        <IconButton component="span" color="primary">
                          <CloudUpload />
                        </IconButton>
                      </label>
                    </Box>
                  </Box>
                </Grid>

                {/* Profile Form */}
                <Grid item xs={12}>
                  <form onSubmit={profileForm.handleSubmit(handleProfileSubmit)}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          {...profileForm.register('name')}
                          label="Full Name"
                          fullWidth
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.name}
                          helperText={profileForm.formState.errors.name?.message}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          {...profileForm.register('email')}
                          label="Email Address"
                          fullWidth
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.email}
                          helperText={profileForm.formState.errors.email?.message}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <Email color="action" />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          {...profileForm.register('phone')}
                          label="Phone Number"
                          fullWidth
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.phone}
                          helperText={profileForm.formState.errors.phone?.message}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <Phone color="action" />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          {...profileForm.register('businessName')}
                          label="Business Name"
                          fullWidth
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.businessName}
                          helperText={profileForm.formState.errors.businessName?.message}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          {...profileForm.register('address')}
                          label="Address"
                          fullWidth
                          multiline
                          rows={2}
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.address}
                          helperText={profileForm.formState.errors.address?.message}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <LocationOn color="action" />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          {...profileForm.register('city')}
                          label="City"
                          fullWidth
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.city}
                          helperText={profileForm.formState.errors.city?.message}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          {...profileForm.register('state')}
                          label="State"
                          fullWidth
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.state}
                          helperText={profileForm.formState.errors.state?.message}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          {...profileForm.register('pincode')}
                          label="Pincode"
                          fullWidth
                          disabled={!editingProfile}
                          error={!!profileForm.formState.errors.pincode}
                          helperText={profileForm.formState.errors.pincode?.message}
                        />
                      </Grid>
                    </Grid>

                    <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                      {editingProfile ? (
                        <>
                          <Button
                            type="submit"
                            variant="contained"
                            startIcon={<Save />}
                            disabled={updateProfileMutation.isLoading}
                            sx={{ borderRadius: 2 }}
                          >
                            {updateProfileMutation.isLoading ? 'Saving...' : 'Save Changes'}
                          </Button>
                          <Button
                            variant="outlined"
                            startIcon={<Cancel />}
                            onClick={() => setEditingProfile(false)}
                            sx={{ borderRadius: 2 }}
                          >
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <Button
                          variant="contained"
                          startIcon={<Edit />}
                          onClick={() => setEditingProfile(true)}
                          sx={{ borderRadius: 2 }}
                        >
                          Edit Profile
                        </Button>
                      )}
                    </Box>
                  </form>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Business Tab */}
            <TabPanel value={activeTab} index={1}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Alert severity="info" sx={{ borderRadius: 2, mb: 3 }}>
                    Complete your business verification to unlock all features and build customer trust.
                  </Alert>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    {...profileForm.register('businessType')}
                    label="Business Type"
                    fullWidth
                    select
                    disabled={!editingProfile}
                  >
                    <MenuItem value="retail">Retail</MenuItem>
                    <MenuItem value="wholesale">Wholesale</MenuItem>
                    <MenuItem value="service">Service</MenuItem>
                    <MenuItem value="manufacturing">Manufacturing</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </TextField>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    {...profileForm.register('gstNumber')}
                    label="GST Number (Optional)"
                    fullWidth
                    disabled={!editingProfile}
                    error={!!profileForm.formState.errors.gstNumber}
                    helperText={profileForm.formState.errors.gstNumber?.message}
                  />
                </Grid>

                {/* Verification Status */}
                <Grid item xs={12}>
                  <Card sx={{ borderRadius: 2, border: '1px solid', borderColor: 'divider' }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                        Verification Status
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <Email color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Email Verification"
                            secondary="Your email has been verified"
                          />
                          <ListItemSecondaryAction>
                            <Chip label="Verified" color="success" size="small" />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <Phone color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Phone Verification"
                            secondary="Your phone number has been verified"
                          />
                          <ListItemSecondaryAction>
                            <Chip label="Verified" color="success" size="small" />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <Business color="warning" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Business Verification"
                            secondary="Upload business documents for verification"
                          />
                          <ListItemSecondaryAction>
                            <Chip label="Pending" color="warning" size="small" />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Payment Tab */}
            <TabPanel value={activeTab} index={2}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Alert severity="info" sx={{ borderRadius: 2, mb: 3 }}>
                    Add your payment details to receive payments from customers. All information is encrypted and secure.
                  </Alert>
                </Grid>

                {/* UPI Details */}
                <Grid item xs={12}>
                  <Card sx={{ borderRadius: 2, mb: 3 }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          UPI Details
                        </Typography>
                        <Button
                          variant="outlined"
                          startIcon={<QrCode />}
                          onClick={() => setShowUpiDialog(true)}
                          sx={{ borderRadius: 2 }}
                        >
                          Generate QR
                        </Button>
                      </Box>
                      <form onSubmit={paymentForm.handleSubmit(handlePaymentSubmit)}>
                        <TextField
                          {...paymentForm.register('upiId')}
                          label="UPI ID"
                          fullWidth
                          disabled={!editingPayment}
                          error={!!paymentForm.formState.errors.upiId}
                          helperText={paymentForm.formState.errors.upiId?.message}
                          placeholder="yourname@paytm"
                          sx={{ mb: 2 }}
                        />
                      </form>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Bank Details */}
                <Grid item xs={12}>
                  <Card sx={{ borderRadius: 2 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                        Bank Account Details
                      </Typography>
                      <form onSubmit={paymentForm.handleSubmit(handlePaymentSubmit)}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <TextField
                              {...paymentForm.register('bankName')}
                              label="Bank Name"
                              fullWidth
                              disabled={!editingPayment}
                              error={!!paymentForm.formState.errors.bankName}
                              helperText={paymentForm.formState.errors.bankName?.message}
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <TextField
                              {...paymentForm.register('accountHolderName')}
                              label="Account Holder Name"
                              fullWidth
                              disabled={!editingPayment}
                              error={!!paymentForm.formState.errors.accountHolderName}
                              helperText={paymentForm.formState.errors.accountHolderName?.message}
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <TextField
                              {...paymentForm.register('accountNumber')}
                              label="Account Number"
                              fullWidth
                              disabled={!editingPayment}
                              error={!!paymentForm.formState.errors.accountNumber}
                              helperText={paymentForm.formState.errors.accountNumber?.message}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <AccountBalance color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <TextField
                              {...paymentForm.register('ifscCode')}
                              label="IFSC Code"
                              fullWidth
                              disabled={!editingPayment}
                              error={!!paymentForm.formState.errors.ifscCode}
                              helperText={paymentForm.formState.errors.ifscCode?.message}
                            />
                          </Grid>
                        </Grid>

                        <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                          {editingPayment ? (
                            <>
                              <Button
                                type="submit"
                                variant="contained"
                                startIcon={<Save />}
                                disabled={updatePaymentMutation.isLoading}
                                sx={{ borderRadius: 2 }}
                              >
                                {updatePaymentMutation.isLoading ? 'Saving...' : 'Save Payment Details'}
                              </Button>
                              <Button
                                variant="outlined"
                                startIcon={<Cancel />}
                                onClick={() => setEditingPayment(false)}
                                sx={{ borderRadius: 2 }}
                              >
                                Cancel
                              </Button>
                            </>
                          ) : (
                            <Button
                              variant="contained"
                              startIcon={<Edit />}
                              onClick={() => setEditingPayment(true)}
                              sx={{ borderRadius: 2 }}
                            >
                              Edit Payment Details
                            </Button>
                          )}
                        </Box>
                      </form>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Notifications Tab */}
            <TabPanel value={activeTab} index={3}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Notification Preferences
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Choose how you want to be notified about important updates.
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Card sx={{ borderRadius: 2 }}>
                    <CardContent>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <ShoppingCart />
                          </ListItemIcon>
                          <ListItemText
                            primary="Order Updates"
                            secondary="Get notified when you receive new orders or order status changes"
                          />
                          <ListItemSecondaryAction>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={notifications.orderUpdates}
                                  onChange={handleNotificationChange('orderUpdates')}
                                />
                              }
                              label=""
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <Divider />
                        <ListItem>
                          <ListItemIcon>
                            <Payment />
                          </ListItemIcon>
                          <ListItemText
                            primary="Payment Alerts"
                            secondary="Receive notifications for successful payments and refunds"
                          />
                          <ListItemSecondaryAction>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={notifications.paymentAlerts}
                                  onChange={handleNotificationChange('paymentAlerts')}
                                />
                              }
                              label=""
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <Divider />
                        <ListItem>
                          <ListItemIcon>
                            <Email />
                          </ListItemIcon>
                          <ListItemText
                            primary="Marketing Emails"
                            secondary="Receive tips, updates, and promotional content"
                          />
                          <ListItemSecondaryAction>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={notifications.marketingEmails}
                                  onChange={handleNotificationChange('marketingEmails')}
                                />
                              }
                              label=""
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <Divider />
                        <ListItem>
                          <ListItemIcon>
                            <Phone />
                          </ListItemIcon>
                          <ListItemText
                            primary="SMS Notifications"
                            secondary="Receive important alerts via SMS"
                          />
                          <ListItemSecondaryAction>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={notifications.smsNotifications}
                                  onChange={handleNotificationChange('smsNotifications')}
                                />
                              }
                              label=""
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Security Tab */}
            <TabPanel value={activeTab} index={4}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Security Settings
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Manage your account security and privacy settings.
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card sx={{ borderRadius: 2 }}>
                    <CardContent>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                        Change Password
                      </Typography>
                      <TextField
                        label="Current Password"
                        type="password"
                        fullWidth
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        label="New Password"
                        type="password"
                        fullWidth
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        label="Confirm New Password"
                        type="password"
                        fullWidth
                        sx={{ mb: 2 }}
                      />
                      <Button
                        variant="contained"
                        sx={{ borderRadius: 2 }}
                      >
                        Update Password
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card sx={{ borderRadius: 2 }}>
                    <CardContent>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                        Two-Factor Authentication
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Add an extra layer of security to your account.
                      </Typography>
                      <Button
                        variant="outlined"
                        sx={{ borderRadius: 2 }}
                      >
                        Enable 2FA
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card sx={{ borderRadius: 2, border: '1px solid', borderColor: 'error.main' }}>
                    <CardContent>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: 'error.main' }}>
                        Danger Zone
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        These actions are irreversible. Please proceed with caution.
                      </Typography>
                      <Button
                        variant="outlined"
                        color="error"
                        sx={{ borderRadius: 2 }}
                      >
                        Delete Account
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>
          </Card>
        </motion.div>

        {/* UPI QR Dialog */}
        <Dialog
          open={showUpiDialog}
          onClose={() => setShowUpiDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>UPI QR Code</DialogTitle>
          <DialogContent sx={{ textAlign: 'center', py: 4 }}>
            <Box
              sx={{
                width: 200,
                height: 200,
                backgroundColor: 'grey.100',
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 2,
              }}
            >
              <QrCode sx={{ fontSize: 100, color: 'grey.400' }} />
            </Box>
            <Typography variant="body2" color="text.secondary">
              Customers can scan this QR code to pay you directly via UPI
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowUpiDialog(false)}>Close</Button>
            <Button variant="contained" startIcon={<Download />}>
              Download QR
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </DashboardLayout>
  );
};

export default SettingsPage;
