import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Avatar,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormControlLabel,
  Fab,
  Tooltip,
  Alert,
  Skeleton,
} from '@mui/material';
import {
  Search,
  Add,
  MoreVert,
  Edit,
  Delete,
  Link,
  Visibility,
  VisibilityOff,
  QrCode,
  Share,
  FilterList,
  SelectAll,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import { productService } from '../services/productService';
import ProductForm from '../components/products/ProductForm';
import PlaceholderImage from '../components/common/PlaceholderImage';
import { useNotification } from '../contexts/NotificationContext';
import { handleApiError } from '../utils/errorHandler';

const ProductsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showProductForm, setShowProductForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [bulkDeleteOpen, setBulkDeleteOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotification();

  // Mock data for testing
  const mockProducts = [
    {
      id: 1,
      name: 'Premium T-Shirt',
      description: 'High-quality cotton t-shirt with premium finish and comfortable fit',
      price: 1299,
      stock: 50,
      category: 'Clothing',
      isActive: true,
      image: null,
    },
    {
      id: 2,
      name: 'Wireless Headphones',
      description: 'Noise-cancelling wireless headphones with 30-hour battery life',
      price: 4999,
      stock: 25,
      category: 'Electronics',
      isActive: true,
      image: null,
    },
    {
      id: 3,
      name: 'Smart Watch',
      description: 'Feature-rich smartwatch with health monitoring and GPS',
      price: 8999,
      stock: 15,
      category: 'Electronics',
      isActive: false,
      image: null,
    },
    {
      id: 4,
      name: 'Running Shoes',
      description: 'Lightweight running shoes with advanced cushioning technology',
      price: 3499,
      stock: 30,
      category: 'Footwear',
      isActive: true,
      image: null,
    },
  ];

  // Fetch products
  const {
    data: productsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['products', page, searchTerm],
    queryFn: () => productService.getProducts(page, 12, searchTerm),
    keepPreviousData: true,
    // Use mock data as fallback when API fails
    onError: () => {
      console.log('📦 Using mock data for products');
    },
    // Provide initial data for immediate UI rendering
    initialData: {
      products: mockProducts.filter(p =>
        !searchTerm || p.name.toLowerCase().includes(searchTerm.toLowerCase())
      ),
      total: mockProducts.length,
      page: 1,
      totalPages: 1,
    },
  });

  // Delete product mutation
  const deleteProductMutation = useMutation({
    mutationFn: productService.deleteProduct,
    onSuccess: () => {
      queryClient.invalidateQueries(['products']);
      setDeleteConfirmOpen(false);
      setSelectedProduct(null);
      showSuccess('Product deleted successfully!');
    },
    onError: (error) => {
      const errorInfo = handleApiError(error);
      showError(errorInfo.message, { title: 'Failed to delete product' });
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: productService.bulkDelete,
    onSuccess: () => {
      queryClient.invalidateQueries(['products']);
      setBulkDeleteOpen(false);
      setSelectedProducts([]);
    },
  });

  // Toggle status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: productService.toggleStatus,
    onSuccess: () => {
      queryClient.invalidateQueries(['products']);
    },
  });

  const handleMenuOpen = (event, product) => {
    setAnchorEl(event.currentTarget);
    setSelectedProduct(product);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProduct(null);
  };

  const handleEdit = () => {
    setEditingProduct(selectedProduct);
    setShowProductForm(true);
    handleMenuClose();
  };

  const handleDelete = () => {
    setDeleteConfirmOpen(true);
    handleMenuClose();
  };

  const handleToggleStatus = () => {
    if (selectedProduct) {
      toggleStatusMutation.mutate(selectedProduct.id);
    }
    handleMenuClose();
  };

  const handleSelectProduct = (productId) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProducts.length === productsData?.products?.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(productsData?.products?.map(p => p.id) || []);
    }
  };

  const handleBulkDelete = () => {
    setBulkDeleteOpen(true);
  };

  const confirmDelete = () => {
    if (selectedProduct) {
      deleteProductMutation.mutate(selectedProduct.id);
    }
  };

  const confirmBulkDelete = () => {
    bulkDeleteMutation.mutate(selectedProducts);
  };

  const products = productsData?.products || [];
  const totalProducts = productsData?.total || 0;

  return (
    <DashboardLayout>
      <Box>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                Products
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage your product catalog and checkout links
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setShowProductForm(true)}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: 3,
                py: 1.5,
              }}
            >
              Add Product
            </Button>
          </Box>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card sx={{ mb: 3, borderRadius: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                <TextField
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ flex: 1, minWidth: 300 }}
                />
                <Button
                  variant="outlined"
                  startIcon={<FilterList />}
                  onClick={() => setShowFilters(!showFilters)}
                  sx={{ borderRadius: 2 }}
                >
                  Filters
                </Button>
                {selectedProducts.length > 0 && (
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<Delete />}
                    onClick={handleBulkDelete}
                    sx={{ borderRadius: 2 }}
                  >
                    Delete ({selectedProducts.length})
                  </Button>
                )}
              </Box>

              {/* Bulk Actions */}
              {selectedProducts.length > 0 && (
                <Box sx={{ mt: 2, p: 2, backgroundColor: 'primary.50', borderRadius: 2 }}>
                  <Typography variant="body2" color="primary.main">
                    {selectedProducts.length} product(s) selected
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error.message}
          </Alert>
        )}

        {/* Products Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {isLoading ? (
            <Grid container spacing={3}>
              {[...Array(8)].map((_, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                  <Card sx={{ borderRadius: 2 }}>
                    <Skeleton variant="rectangular" height={200} />
                    <CardContent>
                      <Skeleton variant="text" height={24} />
                      <Skeleton variant="text" height={20} width="60%" />
                      <Skeleton variant="text" height={20} width="40%" />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : products.length === 0 ? (
            <Card sx={{ borderRadius: 2, textAlign: 'center', py: 8 }}>
              <CardContent>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No products found
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {searchTerm ? 'Try adjusting your search terms' : 'Start by adding your first product'}
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => setShowProductForm(true)}
                  sx={{ borderRadius: 2 }}
                >
                  Add Product
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Grid container spacing={3}>
              {/* Select All Checkbox */}
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedProducts.length === products.length && products.length > 0}
                      indeterminate={selectedProducts.length > 0 && selectedProducts.length < products.length}
                      onChange={handleSelectAll}
                    />
                  }
                  label={`Select All (${products.length} products)`}
                />
              </Grid>

              {/* Product Cards */}
              {products.map((product, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card
                      sx={{
                        borderRadius: 2,
                        position: 'relative',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
                        },
                        transition: 'all 0.3s ease-in-out',
                      }}
                    >
                      {/* Selection Checkbox */}
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onChange={() => handleSelectProduct(product.id)}
                        sx={{
                          position: 'absolute',
                          top: 8,
                          left: 8,
                          zIndex: 1,
                          backgroundColor: 'rgba(255,255,255,0.8)',
                          borderRadius: 1,
                        }}
                      />

                      {/* Product Image */}
                      <Box
                        sx={{
                          height: 200,
                          position: 'relative',
                          overflow: 'hidden',
                        }}
                      >
                        {product.image ? (
                          <Box
                            sx={{
                              width: '100%',
                              height: '100%',
                              backgroundImage: `url(${product.image})`,
                              backgroundSize: 'cover',
                              backgroundPosition: 'center',
                            }}
                          />
                        ) : (
                          <PlaceholderImage height={200} text="Product Image" />
                        )}
                        {/* Status Badge */}
                        <Chip
                          label={product.isActive ? 'Active' : 'Inactive'}
                          color={product.isActive ? 'success' : 'default'}
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                          }}
                        />

                        {/* Menu Button */}
                        <IconButton
                          onClick={(e) => handleMenuOpen(e, product)}
                          sx={{
                            position: 'absolute',
                            bottom: 8,
                            right: 8,
                            backgroundColor: 'rgba(255,255,255,0.9)',
                            '&:hover': {
                              backgroundColor: 'rgba(255,255,255,1)',
                            },
                          }}
                        >
                          <MoreVert />
                        </IconButton>
                      </Box>

                      <CardContent>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }} noWrap>
                          {product.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {product.description?.substring(0, 80)}
                          {product.description?.length > 80 && '...'}
                        </Typography>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="h6" color="primary.main" sx={{ fontWeight: 700 }}>
                            ₹{product.price?.toLocaleString()}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Stock: {product.stock || 0}
                          </Typography>
                        </Box>

                        {/* Quick Actions */}
                        <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                          <Button
                            size="small"
                            startIcon={<Link />}
                            onClick={() => {/* Handle checkout link */}}
                            sx={{ flex: 1, borderRadius: 1 }}
                          >
                            Link
                          </Button>
                          <Button
                            size="small"
                            startIcon={<QrCode />}
                            onClick={() => {/* Handle QR code */}}
                            sx={{ flex: 1, borderRadius: 1 }}
                          >
                            QR
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          )}
        </motion.div>

        {/* Floating Add Button */}
        <Fab
          color="primary"
          onClick={() => setShowProductForm(true)}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1000,
          }}
        >
          <Add />
        </Fab>

        {/* Product Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handleEdit}>
            <Edit sx={{ mr: 1 }} fontSize="small" />
            Edit
          </MenuItem>
          <MenuItem onClick={handleToggleStatus}>
            {selectedProduct?.isActive ? (
              <>
                <VisibilityOff sx={{ mr: 1 }} fontSize="small" />
                Deactivate
              </>
            ) : (
              <>
                <Visibility sx={{ mr: 1 }} fontSize="small" />
                Activate
              </>
            )}
          </MenuItem>
          <MenuItem onClick={() => {/* Handle checkout link */}}>
            <Link sx={{ mr: 1 }} fontSize="small" />
            Checkout Link
          </MenuItem>
          <MenuItem onClick={() => {/* Handle QR code */}}>
            <QrCode sx={{ mr: 1 }} fontSize="small" />
            QR Code
          </MenuItem>
          <MenuItem onClick={() => {/* Handle share */}}>
            <Share sx={{ mr: 1 }} fontSize="small" />
            Share
          </MenuItem>
          <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
            <Delete sx={{ mr: 1 }} fontSize="small" />
            Delete
          </MenuItem>
        </Menu>

        {/* Product Form Dialog */}
        <ProductForm
          open={showProductForm}
          onClose={() => {
            setShowProductForm(false);
            setEditingProduct(null);
          }}
          product={editingProduct}
          onSuccess={() => {
            queryClient.invalidateQueries(['products']);
            setShowProductForm(false);
            setEditingProduct(null);
          }}
        />

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteConfirmOpen}
          onClose={() => setDeleteConfirmOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Delete Product</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete "{selectedProduct?.name}"? This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
            <Button
              onClick={confirmDelete}
              color="error"
              variant="contained"
              disabled={deleteProductMutation.isLoading}
            >
              {deleteProductMutation.isLoading ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Bulk Delete Confirmation Dialog */}
        <Dialog
          open={bulkDeleteOpen}
          onClose={() => setBulkDeleteOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Delete Products</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete {selectedProducts.length} product(s)? This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setBulkDeleteOpen(false)}>Cancel</Button>
            <Button
              onClick={confirmBulkDelete}
              color="error"
              variant="contained"
              disabled={bulkDeleteMutation.isLoading}
            >
              {bulkDeleteMutation.isLoading ? 'Deleting...' : 'Delete All'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </DashboardLayout>
  );
};

export default ProductsPage;
