import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Avatar,
  Button,
  TextField,
  Chip,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Person,
  Email,
  Phone,
  Edit,
  Save,
  Cancel,
  Verified,
  Star,
  ShoppingBag,
  TrendingUp,
  Notifications,
  Security
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import BuyerLayout from '../components/dashboard/BuyerLayout';
import { buyerService } from '../services/buyerService';

const BuyerProfilePage = () => {
  const { user, updateUser } = useAuth();
  const [editing, setEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    phone: ''
  });
  const [preferences, setPreferences] = useState({
    sms: true,
    email: true,
    whatsapp: false,
    orderUpdates: true,
    promotions: false
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || ''
      });
      setPreferences({
        sms: user.preferences?.communicationPreferences?.sms ?? true,
        email: user.preferences?.communicationPreferences?.email ?? true,
        whatsapp: user.preferences?.communicationPreferences?.whatsapp ?? false,
        orderUpdates: true,
        promotions: false
      });
    }
  }, [user]);

  const handleSaveProfile = async () => {
    try {
      setLoading(true);
      const response = await buyerService.updateProfile(profileData);

      // Update local state
      updateUser(response.buyer);
      setEditing(false);
      setMessage('Profile updated successfully!');

      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('Failed to update profile:', error);
      setMessage('Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSavePreferences = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // await profileService.updatePreferences(preferences);
      
      setMessage('Preferences updated successfully!');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('Failed to update preferences:', error);
      setMessage('Failed to update preferences. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setProfileData({
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || ''
    });
    setEditing(false);
  };

  if (!user) {
    return (
      <BuyerLayout>
        <Container>
          <Alert severity="error">Please log in to view your profile</Alert>
        </Container>
      </BuyerLayout>
    );
  }

  return (
    <BuyerLayout>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
            My Profile
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your personal information and preferences
          </Typography>
        </Box>

        {message && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {message}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Profile Information */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">Personal Information</Typography>
                  {!editing ? (
                    <Button
                      startIcon={<Edit />}
                      onClick={() => setEditing(true)}
                    >
                      Edit Profile
                    </Button>
                  ) : (
                    <Box>
                      <Button
                        startIcon={<Cancel />}
                        onClick={handleCancelEdit}
                        sx={{ mr: 1 }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        startIcon={<Save />}
                        onClick={handleSaveProfile}
                        disabled={loading}
                      >
                        Save Changes
                      </Button>
                    </Box>
                  )}
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12} sm={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 120,
                          height: 120,
                          mx: 'auto',
                          mb: 2,
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          fontSize: '3rem'
                        }}
                      >
                        {user.name?.charAt(0)?.toUpperCase() || 'B'}
                      </Avatar>
                      <Typography variant="h6">{user.name}</Typography>
                      <Chip
                        label={user.verification?.phoneVerified ? 'Verified' : 'Unverified'}
                        color={user.verification?.phoneVerified ? 'success' : 'warning'}
                        icon={<Verified />}
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Full Name"
                          value={profileData.name}
                          onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
                          disabled={!editing}
                          InputProps={{
                            startAdornment: <Person sx={{ mr: 1, color: 'text.secondary' }} />
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Email Address"
                          type="email"
                          value={profileData.email}
                          onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                          disabled={!editing}
                          InputProps={{
                            startAdornment: <Email sx={{ mr: 1, color: 'text.secondary' }} />
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Phone Number"
                          value={profileData.phone}
                          disabled={true} // Phone number should not be editable
                          InputProps={{
                            startAdornment: <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                          }}
                          helperText="Phone number cannot be changed"
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Communication Preferences */}
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">Communication Preferences</Typography>
                  <Button
                    variant="outlined"
                    onClick={handleSavePreferences}
                    disabled={loading}
                  >
                    Save Preferences
                  </Button>
                </Box>

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Notifications />
                    </ListItemIcon>
                    <ListItemText
                      primary="SMS Notifications"
                      secondary="Receive order updates via SMS"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={preferences.sms}
                          onChange={(e) => setPreferences({ ...preferences, sms: e.target.checked })}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <Email />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email Notifications"
                      secondary="Receive order updates via email"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={preferences.email}
                          onChange={(e) => setPreferences({ ...preferences, email: e.target.checked })}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <Phone />
                    </ListItemIcon>
                    <ListItemText
                      primary="WhatsApp Updates"
                      secondary="Receive order updates on WhatsApp"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={preferences.whatsapp}
                          onChange={(e) => setPreferences({ ...preferences, whatsapp: e.target.checked })}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Profile Stats */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Account Statistics
                </Typography>
                
                <List>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <ShoppingBag color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Orders"
                      secondary={user.profile?.totalOrders || 0}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <TrendingUp color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Spent"
                      secondary={`₹${user.profile?.totalSpent || 0}`}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Star color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Loyalty Points"
                      secondary={user.profile?.loyaltyPoints || 0}
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Member since: {new Date(user.createdAt).toLocaleDateString()}
                </Typography>
                
                {user.profile?.averageOrderValue > 0 && (
                  <Typography variant="body2" color="text.secondary">
                    Average order value: ₹{user.profile.averageOrderValue}
                  </Typography>
                )}
              </CardContent>
            </Card>

            {/* Security Card */}
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Account Security
                </Typography>
                
                <List>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Security color={user.verification?.phoneVerified ? 'success' : 'warning'} />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone Verification"
                      secondary={user.verification?.phoneVerified ? 'Verified' : 'Not verified'}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Email color={user.verification?.emailVerified ? 'success' : 'warning'} />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email Verification"
                      secondary={user.verification?.emailVerified ? 'Verified' : 'Not verified'}
                    />
                  </ListItem>
                </List>

                {!user.verification?.emailVerified && (
                  <Button variant="outlined" size="small" fullWidth sx={{ mt: 2 }}>
                    Verify Email
                  </Button>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </BuyerLayout>
  );
};

export default BuyerProfilePage;
