import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  LocationOn,
  Add,
  Edit,
  Delete,
  Home,
  Business,
  LocationCity
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import BuyerLayout from '../components/dashboard/BuyerLayout';

const BuyerAddressesPage = () => {
  const { user } = useAuth();
  const [addresses, setAddresses] = useState([]);
  const [addAddressOpen, setAddAddressOpen] = useState(false);
  const [editAddressOpen, setEditAddressOpen] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [newAddress, setNewAddress] = useState({
    street: '',
    city: '',
    state: '',
    pincode: '',
    label: 'Home',
    isDefault: false
  });

  useEffect(() => {
    if (user?.addresses) {
      setAddresses(user.addresses);
    }
  }, [user]);

  const handleAddAddress = async () => {
    try {
      // TODO: Replace with actual API call
      // await addressService.addAddress(newAddress);
      
      const addressWithId = {
        ...newAddress,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };

      // If this is the first address or marked as default, make it default
      if (addresses.length === 0 || newAddress.isDefault) {
        // Remove default from other addresses
        const updatedAddresses = addresses.map(addr => ({ ...addr, isDefault: false }));
        setAddresses([...updatedAddresses, { ...addressWithId, isDefault: true }]);
      } else {
        setAddresses([...addresses, addressWithId]);
      }

      setNewAddress({
        street: '',
        city: '',
        state: '',
        pincode: '',
        label: 'Home',
        isDefault: false
      });
      setAddAddressOpen(false);
    } catch (error) {
      console.error('Failed to add address:', error);
    }
  };

  const handleEditAddress = async () => {
    try {
      // TODO: Replace with actual API call
      // await addressService.updateAddress(selectedAddress.id, selectedAddress);
      
      const updatedAddresses = addresses.map(addr => {
        if (addr.id === selectedAddress.id) {
          return selectedAddress;
        }
        // If this address is being set as default, remove default from others
        if (selectedAddress.isDefault && addr.isDefault) {
          return { ...addr, isDefault: false };
        }
        return addr;
      });

      setAddresses(updatedAddresses);
      setEditAddressOpen(false);
      setSelectedAddress(null);
    } catch (error) {
      console.error('Failed to update address:', error);
    }
  };

  const handleDeleteAddress = async (addressId) => {
    try {
      // TODO: Replace with actual API call
      // await addressService.deleteAddress(addressId);
      
      const addressToDelete = addresses.find(addr => addr.id === addressId);
      const updatedAddresses = addresses.filter(addr => addr.id !== addressId);
      
      // If we deleted the default address, make the first remaining address default
      if (addressToDelete?.isDefault && updatedAddresses.length > 0) {
        updatedAddresses[0].isDefault = true;
      }
      
      setAddresses(updatedAddresses);
    } catch (error) {
      console.error('Failed to delete address:', error);
    }
  };

  const handleSetDefault = async (addressId) => {
    try {
      // TODO: Replace with actual API call
      // await addressService.setDefaultAddress(addressId);
      
      const updatedAddresses = addresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }));
      
      setAddresses(updatedAddresses);
    } catch (error) {
      console.error('Failed to set default address:', error);
    }
  };

  const openEditDialog = (address) => {
    setSelectedAddress({ ...address });
    setEditAddressOpen(true);
  };

  const getAddressIcon = (label) => {
    switch (label.toLowerCase()) {
      case 'home': return <Home />;
      case 'office': 
      case 'work': return <Business />;
      default: return <LocationCity />;
    }
  };

  return (
    <BuyerLayout>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
            My Addresses
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your delivery addresses
          </Typography>
        </Box>

        {/* Add Address Button */}
        <Box sx={{ mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setAddAddressOpen(true)}
            size="large"
          >
            Add New Address
          </Button>
        </Box>

        {/* Addresses Grid */}
        {addresses.length === 0 ? (
          <Card>
            <CardContent>
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <LocationOn sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No addresses saved
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Add your first address for faster checkout
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => setAddAddressOpen(true)}
                >
                  Add Address
                </Button>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Grid container spacing={3}>
            {addresses.map((address) => (
              <Grid item xs={12} md={6} lg={4} key={address.id}>
                <Paper
                  sx={{
                    p: 3,
                    border: address.isDefault ? '2px solid' : '1px solid',
                    borderColor: address.isDefault ? 'primary.main' : 'divider',
                    position: 'relative',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column'
                  }}
                >
                  {/* Address Header */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {getAddressIcon(address.label)}
                      <Typography variant="h6" sx={{ ml: 1 }}>
                        {address.label}
                      </Typography>
                    </Box>
                    {address.isDefault && (
                      <Chip label="Default" color="primary" size="small" />
                    )}
                  </Box>

                  {/* Address Details */}
                  <Box sx={{ flex: 1, mb: 2 }}>
                    <Typography variant="body1" gutterBottom>
                      {address.street}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {address.city}, {address.state}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {address.pincode}
                    </Typography>
                  </Box>

                  {/* Actions */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      {!address.isDefault && (
                        <Button
                          size="small"
                          onClick={() => handleSetDefault(address.id)}
                        >
                          Set as Default
                        </Button>
                      )}
                    </Box>
                    <Box>
                      <IconButton
                        size="small"
                        onClick={() => openEditDialog(address)}
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteAddress(address.id)}
                        disabled={addresses.length === 1}
                      >
                        <Delete />
                      </IconButton>
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Add Address Dialog */}
        <Dialog open={addAddressOpen} onClose={() => setAddAddressOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Add New Address</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="Street Address"
              value={newAddress.street}
              onChange={(e) => setNewAddress({ ...newAddress, street: e.target.value })}
              margin="normal"
              multiline
              rows={2}
              required
            />
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="City"
                  value={newAddress.city}
                  onChange={(e) => setNewAddress({ ...newAddress, city: e.target.value })}
                  margin="normal"
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="State"
                  value={newAddress.state}
                  onChange={(e) => setNewAddress({ ...newAddress, state: e.target.value })}
                  margin="normal"
                  required
                />
              </Grid>
            </Grid>
            <TextField
              fullWidth
              label="Pincode"
              value={newAddress.pincode}
              onChange={(e) => setNewAddress({ ...newAddress, pincode: e.target.value })}
              margin="normal"
              required
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Address Label</InputLabel>
              <Select
                value={newAddress.label}
                onChange={(e) => setNewAddress({ ...newAddress, label: e.target.value })}
                label="Address Label"
              >
                <MenuItem value="Home">Home</MenuItem>
                <MenuItem value="Office">Office</MenuItem>
                <MenuItem value="Work">Work</MenuItem>
                <MenuItem value="Other">Other</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAddAddressOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleAddAddress} 
              variant="contained"
              disabled={!newAddress.street || !newAddress.city || !newAddress.state || !newAddress.pincode}
            >
              Add Address
            </Button>
          </DialogActions>
        </Dialog>

        {/* Edit Address Dialog */}
        <Dialog open={editAddressOpen} onClose={() => setEditAddressOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Edit Address</DialogTitle>
          <DialogContent>
            {selectedAddress && (
              <>
                <TextField
                  fullWidth
                  label="Street Address"
                  value={selectedAddress.street}
                  onChange={(e) => setSelectedAddress({ ...selectedAddress, street: e.target.value })}
                  margin="normal"
                  multiline
                  rows={2}
                  required
                />
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="City"
                      value={selectedAddress.city}
                      onChange={(e) => setSelectedAddress({ ...selectedAddress, city: e.target.value })}
                      margin="normal"
                      required
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="State"
                      value={selectedAddress.state}
                      onChange={(e) => setSelectedAddress({ ...selectedAddress, state: e.target.value })}
                      margin="normal"
                      required
                    />
                  </Grid>
                </Grid>
                <TextField
                  fullWidth
                  label="Pincode"
                  value={selectedAddress.pincode}
                  onChange={(e) => setSelectedAddress({ ...selectedAddress, pincode: e.target.value })}
                  margin="normal"
                  required
                />
                <FormControl fullWidth margin="normal">
                  <InputLabel>Address Label</InputLabel>
                  <Select
                    value={selectedAddress.label}
                    onChange={(e) => setSelectedAddress({ ...selectedAddress, label: e.target.value })}
                    label="Address Label"
                  >
                    <MenuItem value="Home">Home</MenuItem>
                    <MenuItem value="Office">Office</MenuItem>
                    <MenuItem value="Work">Work</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </Select>
                </FormControl>
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditAddressOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleEditAddress} 
              variant="contained"
              disabled={!selectedAddress?.street || !selectedAddress?.city || !selectedAddress?.state || !selectedAddress?.pincode}
            >
              Update Address
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </BuyerLayout>
  );
};

export default BuyerAddressesPage;
