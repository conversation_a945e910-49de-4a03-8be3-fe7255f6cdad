import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tabs,
  Tab
} from '@mui/material';
import {
  ShoppingBag,
  Search,
  Receipt,
  LocalShipping,
  CheckCircle,
  Cancel,
  Pending,
  FilterList
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import BuyerLayout from '../components/dashboard/BuyerLayout';

const BuyerOrdersPage = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [orderDetailsOpen, setOrderDetailsOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadOrders();
  }, []);

  useEffect(() => {
    filterOrders();
  }, [orders, searchTerm, statusFilter]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // const response = await orderService.getBuyerOrders();
      
      // Mock data for now
      const mockOrders = [
        {
          id: 'ORD001',
          orderId: 'SC-2025-001',
          productName: 'Premium Wireless Headphones',
          amount: 2999,
          status: 'delivered',
          date: '2025-01-15',
          seller: 'TechStore',
          quantity: 1,
          image: '/api/placeholder/60/60',
          trackingId: 'TRK123456789'
        },
        {
          id: 'ORD002',
          orderId: 'SC-2025-002',
          productName: 'Wireless Gaming Mouse',
          amount: 1299,
          status: 'shipped',
          date: '2025-01-10',
          seller: 'GamerHub',
          quantity: 1,
          image: '/api/placeholder/60/60',
          trackingId: 'TRK987654321'
        },
        {
          id: 'ORD003',
          orderId: 'SC-2025-003',
          productName: 'Bluetooth Speaker',
          amount: 899,
          status: 'processing',
          date: '2025-01-08',
          seller: 'AudioWorld',
          quantity: 2,
          image: '/api/placeholder/60/60'
        },
        {
          id: 'ORD004',
          orderId: 'SC-2025-004',
          productName: 'Phone Case',
          amount: 299,
          status: 'pending',
          date: '2025-01-05',
          seller: 'AccessoryShop',
          quantity: 1,
          image: '/api/placeholder/60/60'
        }
      ];
      
      setOrders(mockOrders);
    } catch (error) {
      console.error('Failed to load orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterOrders = () => {
    let filtered = orders;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.seller.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'shipped': return 'info';
      case 'processing': return 'warning';
      case 'pending': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered': return <CheckCircle />;
      case 'shipped': return <LocalShipping />;
      case 'processing': return <Pending />;
      case 'pending': return <Receipt />;
      case 'cancelled': return <Cancel />;
      default: return <Receipt />;
    }
  };

  const handleOrderClick = (order) => {
    setSelectedOrder(order);
    setOrderDetailsOpen(true);
  };

  const getOrderCounts = () => {
    return {
      all: orders.length,
      pending: orders.filter(o => o.status === 'pending').length,
      processing: orders.filter(o => o.status === 'processing').length,
      shipped: orders.filter(o => o.status === 'shipped').length,
      delivered: orders.filter(o => o.status === 'delivered').length,
      cancelled: orders.filter(o => o.status === 'cancelled').length,
    };
  };

  const orderCounts = getOrderCounts();

  if (loading) {
    return (
      <BuyerLayout>
        <Container>
          <Typography>Loading orders...</Typography>
        </Container>
      </BuyerLayout>
    );
  }

  return (
    <BuyerLayout>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
            My Orders
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Track and manage your orders
          </Typography>
        </Box>

        {/* Search and Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Search orders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Tabs
                  value={statusFilter}
                  onChange={(e, newValue) => setStatusFilter(newValue)}
                  variant="scrollable"
                  scrollButtons="auto"
                >
                  <Tab label={`All (${orderCounts.all})`} value="all" />
                  <Tab label={`Pending (${orderCounts.pending})`} value="pending" />
                  <Tab label={`Processing (${orderCounts.processing})`} value="processing" />
                  <Tab label={`Shipped (${orderCounts.shipped})`} value="shipped" />
                  <Tab label={`Delivered (${orderCounts.delivered})`} value="delivered" />
                </Tabs>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Orders List */}
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent>
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <ShoppingBag sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No orders found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'You haven\'t placed any orders yet'
                  }
                </Typography>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Grid container spacing={2}>
            {filteredOrders.map((order) => (
              <Grid item xs={12} key={order.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { boxShadow: 4 },
                    transition: 'box-shadow 0.2s'
                  }}
                  onClick={() => handleOrderClick(order)}
                >
                  <CardContent>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={2}>
                        <Avatar
                          src={order.image}
                          sx={{ width: 60, height: 60 }}
                          variant="rounded"
                        >
                          <ShoppingBag />
                        </Avatar>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="h6" gutterBottom>
                          {order.productName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Order #{order.orderId}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Seller: {order.seller}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Typography variant="body2" color="text.secondary">
                          Quantity
                        </Typography>
                        <Typography variant="body1" fontWeight={600}>
                          {order.quantity}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Typography variant="body2" color="text.secondary">
                          Amount
                        </Typography>
                        <Typography variant="h6" fontWeight={600}>
                          ₹{order.amount.toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Chip
                          label={order.status}
                          color={getStatusColor(order.status)}
                          icon={getStatusIcon(order.status)}
                          sx={{ mb: 1 }}
                        />
                        <Typography variant="body2" color="text.secondary">
                          {order.date}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Order Details Dialog */}
        <Dialog 
          open={orderDetailsOpen} 
          onClose={() => setOrderDetailsOpen(false)}
          maxWidth="md"
          fullWidth
        >
          {selectedOrder && (
            <>
              <DialogTitle>
                Order Details - #{selectedOrder.orderId}
              </DialogTitle>
              <DialogContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>
                      Product Information
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      <strong>Product:</strong> {selectedOrder.productName}
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      <strong>Seller:</strong> {selectedOrder.seller}
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      <strong>Quantity:</strong> {selectedOrder.quantity}
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      <strong>Amount:</strong> ₹{selectedOrder.amount.toLocaleString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>
                      Order Status
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Chip
                        label={selectedOrder.status}
                        color={getStatusColor(selectedOrder.status)}
                        icon={getStatusIcon(selectedOrder.status)}
                        size="large"
                      />
                    </Box>
                    <Typography variant="body1" gutterBottom>
                      <strong>Order Date:</strong> {selectedOrder.date}
                    </Typography>
                    {selectedOrder.trackingId && (
                      <Typography variant="body1" gutterBottom>
                        <strong>Tracking ID:</strong> {selectedOrder.trackingId}
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOrderDetailsOpen(false)}>
                  Close
                </Button>
                {selectedOrder.trackingId && (
                  <Button variant="contained">
                    Track Order
                  </Button>
                )}
              </DialogActions>
            </>
          )}
        </Dialog>
      </Container>
    </BuyerLayout>
  );
};

export default BuyerOrdersPage;
