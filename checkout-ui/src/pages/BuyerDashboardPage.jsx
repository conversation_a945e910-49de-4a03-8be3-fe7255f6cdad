import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert
} from '@mui/material';
import {
  ShoppingBag,
  LocationOn,
  Phone,
  Email,
  Edit,
  Add,
  Home,
  Business,
  Star,
  TrendingUp,
  Receipt,
  LocalShipping
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const BuyerDashboardPage = () => {
  const { user } = useAuth();
  const [addresses, setAddresses] = useState([]);
  const [orders, setOrders] = useState([]);
  const [editProfileOpen, setEditProfileOpen] = useState(false);
  const [addAddressOpen, setAddAddressOpen] = useState(false);
  const [profileData, setProfileData] = useState({
    name: '',
    email: ''
  });
  const [newAddress, setNewAddress] = useState({
    street: '',
    city: '',
    state: '',
    pincode: '',
    label: 'Home'
  });

  useEffect(() => {
    if (user) {
      setAddresses(user.addresses || []);
      setProfileData({
        name: user.name || '',
        email: user.email || ''
      });
      // Load orders - this would be from an API call
      loadOrders();
    }
  }, [user]);

  const loadOrders = async () => {
    // Mock orders data - replace with actual API call
    setOrders([
      {
        id: 'ORD001',
        productName: 'Premium Headphones',
        amount: 2999,
        status: 'delivered',
        date: '2025-01-15',
        image: '/api/placeholder/60/60'
      },
      {
        id: 'ORD002', 
        productName: 'Wireless Mouse',
        amount: 1299,
        status: 'shipped',
        date: '2025-01-10',
        image: '/api/placeholder/60/60'
      }
    ]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'shipped': return 'info';
      case 'processing': return 'warning';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered': return <Receipt />;
      case 'shipped': return <LocalShipping />;
      case 'processing': return <TrendingUp />;
      default: return <ShoppingBag />;
    }
  };

  const handleProfileUpdate = () => {
    // Update profile logic
    setEditProfileOpen(false);
  };

  const handleAddAddress = () => {
    // Add address logic
    setAddresses([...addresses, { ...newAddress, id: Date.now() }]);
    setNewAddress({ street: '', city: '', state: '', pincode: '', label: 'Home' });
    setAddAddressOpen(false);
  };

  if (!user) {
    return (
      <Container>
        <Alert severity="error">Please log in to view your dashboard</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
          Welcome back, {user.name}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your orders, addresses, and profile
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Profile Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ width: 60, height: 60, mr: 2, bgcolor: 'primary.main' }}>
                  {user.name?.charAt(0) || 'B'}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6">{user.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {user.email}
                  </Typography>
                  <Chip 
                    label={user.verification?.phoneVerified ? 'Verified' : 'Unverified'} 
                    color={user.verification?.phoneVerified ? 'success' : 'warning'}
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>
                <IconButton onClick={() => setEditProfileOpen(true)}>
                  <Edit />
                </IconButton>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Total Orders</Typography>
                <Typography variant="body2" fontWeight={600}>
                  {user.profile?.totalOrders || 0}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Total Spent</Typography>
                <Typography variant="body2" fontWeight={600}>
                  ₹{user.profile?.totalSpent || 0}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">Loyalty Points</Typography>
                <Typography variant="body2" fontWeight={600} color="primary.main">
                  {user.profile?.loyaltyPoints || 0} <Star sx={{ fontSize: 16, ml: 0.5 }} />
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Orders */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Recent Orders</Typography>
                <Button variant="outlined" size="small">View All</Button>
              </Box>

              {orders.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <ShoppingBag sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No orders yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Start shopping to see your orders here
                  </Typography>
                </Box>
              ) : (
                <List>
                  {orders.map((order, index) => (
                    <React.Fragment key={order.id}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Avatar src={order.image} sx={{ width: 40, height: 40 }}>
                            <ShoppingBag />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={order.productName}
                          secondary={`Order #${order.id} • ${order.date}`}
                        />
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{order.amount.toLocaleString()}
                          </Typography>
                          <Chip
                            label={order.status}
                            color={getStatusColor(order.status)}
                            size="small"
                            icon={getStatusIcon(order.status)}
                          />
                        </Box>
                      </ListItem>
                      {index < orders.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Saved Addresses */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Saved Addresses</Typography>
                <Button 
                  variant="outlined" 
                  startIcon={<Add />}
                  onClick={() => setAddAddressOpen(true)}
                >
                  Add Address
                </Button>
              </Box>

              {addresses.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <LocationOn sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No addresses saved
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Add an address for faster checkout
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  {addresses.map((address) => (
                    <Grid item xs={12} md={6} key={address.id}>
                      <Paper sx={{ p: 2, border: address.isDefault ? '2px solid' : '1px solid', borderColor: address.isDefault ? 'primary.main' : 'divider' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {address.label === 'Home' ? <Home sx={{ mr: 1 }} /> : <Business sx={{ mr: 1 }} />}
                            <Typography variant="subtitle2">{address.label}</Typography>
                          </Box>
                          {address.isDefault && (
                            <Chip label="Default" color="primary" size="small" />
                          )}
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {address.street}<br />
                          {address.city}, {address.state}<br />
                          {address.pincode}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Edit Profile Dialog */}
      <Dialog open={editProfileOpen} onClose={() => setEditProfileOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Profile</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Name"
            value={profileData.name}
            onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Email"
            type="email"
            value={profileData.email}
            onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditProfileOpen(false)}>Cancel</Button>
          <Button onClick={handleProfileUpdate} variant="contained">Update</Button>
        </DialogActions>
      </Dialog>

      {/* Add Address Dialog */}
      <Dialog open={addAddressOpen} onClose={() => setAddAddressOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Address</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Street Address"
            value={newAddress.street}
            onChange={(e) => setNewAddress({ ...newAddress, street: e.target.value })}
            margin="normal"
            multiline
            rows={2}
          />
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="City"
                value={newAddress.city}
                onChange={(e) => setNewAddress({ ...newAddress, city: e.target.value })}
                margin="normal"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="State"
                value={newAddress.state}
                onChange={(e) => setNewAddress({ ...newAddress, state: e.target.value })}
                margin="normal"
              />
            </Grid>
          </Grid>
          <TextField
            fullWidth
            label="Pincode"
            value={newAddress.pincode}
            onChange={(e) => setNewAddress({ ...newAddress, pincode: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Label (Home, Office, etc.)"
            value={newAddress.label}
            onChange={(e) => setNewAddress({ ...newAddress, label: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddAddressOpen(false)}>Cancel</Button>
          <Button onClick={handleAddAddress} variant="contained">Add Address</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BuyerDashboardPage;
