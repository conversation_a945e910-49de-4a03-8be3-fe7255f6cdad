import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  IconButton,
  Alert
} from '@mui/material';
import {
  ShoppingBag,
  LocationOn,
  Star,
  TrendingUp,
  Receipt,
  LocalShipping,
  Add
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import BuyerLayout from '../components/dashboard/BuyerLayout';

const BuyerDashboardPage = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);

  useEffect(() => {
    if (user) {
      // Load orders - this would be from an API call
      loadOrders();
    }
  }, [user]);

  const loadOrders = async () => {
    // Mock orders data - replace with actual API call
    setOrders([
      {
        id: 'ORD001',
        productName: 'Premium Headphones',
        amount: 2999,
        status: 'delivered',
        date: '2025-01-15',
        image: '/api/placeholder/60/60'
      },
      {
        id: 'ORD002', 
        productName: 'Wireless Mouse',
        amount: 1299,
        status: 'shipped',
        date: '2025-01-10',
        image: '/api/placeholder/60/60'
      }
    ]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'shipped': return 'info';
      case 'processing': return 'warning';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered': return <Receipt />;
      case 'shipped': return <LocalShipping />;
      case 'processing': return <TrendingUp />;
      default: return <ShoppingBag />;
    }
  };



  if (!user) {
    return (
      <BuyerLayout>
        <Container>
          <Alert severity="error">Please log in to view your dashboard</Alert>
        </Container>
      </BuyerLayout>
    );
  }

  return (
    <BuyerLayout>
      <Container maxWidth="lg">
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
          Welcome back, {user.name}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your orders, addresses, and profile
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Profile Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ width: 60, height: 60, mr: 2, bgcolor: 'primary.main' }}>
                  {user.name?.charAt(0) || 'B'}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6">{user.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {user.email}
                  </Typography>
                  <Chip
                    label={user.verification?.phoneVerified ? 'Verified' : 'Unverified'}
                    color={user.verification?.phoneVerified ? 'success' : 'warning'}
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Total Orders</Typography>
                <Typography variant="body2" fontWeight={600}>
                  {user.profile?.totalOrders || 0}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Total Spent</Typography>
                <Typography variant="body2" fontWeight={600}>
                  ₹{user.profile?.totalSpent || 0}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">Loyalty Points</Typography>
                <Typography variant="body2" fontWeight={600} color="primary.main">
                  {user.profile?.loyaltyPoints || 0} <Star sx={{ fontSize: 16, ml: 0.5 }} />
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Orders */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Recent Orders</Typography>
                <Button variant="outlined" size="small">View All</Button>
              </Box>

              {orders.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <ShoppingBag sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No orders yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Start shopping to see your orders here
                  </Typography>
                </Box>
              ) : (
                <List>
                  {orders.map((order, index) => (
                    <React.Fragment key={order.id}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Avatar src={order.image} sx={{ width: 40, height: 40 }}>
                            <ShoppingBag />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={order.productName}
                          secondary={`Order #${order.id} • ${order.date}`}
                        />
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="body2" fontWeight={600}>
                            ₹{order.amount.toLocaleString()}
                          </Typography>
                          <Chip
                            label={order.status}
                            color={getStatusColor(order.status)}
                            size="small"
                            icon={getStatusIcon(order.status)}
                          />
                        </Box>
                      </ListItem>
                      {index < orders.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

      </Grid>
      </Container>
    </BuyerLayout>
  );
};

export default BuyerDashboardPage;
