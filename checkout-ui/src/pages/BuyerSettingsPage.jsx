import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Notifications,
  Security,
  Payment,
  Language,
  DarkMode,
  Delete,
  Logout,
  Help,
  Info,
  Warning
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import BuyerLayout from '../components/dashboard/BuyerLayout';

const BuyerSettingsPage = () => {
  const { user, logout } = useAuth();
  const [settings, setSettings] = useState({
    notifications: {
      orderUpdates: true,
      promotions: false,
      newsletter: false,
      sms: true,
      email: true,
      push: true
    },
    preferences: {
      theme: 'light',
      language: 'en',
      currency: 'INR',
      defaultPayment: 'cod'
    },
    privacy: {
      profileVisible: false,
      shareData: false,
      analytics: true
    }
  });
  const [deleteAccountOpen, setDeleteAccountOpen] = useState(false);
  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);
  const [message, setMessage] = useState('');

  const handleSettingChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value
      }
    }));
    
    // Auto-save settings
    saveSettings(category, setting, value);
  };

  const saveSettings = async (category, setting, value) => {
    try {
      // TODO: Replace with actual API call
      // await settingsService.updateSetting(category, setting, value);
      
      setMessage('Settings updated successfully!');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('Failed to save settings:', error);
      setMessage('Failed to save settings. Please try again.');
    }
  };

  const handleDeleteAccount = async () => {
    try {
      // TODO: Replace with actual API call
      // await accountService.deleteAccount();
      
      logout();
      setDeleteAccountOpen(false);
    } catch (error) {
      console.error('Failed to delete account:', error);
      setMessage('Failed to delete account. Please try again.');
    }
  };

  const handleLogout = () => {
    logout();
    setLogoutConfirmOpen(false);
  };

  return (
    <BuyerLayout>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
            Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your account preferences and settings
          </Typography>
        </Box>

        {message && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {message}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Notification Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Notifications sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography variant="h6">Notifications</Typography>
                </Box>

                <List>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Order Updates"
                      secondary="Get notified about order status changes"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.orderUpdates}
                          onChange={(e) => handleSettingChange('notifications', 'orderUpdates', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Promotions & Offers"
                      secondary="Receive promotional notifications"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.promotions}
                          onChange={(e) => handleSettingChange('notifications', 'promotions', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Newsletter"
                      secondary="Weekly newsletter with updates"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.newsletter}
                          onChange={(e) => handleSettingChange('notifications', 'newsletter', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="SMS Notifications"
                      secondary="Receive notifications via SMS"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.sms}
                          onChange={(e) => handleSettingChange('notifications', 'sms', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Email Notifications"
                      secondary="Receive notifications via email"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.email}
                          onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* App Preferences */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Language sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography variant="h6">App Preferences</Typography>
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Theme</InputLabel>
                      <Select
                        value={settings.preferences.theme}
                        onChange={(e) => handleSettingChange('preferences', 'theme', e.target.value)}
                        label="Theme"
                      >
                        <MenuItem value="light">Light</MenuItem>
                        <MenuItem value="dark">Dark</MenuItem>
                        <MenuItem value="auto">Auto</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Language</InputLabel>
                      <Select
                        value={settings.preferences.language}
                        onChange={(e) => handleSettingChange('preferences', 'language', e.target.value)}
                        label="Language"
                      >
                        <MenuItem value="en">English</MenuItem>
                        <MenuItem value="hi">Hindi</MenuItem>
                        <MenuItem value="ta">Tamil</MenuItem>
                        <MenuItem value="te">Telugu</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Currency</InputLabel>
                      <Select
                        value={settings.preferences.currency}
                        onChange={(e) => handleSettingChange('preferences', 'currency', e.target.value)}
                        label="Currency"
                      >
                        <MenuItem value="INR">Indian Rupee (₹)</MenuItem>
                        <MenuItem value="USD">US Dollar ($)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Default Payment Method</InputLabel>
                      <Select
                        value={settings.preferences.defaultPayment}
                        onChange={(e) => handleSettingChange('preferences', 'defaultPayment', e.target.value)}
                        label="Default Payment Method"
                      >
                        <MenuItem value="cod">Cash on Delivery</MenuItem>
                        <MenuItem value="upi">UPI</MenuItem>
                        <MenuItem value="card">Credit/Debit Card</MenuItem>
                        <MenuItem value="wallet">Digital Wallet</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Privacy Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Security sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography variant="h6">Privacy & Security</Typography>
                </Box>

                <List>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Profile Visibility"
                      secondary="Make your profile visible to sellers"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.privacy.profileVisible}
                          onChange={(e) => handleSettingChange('privacy', 'profileVisible', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Share Usage Data"
                      secondary="Help improve the app by sharing usage data"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.privacy.shareData}
                          onChange={(e) => handleSettingChange('privacy', 'shareData', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Analytics"
                      secondary="Allow analytics for better experience"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.privacy.analytics}
                          onChange={(e) => handleSettingChange('privacy', 'analytics', e.target.checked)}
                        />
                      }
                      label=""
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Account Actions */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Security sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography variant="h6">Account Actions</Typography>
                </Box>

                <List>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Help />
                    </ListItemIcon>
                    <ListItemText
                      primary="Help & Support"
                      secondary="Get help with your account"
                    />
                    <Button variant="outlined" size="small">
                      Contact
                    </Button>
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Info />
                    </ListItemIcon>
                    <ListItemText
                      primary="About"
                      secondary="App version and information"
                    />
                    <Button variant="outlined" size="small">
                      View
                    </Button>
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Logout />
                    </ListItemIcon>
                    <ListItemText
                      primary="Logout"
                      secondary="Sign out of your account"
                    />
                    <Button 
                      variant="outlined" 
                      size="small"
                      onClick={() => setLogoutConfirmOpen(true)}
                    >
                      Logout
                    </Button>
                  </ListItem>
                  <Divider />
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Delete color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Delete Account"
                      secondary="Permanently delete your account"
                    />
                    <Button 
                      variant="outlined" 
                      color="error" 
                      size="small"
                      onClick={() => setDeleteAccountOpen(true)}
                    >
                      Delete
                    </Button>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Delete Account Confirmation */}
        <Dialog open={deleteAccountOpen} onClose={() => setDeleteAccountOpen(false)}>
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Warning color="error" sx={{ mr: 1 }} />
              Delete Account
            </Box>
          </DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete your account? This action cannot be undone.
              All your data, orders, and addresses will be permanently removed.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteAccountOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleDeleteAccount} color="error" variant="contained">
              Delete Account
            </Button>
          </DialogActions>
        </Dialog>

        {/* Logout Confirmation */}
        <Dialog open={logoutConfirmOpen} onClose={() => setLogoutConfirmOpen(false)}>
          <DialogTitle>Logout</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to logout?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setLogoutConfirmOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleLogout} variant="contained">
              Logout
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </BuyerLayout>
  );
};

export default BuyerSettingsPage;
