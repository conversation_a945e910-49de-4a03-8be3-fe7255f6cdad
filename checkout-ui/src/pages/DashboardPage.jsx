import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Avatar,
  IconButton,
  LinearProgress,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  ShoppingCart,
  Inventory,
  AttachMoney,
  MoreVert,
  ArrowUpward,
  ArrowDownward,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import DashboardLayout from '../components/dashboard/DashboardLayout';

// Mock data
const stats = [
  {
    title: 'Total Revenue',
    value: '₹1,24,500',
    change: '+12.5%',
    trend: 'up',
    icon: AttachMoney,
    color: '#22c55e',
  },
  {
    title: 'Total Orders',
    value: '1,234',
    change: '+8.2%',
    trend: 'up',
    icon: ShoppingCart,
    color: '#3b82f6',
  },
  {
    title: 'Products',
    value: '45',
    change: '+2',
    trend: 'up',
    icon: Inventory,
    color: '#f59e0b',
  },
  {
    title: 'Conversion Rate',
    value: '3.2%',
    change: '-0.5%',
    trend: 'down',
    icon: TrendingUp,
    color: '#ef4444',
  },
];

const recentOrders = [
  {
    id: 'ORD-001',
    customer: '<PERSON><PERSON>',
    product: 'Premium T-Shirt',
    amount: '₹1,299',
    status: 'completed',
    time: '2 hours ago',
  },
  {
    id: 'ORD-002',
    customer: 'Priya Patel',
    product: 'Wireless Headphones',
    amount: '₹2,999',
    status: 'processing',
    time: '4 hours ago',
  },
  {
    id: 'ORD-003',
    customer: 'Amit Kumar',
    product: 'Smart Watch',
    amount: '₹8,999',
    status: 'shipped',
    time: '6 hours ago',
  },
  {
    id: 'ORD-004',
    customer: 'Sneha Reddy',
    product: 'Running Shoes',
    amount: '₹4,499',
    status: 'pending',
    time: '8 hours ago',
  },
];

const getStatusColor = (status) => {
  switch (status) {
    case 'completed':
      return 'success';
    case 'processing':
      return 'warning';
    case 'shipped':
      return 'info';
    case 'pending':
      return 'default';
    default:
      return 'default';
  }
};

const StatCard = ({ stat, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
  >
    <Card
      sx={{
        height: '100%',
        background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
        backdropFilter: 'blur(20px)',
        border: '1px solid rgba(255,255,255,0.2)',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
        },
        transition: 'all 0.3s ease-in-out',
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {stat.title}
            </Typography>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
              {stat.value}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              {stat.trend === 'up' ? (
                <ArrowUpward sx={{ fontSize: 16, color: 'success.main' }} />
              ) : (
                <ArrowDownward sx={{ fontSize: 16, color: 'error.main' }} />
              )}
              <Typography
                variant="body2"
                sx={{
                  color: stat.trend === 'up' ? 'success.main' : 'error.main',
                  fontWeight: 600,
                }}
              >
                {stat.change}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                vs last month
              </Typography>
            </Box>
          </Box>
          <Avatar
            sx={{
              backgroundColor: stat.color,
              width: 56,
              height: 56,
            }}
          >
            <stat.icon />
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  </motion.div>
);

const DashboardPage = () => {
  return (
    <DashboardLayout>
      <Box>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Welcome back! Here's what's happening with your store today.
          </Typography>
        </motion.div>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} lg={3} key={stat.title}>
              <StatCard stat={stat} index={index} />
            </Grid>
          ))}
        </Grid>

        {/* Recent Orders */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card
            sx={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255,255,255,0.2)',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Recent Orders
                </Typography>
                <IconButton size="small">
                  <MoreVert />
                </IconButton>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {recentOrders.map((order, index) => (
                  <motion.div
                    key={order.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 2,
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                        border: '1px solid',
                        borderColor: 'divider',
                        '&:hover': {
                          backgroundColor: 'action.hover',
                        },
                        transition: 'all 0.2s ease-in-out',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ width: 40, height: 40 }}>
                          {order.customer.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {order.id}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {order.customer} • {order.product}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Chip
                          label={order.status}
                          color={getStatusColor(order.status)}
                          size="small"
                          sx={{ textTransform: 'capitalize' }}
                        />
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {order.amount}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {order.time}
                        </Typography>
                      </Box>
                    </Box>
                  </motion.div>
                ))}
              </Box>
            </CardContent>
          </Card>
        </motion.div>
      </Box>
    </DashboardLayout>
  );
};

export default DashboardPage;
