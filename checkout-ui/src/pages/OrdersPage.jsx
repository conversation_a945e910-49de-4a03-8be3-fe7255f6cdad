import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Button,
  Chip,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Tabs,
  Tab,
  Badge,
  Tooltip,
  Alert,
  Skeleton,
  Pagination,
} from '@mui/material';
import {
  Search,
  FilterList,
  MoreVert,
  Visibility,
  Edit,
  LocalShipping,
  CheckCircle,
  Cancel,
  Receipt,
  Email,
  Download,
  Refresh,
  TrendingUp,
  TrendingDown,
  AttachMoney,
  ShoppingCart,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import { orderService } from '../services/orderService';

// Order status configurations
const ORDER_STATUSES = {
  pending: { label: 'Pending', color: 'warning', icon: ShoppingCart },
  confirmed: { label: 'Confirmed', color: 'info', icon: CheckCircle },
  processing: { label: 'Processing', color: 'primary', icon: Edit },
  shipped: { label: 'Shipped', color: 'secondary', icon: LocalShipping },
  delivered: { label: 'Delivered', color: 'success', icon: CheckCircle },
  cancelled: { label: 'Cancelled', color: 'error', icon: Cancel },
  refunded: { label: 'Refunded', color: 'default', icon: AttachMoney },
};

const OrdersPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showStatusUpdate, setShowStatusUpdate] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [statusNotes, setStatusNotes] = useState('');
  const [page, setPage] = useState(1);

  const queryClient = useQueryClient();

  // Mock data for testing
  const mockOrders = [
    {
      id: 'ORD-001',
      customerName: 'Rahul Sharma',
      customerEmail: '<EMAIL>',
      customerPhone: '+919876543210',
      productName: 'Premium T-Shirt',
      quantity: 2,
      amount: 2598,
      status: 'confirmed',
      paymentStatus: 'paid',
      createdAt: new Date('2024-01-15T10:30:00'),
      updatedAt: new Date('2024-01-15T11:00:00'),
      shippingAddress: {
        street: '123 Main Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400001',
      },
    },
    {
      id: 'ORD-002',
      customerName: 'Priya Patel',
      customerEmail: '<EMAIL>',
      customerPhone: '+919876543211',
      productName: 'Wireless Headphones',
      quantity: 1,
      amount: 4999,
      status: 'shipped',
      paymentStatus: 'paid',
      createdAt: new Date('2024-01-14T15:20:00'),
      updatedAt: new Date('2024-01-15T09:15:00'),
      trackingNumber: 'TRK123456789',
      shippingAddress: {
        street: '456 Park Avenue',
        city: 'Delhi',
        state: 'Delhi',
        pincode: '110001',
      },
    },
    {
      id: 'ORD-003',
      customerName: 'Amit Kumar',
      customerEmail: '<EMAIL>',
      customerPhone: '+919876543212',
      productName: 'Smart Watch',
      quantity: 1,
      amount: 8999,
      status: 'delivered',
      paymentStatus: 'paid',
      createdAt: new Date('2024-01-13T12:45:00'),
      updatedAt: new Date('2024-01-14T16:30:00'),
      deliveredAt: new Date('2024-01-14T16:30:00'),
      shippingAddress: {
        street: '789 Tech Street',
        city: 'Bangalore',
        state: 'Karnataka',
        pincode: '560001',
      },
    },
    {
      id: 'ORD-004',
      customerName: 'Sneha Reddy',
      customerEmail: '<EMAIL>',
      customerPhone: '+919876543213',
      productName: 'Running Shoes',
      quantity: 1,
      amount: 3499,
      status: 'pending',
      paymentStatus: 'pending',
      createdAt: new Date('2024-01-15T14:20:00'),
      updatedAt: new Date('2024-01-15T14:20:00'),
      shippingAddress: {
        street: '321 Sports Complex',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600001',
      },
    },
  ];

  // Fetch orders
  const {
    data: ordersData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['orders', page, searchTerm, statusFilter, dateFilter],
    queryFn: () => orderService.getOrders(page, 10, {
      search: searchTerm,
      status: statusFilter !== 'all' ? statusFilter : undefined,
      dateRange: dateFilter !== 'all' ? dateFilter : undefined,
    }),
    keepPreviousData: true,
    initialData: {
      orders: mockOrders.filter(order => {
        const customerName = order.customerName || order.buyer?.name || '';
        const productName = order.productName || order.product?.name || '';
        const orderId = order.id || order.orderId || '';

        const matchesSearch = !searchTerm ||
          customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
          productName.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

        return matchesSearch && matchesStatus;
      }),
      total: mockOrders.length,
      page: 1,
      totalPages: 1,
    },
  });

  // Update order status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ orderId, status, notes }) =>
      orderService.updateOrderStatus(orderId, status, notes),
    onSuccess: () => {
      queryClient.invalidateQueries(['orders']);
      setShowStatusUpdate(false);
      setSelectedOrder(null);
      setNewStatus('');
      setStatusNotes('');
    },
  });

  const orders = ordersData?.orders || [];
  const totalOrders = ordersData?.total || 0;

  // Calculate order statistics
  const orderStats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    confirmed: orders.filter(o => o.status === 'confirmed').length,
    shipped: orders.filter(o => o.status === 'shipped').length,
    delivered: orders.filter(o => o.status === 'delivered').length,
    totalRevenue: orders.reduce((sum, order) => sum + order.amount, 0),
  };

  const handleMenuOpen = (event, order) => {
    setAnchorEl(event.currentTarget);
    setSelectedOrder(order);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedOrder(null);
  };

  const handleViewOrder = () => {
    setShowOrderDetails(true);
    handleMenuClose();
  };

  const handleUpdateStatus = () => {
    setShowStatusUpdate(true);
    setNewStatus(selectedOrder?.status || '');
    handleMenuClose();
  };

  const handleStatusUpdate = () => {
    if (selectedOrder && newStatus) {
      updateStatusMutation.mutate({
        orderId: selectedOrder.id,
        status: newStatus,
        notes: statusNotes,
      });
    }
  };

  const getStatusChip = (status) => {
    const config = ORDER_STATUSES[status] || ORDER_STATUSES.pending;
    const Icon = config.icon;

    return (
      <Chip
        icon={<Icon fontSize="small" />}
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  };

  return (
    <DashboardLayout>
      <Box>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                Orders
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Track and manage your orders efficiently
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Download />}
                sx={{ borderRadius: 2 }}
              >
                Export
              </Button>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => refetch()}
                sx={{ borderRadius: 2 }}
              >
                Refresh
              </Button>
            </Box>
          </Box>
        </motion.div>

        {/* Order Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                    {orderStats.total}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Orders
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                    {orderStats.pending}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pending
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                    {orderStats.confirmed}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Confirmed
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                    {orderStats.shipped}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Shipped
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <Card sx={{ borderRadius: 2, textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                    {formatCurrency(orderStats.totalRevenue)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card sx={{ mb: 3, borderRadius: 2 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={4}>
                  <TextField
                    placeholder="Search orders, customers, products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      label="Status"
                    >
                      <MenuItem value="all">All Status</MenuItem>
                      {Object.entries(ORDER_STATUSES).map(([key, config]) => (
                        <MenuItem key={key} value={key}>
                          {config.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Date Range</InputLabel>
                    <Select
                      value={dateFilter}
                      onChange={(e) => setDateFilter(e.target.value)}
                      label="Date Range"
                    >
                      <MenuItem value="all">All Time</MenuItem>
                      <MenuItem value="today">Today</MenuItem>
                      <MenuItem value="week">This Week</MenuItem>
                      <MenuItem value="month">This Month</MenuItem>
                      <MenuItem value="quarter">This Quarter</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <Button
                    variant="outlined"
                    startIcon={<FilterList />}
                    fullWidth
                    sx={{ borderRadius: 2, height: 56 }}
                  >
                    More Filters
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error.message}
          </Alert>
        )}

        {/* Orders Table */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card sx={{ borderRadius: 2 }}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Order ID</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell>Product</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading ? (
                    [...Array(5)].map((_, index) => (
                      <TableRow key={index}>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                        <TableCell><Skeleton /></TableCell>
                      </TableRow>
                    ))
                  ) : orders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 8 }}>
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          No orders found
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {searchTerm || statusFilter !== 'all'
                            ? 'Try adjusting your search or filters'
                            : 'Orders will appear here once customers start purchasing'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    orders.map((order, index) => (
                      <motion.tr
                        key={order.id}
                        component={TableRow}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'action.hover',
                          },
                        }}
                      >
                        <TableCell>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {order.id}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ width: 32, height: 32 }}>
                              {(order.customerName || order.buyer?.name || 'U').charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2">
                                {order.customerName || order.buyer?.name || 'Unknown'}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {order.customerEmail || order.buyer?.email || 'No email'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2">
                              {order.productName || order.product?.name || 'Unknown Product'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Qty: {order.quantity || order.pricing?.quantity || 1}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {formatCurrency(order.amount || order.pricing?.total || 0)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {order.paymentStatus || order.payment?.status || 'pending'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {getStatusChip(order.status)}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {format(new Date(order.createdAt), 'MMM dd, yyyy')}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {format(new Date(order.createdAt), 'hh:mm a')}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            onClick={(e) => handleMenuOpen(e, order)}
                            size="small"
                          >
                            <MoreVert />
                          </IconButton>
                        </TableCell>
                      </motion.tr>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Pagination */}
            {orders.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <Pagination
                  count={Math.ceil(totalOrders / 10)}
                  page={page}
                  onChange={(e, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            )}
          </Card>
        </motion.div>

        {/* Order Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handleViewOrder}>
            <Visibility sx={{ mr: 1 }} fontSize="small" />
            View Details
          </MenuItem>
          <MenuItem onClick={handleUpdateStatus}>
            <Edit sx={{ mr: 1 }} fontSize="small" />
            Update Status
          </MenuItem>
          <MenuItem onClick={() => {/* Handle invoice */}}>
            <Receipt sx={{ mr: 1 }} fontSize="small" />
            Generate Invoice
          </MenuItem>
          <MenuItem onClick={() => {/* Handle notification */}}>
            <Email sx={{ mr: 1 }} fontSize="small" />
            Send Notification
          </MenuItem>
        </Menu>

        {/* Order Details Dialog */}
        <Dialog
          open={showOrderDetails}
          onClose={() => setShowOrderDetails(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Order Details - {selectedOrder?.id}
          </DialogTitle>
          <DialogContent>
            {selectedOrder && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                    Customer Information
                  </Typography>
                  <Typography variant="body2">Name: {selectedOrder.customerName || selectedOrder.buyer?.name || 'Unknown'}</Typography>
                  <Typography variant="body2">Email: {selectedOrder.customerEmail || selectedOrder.buyer?.email || 'No email'}</Typography>
                  <Typography variant="body2">Phone: {selectedOrder.customerPhone || selectedOrder.buyer?.phone || 'No phone'}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                    Order Information
                  </Typography>
                  <Typography variant="body2">Product: {selectedOrder.productName || selectedOrder.product?.name || 'Unknown Product'}</Typography>
                  <Typography variant="body2">Quantity: {selectedOrder.quantity || selectedOrder.pricing?.quantity || 1}</Typography>
                  <Typography variant="body2">Amount: {formatCurrency(selectedOrder.amount || selectedOrder.pricing?.total || 0)}</Typography>
                  <Typography variant="body2">Status: {getStatusChip(selectedOrder.status)}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                    Shipping Address
                  </Typography>
                  <Typography variant="body2">
                    {(selectedOrder.shippingAddress || selectedOrder.buyer?.address)?.street}<br />
                    {(selectedOrder.shippingAddress || selectedOrder.buyer?.address)?.city}, {(selectedOrder.shippingAddress || selectedOrder.buyer?.address)?.state}<br />
                    {(selectedOrder.shippingAddress || selectedOrder.buyer?.address)?.pincode}
                  </Typography>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowOrderDetails(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        {/* Status Update Dialog */}
        <Dialog
          open={showStatusUpdate}
          onClose={() => setShowStatusUpdate(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Update Order Status</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>New Status</InputLabel>
                <Select
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                  label="New Status"
                >
                  {Object.entries(ORDER_STATUSES).map(([key, config]) => (
                    <MenuItem key={key} value={key}>
                      {config.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <TextField
                label="Notes (Optional)"
                multiline
                rows={3}
                fullWidth
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                placeholder="Add any notes about this status update..."
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowStatusUpdate(false)}>Cancel</Button>
            <Button
              onClick={handleStatusUpdate}
              variant="contained"
              disabled={!newStatus || updateStatusMutation.isLoading}
            >
              {updateStatusMutation.isLoading ? 'Updating...' : 'Update Status'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </DashboardLayout>
  );
};

export default OrdersPage;
