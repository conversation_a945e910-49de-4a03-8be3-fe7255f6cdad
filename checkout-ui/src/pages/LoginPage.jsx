import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import { Phone, Verified } from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/authService';

// Validation schemas
const phoneSchema = yup.object({
  phone: yup
    .string()
    .required('Phone number is required')
    .min(10, 'Phone number must be at least 10 digits')
    .matches(/^\+\d{10,15}$/, 'Please enter a valid phone number with country code (+919876543210)'),
});

const otpSchema = yup.object({
  otp: yup
    .string()
    .required('OTP is required')
    .length(6, 'OTP must be 6 digits'),
  name: yup.string().when('$isNewUser', {
    is: true,
    then: (schema) => schema.required('Name is required').min(2, 'Name must be at least 2 characters'),
    otherwise: (schema) => schema.optional()
  }),
});

const LoginPage = () => {
  // Use sessionStorage to persist state across hot reloads
  const [step, setStep] = useState(() => {
    const saved = sessionStorage.getItem('loginStep');
    const initialStep = saved ? parseInt(saved) : 0;
    console.log('🔄 Loading step from sessionStorage:', saved, '→', initialStep);
    return initialStep;
  });
  const [phoneNumber, setPhoneNumber] = useState(() => {
    const saved = sessionStorage.getItem('loginPhone');
    console.log('📱 Loading phone from sessionStorage:', saved);
    return saved || '';
  });
  const [sessionInfo, setSessionInfo] = useState(() => {
    return sessionStorage.getItem('loginSession') || '';
  });
  const [userExists, setUserExists] = useState(null);
  const [existingUserInfo, setExistingUserInfo] = useState(null);
  const { checkUserType, requestOTP, verifyOTP, error, isLoading, clearError } = useAuth();

  // Persist step changes to sessionStorage
  React.useEffect(() => {
    sessionStorage.setItem('loginStep', step.toString());
  }, [step]);

  // Persist phone changes to sessionStorage
  React.useEffect(() => {
    if (phoneNumber) {
      sessionStorage.setItem('loginPhone', phoneNumber);
    }
  }, [phoneNumber]);

  // Persist session changes to sessionStorage
  React.useEffect(() => {
    if (sessionInfo) {
      sessionStorage.setItem('loginSession', sessionInfo);
    }
  }, [sessionInfo]);

  // Debug component mount/unmount
  React.useEffect(() => {
    console.log('🏗️ LoginPage component mounted');
    return () => {
      console.log('🏗️ LoginPage component unmounted');
    };
  }, []);

  // Debug step changes
  React.useEffect(() => {
    console.log('📊 Step changed to:', step);
  }, [step]);

  // Debug loading state changes
  React.useEffect(() => {
    console.log('⏳ Loading state changed to:', isLoading);
  }, [isLoading]);

  // Debug error state changes
  React.useEffect(() => {
    console.log('❌ Error state changed to:', error);
  }, [error]);

  const phoneForm = useForm({
    resolver: yupResolver(phoneSchema),
    defaultValues: { phone: '+91' },
  });

  const otpForm = useForm({
    resolver: yupResolver(otpSchema),
    defaultValues: { otp: '', name: '' },
    context: { isNewUser: userExists === false }
  });

  const steps = ['Enter Phone', 'Verify OTP'];

  const handlePhoneSubmit = React.useCallback(async (data, event) => {
    console.log('🚀 Form submitted with data:', data);

    // Prevent default form submission
    if (event) {
      event.preventDefault();
    }

    try {
      console.log('📱 Submitting phone:', data.phone);
      clearError();

      // First, check if user exists
      console.log('🔍 Checking user type...');
      const userTypeResponse = await checkUserType(data.phone);
      console.log('✅ User type response:', userTypeResponse);

      // Store user existence info
      setUserExists(userTypeResponse.exists);
      setExistingUserInfo(userTypeResponse.exists ? userTypeResponse.user : null);

      // Now request OTP
      const response = await requestOTP(data.phone);
      console.log('✅ OTP Response:', response);

      setPhoneNumber(data.phone);
      setSessionInfo(response.sessionInfo || 'fallback_session');

      console.log('🎯 About to change step to 1');

      // Save to sessionStorage immediately, then update state
      sessionStorage.setItem('loginStep', '1');
      sessionStorage.setItem('loginPhone', data.phone);
      sessionStorage.setItem('loginSession', response.sessionInfo || 'fallback_session');
      sessionStorage.setItem('userExists', userTypeResponse.exists.toString());
      sessionStorage.setItem('existingUserInfo', JSON.stringify(userTypeResponse.exists ? userTypeResponse.user : null));
      console.log('💾 Saved step 1, phone, session, and user info to sessionStorage');

      // Use setTimeout to ensure the step change happens after the current render cycle
      setTimeout(() => {
        console.log('🎯 Executing delayed step change');
        setStep(1);
      }, 0);

    } catch (error) {
      console.error('❌ Phone submission failed:', error);
      // Error is already handled by the context, just log it
    }
  }, [checkUserType, requestOTP, clearError]);

  const handlePhoneFormError = (errors) => {
    console.log('❌ Form validation failed with errors:', errors);
  };

  const handleOTPSubmit = async (data) => {
    try {
      console.log('🔐 OTP Form submitted with data:', data);
      console.log('📱 Phone number from state:', phoneNumber);
      console.log('📱 Phone number from sessionStorage:', sessionStorage.getItem('loginPhone'));

      clearError();

      // Use phone from sessionStorage as fallback, or from form default value
      const phone = phoneNumber ||
                   sessionStorage.getItem('loginPhone') ||
                   phoneForm.getValues('phone');
      console.log('📱 Using phone number:', phone);

      if (!phone) {
        throw new Error('Phone number is missing. Please start over.');
      }

      await verifyOTP(phone, data.otp, data.name);
    } catch (error) {
      console.error('❌ OTP verification failed:', error);
    }
  };

  const handleBack = () => {
    setStep(0);
    clearError();
  };

  const clearSession = () => {
    sessionStorage.removeItem('loginStep');
    sessionStorage.removeItem('loginPhone');
    sessionStorage.removeItem('loginSession');
    setStep(0);
    setPhoneNumber('');
    setSessionInfo('');
  };

  const testConnection = async () => {
    try {
      await authService.healthCheck();
      alert('✅ Backend connection successful!');
    } catch (error) {
      alert('❌ Backend connection failed: ' + error.message);
    }
  };

  const testOTPDirect = async () => {
    try {
      console.log('🧪 Testing OTP directly');
      const response = await requestOTP('+919876543210');
      console.log('✅ Direct OTP Response:', response);
      setPhoneNumber('+919876543210');
      setSessionInfo(response.sessionInfo || 'fallback_session');
      setStep(1);
      alert('✅ OTP sent! Moving to step 2');
    } catch (error) {
      console.error('❌ Direct OTP failed:', error);
      alert('❌ OTP failed: ' + error.message);
    }
  };

  const testStepChange = () => {
    console.log('🔄 Manually changing step from', step, 'to', step === 0 ? 1 : 0);
    setStep(step === 0 ? 1 : 0);
    setPhoneNumber('+919876543210');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: 2,
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
      >
        <Card
          sx={{
            maxWidth: 480,
            width: '100%',
            borderRadius: 3,
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                }}
              >
                SwiftCheckout
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Welcome back! Sign in to your account
              </Typography>

              {/* Debug: Test Buttons */}
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mt: 1, flexWrap: 'wrap' }}>
                <Button
                  onClick={testConnection}
                  variant="text"
                  size="small"
                  sx={{ fontSize: '0.75rem' }}
                >
                  Test Backend
                </Button>
                <Button
                  onClick={testOTPDirect}
                  variant="text"
                  size="small"
                  sx={{ fontSize: '0.75rem' }}
                >
                  Test OTP Direct
                </Button>
                <Button
                  onClick={testStepChange}
                  variant="text"
                  size="small"
                  sx={{ fontSize: '0.75rem' }}
                >
                  Toggle Step
                </Button>
                <Button
                  onClick={clearSession}
                  variant="text"
                  size="small"
                  sx={{ fontSize: '0.75rem' }}
                >
                  Clear Session
                </Button>
              </Box>
            </Box>

            {/* Stepper */}
            <Stepper activeStep={step} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Debug Info */}
            <Box sx={{ mb: 2, p: 1, backgroundColor: 'grey.100', borderRadius: 1, fontSize: '0.75rem' }}>
              <Typography variant="caption">
                Debug: Step={step}, Loading={isLoading}, Error={error ? 'Yes' : 'No'}, Phone={phoneNumber || 'empty'}
              </Typography>
            </Box>

            {/* Error Alert */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
                    {error}
                  </Alert>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Forms */}
            <AnimatePresence mode="wait">
              {step === 0 ? (
                <motion.div
                  key="phone"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      phoneForm.handleSubmit(handlePhoneSubmit, handlePhoneFormError)(e);
                    }}
                  >
                    <TextField
                      {...phoneForm.register('phone')}
                      fullWidth
                      label="Phone Number"
                      placeholder="+919876543210"
                      error={!!phoneForm.formState.errors.phone}
                      helperText={phoneForm.formState.errors.phone?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 3 }}
                    />
                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={isLoading}
                      sx={{
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        fontSize: '1rem',
                        fontWeight: 600,
                      }}
                    >
                      {isLoading ? 'Sending OTP...' : 'Send OTP'}
                    </Button>
                  </form>
                </motion.div>
              ) : (
                <motion.div
                  key="otp"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <form onSubmit={otpForm.handleSubmit(handleOTPSubmit)}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Enter the 6-digit code sent to {phoneNumber}
                    </Typography>

                    {/* Show existing user info if user exists */}
                    {userExists && existingUserInfo && (
                      <Box sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Welcome back!
                        </Typography>
                        <Typography variant="body1" fontWeight={600}>
                          {existingUserInfo.name}
                        </Typography>
                        {existingUserInfo.email && (
                          <Typography variant="body2" color="text.secondary">
                            {existingUserInfo.email}
                          </Typography>
                        )}
                      </Box>
                    )}

                    {/* Show name field only for new users */}
                    {userExists === false && (
                      <TextField
                        {...otpForm.register('name')}
                        fullWidth
                        label="Your Name"
                        placeholder="Enter your full name"
                        error={!!otpForm.formState.errors.name}
                        helperText={otpForm.formState.errors.name?.message}
                        sx={{ mb: 2 }}
                        required
                      />
                    )}

                    <TextField
                      {...otpForm.register('otp')}
                      fullWidth
                      label="OTP Code"
                      placeholder="123456"
                      error={!!otpForm.formState.errors.otp}
                      helperText={otpForm.formState.errors.otp?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Verified color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 3 }}
                    />

                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Button
                        onClick={handleBack}
                        variant="outlined"
                        size="large"
                        sx={{ flex: 1, py: 1.5, borderRadius: 2 }}
                      >
                        Back
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        disabled={isLoading}
                        sx={{
                          flex: 2,
                          py: 1.5,
                          borderRadius: 2,
                          textTransform: 'none',
                          fontSize: '1rem',
                          fontWeight: 600,
                        }}
                      >
                        {isLoading ? 'Verifying...' : 'Verify & Login'}
                      </Button>
                    </Box>
                  </form>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
};

export default LoginPage;
