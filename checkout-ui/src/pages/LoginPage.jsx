import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import { Phone, Verified } from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/authService';

// Validation schemas
const phoneSchema = yup.object({
  phone: yup
    .string()
    .required('Phone number is required')
    .matches(/^\+[1-9]\d{1,14}$/, 'Please enter a valid phone number with country code'),
});

const otpSchema = yup.object({
  otp: yup
    .string()
    .required('OTP is required')
    .length(6, 'OTP must be 6 digits'),
  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),
});

const LoginPage = () => {
  const [step, setStep] = useState(0);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [sessionInfo, setSessionInfo] = useState('');
  const { requestOTP, verifyOTP, error, isLoading, clearError } = useAuth();

  const phoneForm = useForm({
    resolver: yupResolver(phoneSchema),
    defaultValues: { phone: '+91' },
  });

  const otpForm = useForm({
    resolver: yupResolver(otpSchema),
    defaultValues: { otp: '', name: '' },
  });

  const steps = ['Enter Phone', 'Verify OTP'];

  const handlePhoneSubmit = async (data) => {
    try {
      console.log('📱 Submitting phone:', data.phone);
      clearError();

      const response = await requestOTP(data.phone);
      console.log('✅ OTP Response:', response);

      setPhoneNumber(data.phone);
      setSessionInfo(response.sessionInfo || 'fallback_session');
      setStep(1);

      console.log('🎯 Moving to step 2');
    } catch (error) {
      console.error('❌ OTP request failed:', error);
      // Error is already handled by the context, just log it
    }
  };

  const handleOTPSubmit = async (data) => {
    try {
      clearError();
      await verifyOTP(phoneNumber, data.otp, data.name);
    } catch (error) {
      console.error('OTP verification failed:', error);
    }
  };

  const handleBack = () => {
    setStep(0);
    clearError();
  };

  const testConnection = async () => {
    try {
      await authService.healthCheck();
      alert('✅ Backend connection successful!');
    } catch (error) {
      alert('❌ Backend connection failed: ' + error.message);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: 2,
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
      >
        <Card
          sx={{
            maxWidth: 480,
            width: '100%',
            borderRadius: 3,
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                }}
              >
                SwiftCheckout
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Welcome back! Sign in to your account
              </Typography>

              {/* Debug: Test Connection Button */}
              <Button
                onClick={testConnection}
                variant="text"
                size="small"
                sx={{ mt: 1, fontSize: '0.75rem' }}
              >
                Test Backend Connection
              </Button>
            </Box>

            {/* Stepper */}
            <Stepper activeStep={step} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Error Alert */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
                    {error}
                  </Alert>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Forms */}
            <AnimatePresence mode="wait">
              {step === 0 ? (
                <motion.div
                  key="phone"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <form onSubmit={phoneForm.handleSubmit(handlePhoneSubmit)}>
                    <TextField
                      {...phoneForm.register('phone')}
                      fullWidth
                      label="Phone Number"
                      placeholder="+919876543210"
                      error={!!phoneForm.formState.errors.phone}
                      helperText={phoneForm.formState.errors.phone?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 3 }}
                    />
                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={isLoading}
                      sx={{
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        fontSize: '1rem',
                        fontWeight: 600,
                      }}
                    >
                      {isLoading ? 'Sending OTP...' : 'Send OTP'}
                    </Button>
                  </form>
                </motion.div>
              ) : (
                <motion.div
                  key="otp"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <form onSubmit={otpForm.handleSubmit(handleOTPSubmit)}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Enter the 6-digit code sent to {phoneNumber}
                    </Typography>
                    
                    <TextField
                      {...otpForm.register('name')}
                      fullWidth
                      label="Your Name"
                      error={!!otpForm.formState.errors.name}
                      helperText={otpForm.formState.errors.name?.message}
                      sx={{ mb: 2 }}
                    />

                    <TextField
                      {...otpForm.register('otp')}
                      fullWidth
                      label="OTP Code"
                      placeholder="123456"
                      error={!!otpForm.formState.errors.otp}
                      helperText={otpForm.formState.errors.otp?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Verified color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 3 }}
                    />

                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Button
                        onClick={handleBack}
                        variant="outlined"
                        size="large"
                        sx={{ flex: 1, py: 1.5, borderRadius: 2 }}
                      >
                        Back
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        disabled={isLoading}
                        sx={{
                          flex: 2,
                          py: 1.5,
                          borderRadius: 2,
                          textTransform: 'none',
                          fontSize: '1rem',
                          fontWeight: 600,
                        }}
                      >
                        {isLoading ? 'Verifying...' : 'Verify & Login'}
                      </Button>
                    </Box>
                  </form>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
};

export default LoginPage;
