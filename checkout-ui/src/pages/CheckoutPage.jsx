import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  TextField,
  Grid,
  Divider,
  Chip,
  Alert,
  CircularProgress,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  InputAdornment,
  Paper,
  Avatar,
  IconButton,
} from '@mui/material';
import {
  ShoppingCart,
  LocalShipping,
  Security,
  Verified,
  Phone,
  Email,
  LocationOn,
  Payment,
  CreditCard,
  AccountBalance,
  QrCode,
  ArrowBack,
  Add,
  Remove,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Create a separate API service for public checkout (no auth required)
const checkoutAPI = {
  async getProductByShortCode(shortCode) {
    const response = await fetch(`http://localhost:3000/api/public/product/${shortCode}`);
    if (!response.ok) {
      throw new Error('Product not found');
    }
    return response.json();
  },

  async createOrder(orderData) {
    const response = await fetch('http://localhost:3000/api/public/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });
    if (!response.ok) {
      throw new Error('Failed to create order');
    }
    return response.json();
  },
};

const validationSchema = yup.object({
  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),
  phone: yup.string().required('Phone is required').matches(/^[+]?[0-9]{10,15}$/, 'Invalid phone number'),
  email: yup.string().email('Invalid email').required('Email is required'),
  address: yup.string().required('Address is required').min(10, 'Please provide complete address'),
  city: yup.string().required('City is required'),
  pincode: yup.string().required('Pincode is required').matches(/^[0-9]{6}$/, 'Invalid pincode'),
  state: yup.string().required('State is required'),
});

const CheckoutPage = () => {
  const { shortCode } = useParams();
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(1);
  const [paymentMethod, setPaymentMethod] = useState('cod');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      address: '',
      city: '',
      pincode: '',
      state: '',
    },
  });

  // Fetch product data
  const {
    data: productData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['product', shortCode],
    queryFn: () => checkoutAPI.getProductByShortCode(shortCode),
    retry: 1,
  });

  const product = productData?.product;

  // Calculate pricing
  const productPrice = product?.price || 0;
  const subtotal = productPrice * quantity;
  const shippingCharges = product?.shipping?.freeShipping ? 0 : (product?.shipping?.shippingCharges || 50);
  const codCharges = paymentMethod === 'cod' ? (product?.paymentOptions?.codCharges || 0) : 0;
  const total = subtotal + shippingCharges + codCharges;

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= (product?.inventory?.quantity || 999)) {
      setQuantity(newQuantity);
    }
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      const orderData = {
        shortCode,
        buyer: {
          name: data.name,
          phone: data.phone,
          email: data.email,
          address: {
            street: data.address,
            city: data.city,
            state: data.state,
            pincode: data.pincode,
            country: 'India',
          },
        },
        quantity,
        paymentMethod,
        pricing: {
          productPrice,
          quantity,
          subtotal,
          shippingCharges,
          codCharges,
          total,
        },
      };

      const result = await checkoutAPI.createOrder(orderData);
      
      // Redirect to order confirmation or payment
      if (paymentMethod === 'prepaid' && result.paymentUrl) {
        window.location.href = result.paymentUrl;
      } else {
        navigate(`/order-confirmation/${result.orderId}`);
      }
    } catch (error) {
      console.error('Order creation failed:', error);
      alert('Failed to place order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'grey.50',
        }}
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error || !product) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'grey.50',
        }}
      >
        <Container maxWidth="sm">
          <Card sx={{ textAlign: 'center', p: 4 }}>
            <Typography variant="h4" gutterBottom>
              Product Not Found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              The product you're looking for doesn't exist or is no longer available.
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/')}
              sx={{ borderRadius: 2 }}
            >
              Go Home
            </Button>
          </Card>
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'grey.50', py: 4 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate('/')} sx={{ backgroundColor: 'white' }}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h4" sx={{ fontWeight: 700 }}>
            Checkout
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {/* Product Details */}
          <Grid item xs={12} md={7}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card sx={{ mb: 3, borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={5}>
                      <CardMedia
                        component="img"
                        image={product.images?.[0] || '/placeholder-product.jpg'}
                        alt={product.name}
                        sx={{
                          width: '100%',
                          height: 250,
                          objectFit: 'cover',
                          borderRadius: 2,
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={7}>
                      <Typography variant="h5" sx={{ fontWeight: 700, mb: 2 }}>
                        {product.name}
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                        {product.description}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                          ₹{productPrice.toLocaleString()}
                        </Typography>
                        {product.originalPrice && product.originalPrice > productPrice && (
                          <Typography
                            variant="h6"
                            sx={{
                              textDecoration: 'line-through',
                              color: 'text.secondary',
                            }}
                          >
                            ₹{product.originalPrice.toLocaleString()}
                          </Typography>
                        )}
                      </Box>
                      
                      {/* Quantity Selector */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          Quantity:
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <IconButton
                            onClick={() => handleQuantityChange(-1)}
                            disabled={quantity <= 1}
                            size="small"
                            sx={{ border: 1, borderColor: 'grey.300' }}
                          >
                            <Remove />
                          </IconButton>
                          <Typography
                            variant="h6"
                            sx={{
                              minWidth: 40,
                              textAlign: 'center',
                              fontWeight: 600,
                            }}
                          >
                            {quantity}
                          </Typography>
                          <IconButton
                            onClick={() => handleQuantityChange(1)}
                            disabled={quantity >= (product.inventory?.quantity || 999)}
                            size="small"
                            sx={{ border: 1, borderColor: 'grey.300' }}
                          >
                            <Add />
                          </IconButton>
                        </Box>
                      </Box>

                      {/* Stock Status */}
                      <Chip
                        label={`${product.inventory?.quantity || 0} in stock`}
                        color={product.inventory?.quantity > 0 ? 'success' : 'error'}
                        size="small"
                        sx={{ mb: 2 }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Customer Details Form */}
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 3 }}>
                    Delivery Information
                  </Typography>
                  
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name="name"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Full Name"
                              fullWidth
                              error={!!errors.name}
                              helperText={errors.name?.message}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Phone />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name="phone"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Phone Number"
                              fullWidth
                              error={!!errors.phone}
                              helperText={errors.phone?.message}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Phone />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <Controller
                          name="email"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Email Address"
                              type="email"
                              fullWidth
                              error={!!errors.email}
                              helperText={errors.email?.message}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Email />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <Controller
                          name="address"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Complete Address"
                              multiline
                              rows={3}
                              fullWidth
                              error={!!errors.address}
                              helperText={errors.address?.message}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <LocationOn />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="city"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="City"
                              fullWidth
                              error={!!errors.city}
                              helperText={errors.city?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="state"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="State"
                              fullWidth
                              error={!!errors.state}
                              helperText={errors.state?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="pincode"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Pincode"
                              fullWidth
                              error={!!errors.pincode}
                              helperText={errors.pincode?.message}
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* Order Summary */}
          <Grid item xs={12} md={5}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card sx={{ borderRadius: 2, position: 'sticky', top: 24 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 3 }}>
                    Order Summary
                  </Typography>

                  {/* Pricing Breakdown */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Subtotal ({quantity} item{quantity > 1 ? 's' : ''})</Typography>
                      <Typography>₹{subtotal.toLocaleString()}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography>Shipping</Typography>
                      <Typography>
                        {shippingCharges === 0 ? 'FREE' : `₹${shippingCharges}`}
                      </Typography>
                    </Box>
                    {codCharges > 0 && (
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography>COD Charges</Typography>
                        <Typography>₹{codCharges}</Typography>
                      </Box>
                    )}
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                      <Typography variant="h6" sx={{ fontWeight: 700 }}>
                        Total
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main' }}>
                        ₹{total.toLocaleString()}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Payment Method */}
                  <FormControl component="fieldset" sx={{ mb: 3, width: '100%' }}>
                    <FormLabel component="legend" sx={{ fontWeight: 600, mb: 2 }}>
                      Payment Method
                    </FormLabel>
                    <RadioGroup
                      value={paymentMethod}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    >
                      {product.paymentOptions?.cod && (
                        <FormControlLabel
                          value="cod"
                          control={<Radio />}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <AccountBalance />
                              <Typography>Cash on Delivery</Typography>
                            </Box>
                          }
                        />
                      )}
                      {product.paymentOptions?.prepaid && (
                        <FormControlLabel
                          value="prepaid"
                          control={<Radio />}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <CreditCard />
                              <Typography>Pay Online</Typography>
                            </Box>
                          }
                        />
                      )}
                    </RadioGroup>
                  </FormControl>

                  {/* Place Order Button */}
                  <Button
                    variant="contained"
                    fullWidth
                    size="large"
                    onClick={handleSubmit(onSubmit)}
                    disabled={isSubmitting || !product.inventory?.quantity}
                    sx={{
                      borderRadius: 2,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                    }}
                  >
                    {isSubmitting ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      `Place Order - ₹${total.toLocaleString()}`
                    )}
                  </Button>

                  {/* Security Badge */}
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 2, gap: 1 }}>
                    <Security color="success" fontSize="small" />
                    <Typography variant="caption" color="text.secondary">
                      Secure & Safe Payment
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default CheckoutPage;
