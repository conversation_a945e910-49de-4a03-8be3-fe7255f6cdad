import React, { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';
import { authService } from '../services/authService';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// Initial state
const initialState = {
  user: null,
  userType: localStorage.getItem('userType'), // 'buyer' or 'seller'
  token: localStorage.getItem('token'),
  isAuthenticated: !!localStorage.getItem('token'),
  isLoading: true,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_USER: 'UPDATE_USER',
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        userType: action.payload.userType,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case AUTH_ACTIONS.LOGOUT:
      // Clear localStorage on logout
      localStorage.removeItem('token');
      localStorage.removeItem('userType');
      return {
        ...state,
        user: null,
        userType: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if user is authenticated on app load
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          // Try to get current user - could be buyer or seller
          let userResponse;
          let userType;

          try {
            // First try seller endpoint
            userResponse = await authService.getCurrentUser();
            userType = 'seller';
          } catch (sellerError) {
            // If seller fails, try buyer endpoint
            try {
              const buyerApi = axios.create({
                baseURL: API_BASE_URL,
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${token}`
                }
              });
              const response = await buyerApi.get('/buyer/profile');
              userResponse = response.data.buyer;
              userType = 'buyer';
            } catch (buyerError) {
              throw sellerError; // If both fail, throw the original error
            }
          }

          // Store userType in localStorage for persistence
          localStorage.setItem('userType', userType);

          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: {
              user: userResponse,
              userType: userType,
              token
            },
          });
        } catch (error) {
          localStorage.removeItem('token');
          localStorage.removeItem('userType');
          dispatch({ type: AUTH_ACTIONS.LOGOUT });
        }
      } else {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    checkAuth();
  }, []);

  // Check user type
  const checkUserType = async (phone) => {
    try {
      console.log('🔍 AuthContext: Checking user type for', phone);
      const response = await authService.checkUserType(phone);
      console.log('✅ AuthContext: User type check successful', response);
      return response;
    } catch (error) {
      console.error('❌ AuthContext: User type check failed', error);
      throw error;
    }
  };

  // Request OTP
  const requestOTP = async (phone) => {
    try {
      console.log('🔄 AuthContext: Starting OTP request for', phone);
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      const response = await authService.requestOTP(phone);
      console.log('✅ AuthContext: OTP request successful', response);

      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      return response;
    } catch (error) {
      console.error('❌ AuthContext: OTP request failed', error);
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: error.message || 'Failed to send OTP',
      });
      throw error;
    }
  };

  // Verify OTP and login
  const verifyOTP = async (phone, otp, name) => {
    try {
      console.log('🔐 AuthContext: Starting OTP verification');
      console.log('📱 Phone:', phone);
      console.log('🔢 OTP:', otp);
      console.log('👤 Name:', name);

      dispatch({ type: AUTH_ACTIONS.LOGIN_START });
      const response = await authService.verifyOTP(phone, otp, name);

      console.log('✅ AuthContext: OTP verification successful', response);

      // Store token and userType in localStorage for persistent session
      localStorage.setItem('token', response.token);
      localStorage.setItem('userType', response.userType);

      // Handle both buyer and seller responses
      const user = response.buyer || response.seller;
      const userType = response.userType || (response.buyer ? 'buyer' : 'seller');

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: {
          user: user,
          userType: userType,
          token: response.token,
        },
      });

      return response;
    } catch (error) {
      console.error('❌ AuthContext: OTP verification failed', error);
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: error.message || 'Invalid OTP',
      });
      throw error;
    }
  };

  // Logout
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userType');
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
  };

  // Update user profile
  const updateUser = (userData) => {
    dispatch({
      type: AUTH_ACTIONS.UPDATE_USER,
      payload: userData,
    });
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  const value = {
    ...state,
    checkUserType,
    requestOTP,
    verifyOTP,
    logout,
    updateUser,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
