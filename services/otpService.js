const admin = require('firebase-admin');
const fetch = require('node-fetch');

// Initialize Firebase Admin SDK
let firebaseApp = null;

try {
  if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
    };

    firebaseApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });

    console.log('🔥 Firebase Admin SDK initialized successfully');
  } else {
    console.log('⚠️  Firebase credentials not configured, using mock OTP service');
  }
} catch (error) {
  console.error('Firebase initialization error:', error.message);
  console.log('⚠️  Using mock OTP service');
}

// In-memory OTP storage for development/fallback (use Redis in production)
const otpStore = new Map();

// Generate OTP (fallback for development)
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Store OTP with expiration (fallback for development)
const storeOTP = (identifier, otp, expiresInMinutes = 10) => {
  const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);
  otpStore.set(identifier, {
    otp,
    expiresAt,
    attempts: 0,
    maxAttempts: 3
  });
};

// Verify OTP (fallback for development)
const verifyOTP = (identifier, providedOTP) => {
  const stored = otpStore.get(identifier);

  if (!stored) {
    return { success: false, error: 'OTP not found or expired' };
  }

  if (new Date() > stored.expiresAt) {
    otpStore.delete(identifier);
    return { success: false, error: 'OTP expired' };
  }

  if (stored.attempts >= stored.maxAttempts) {
    otpStore.delete(identifier);
    return { success: false, error: 'Maximum attempts exceeded' };
  }

  stored.attempts++;

  if (stored.otp !== providedOTP) {
    otpStore.set(identifier, stored);
    return {
      success: false,
      error: 'Invalid OTP',
      attemptsLeft: stored.maxAttempts - stored.attempts
    };
  }

  // OTP verified successfully
  otpStore.delete(identifier);
  return { success: true };
};

// Send OTP via Firebase Auth REST API
const sendFirebaseOTP = async (phoneNumber) => {
  if (!process.env.FIREBASE_PROJECT_ID) {
    throw new Error('Firebase not configured');
  }

  try {
    // Use Firebase Auth REST API to send verification code
    const response = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:sendVerificationCode?key=${process.env.FIREBASE_WEB_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phoneNumber: phoneNumber,
        recaptchaToken: 'bypass' // For testing, in production use proper reCAPTCHA
      })
    });

    const data = await response.json();

    if (response.ok && data.sessionInfo) {
      console.log(`🔥 Firebase OTP sent to ${phoneNumber}`);
      return {
        success: true,
        message: 'OTP sent via Firebase Auth',
        sessionInfo: data.sessionInfo
      };
    } else {
      console.error('Firebase OTP error:', data);

      // Fallback to mock OTP for development
      const otp = generateOTP();
      storeOTP(phoneNumber, otp);
      console.log(`📱 Fallback OTP for ${phoneNumber}: ${otp}`);

      return {
        success: true,
        message: 'OTP sent (fallback mode)',
        sessionInfo: `fallback_session_${Date.now()}`
      };
    }
  } catch (error) {
    console.error('Firebase OTP error:', error);

    // Fallback to mock OTP
    const otp = generateOTP();
    storeOTP(phoneNumber, otp);
    console.log(`📱 Fallback OTP for ${phoneNumber}: ${otp}`);

    return {
      success: true,
      message: 'OTP sent (fallback mode)',
      sessionInfo: `fallback_session_${Date.now()}`
    };
  }
};

// Verify Firebase OTP
const verifyFirebaseOTP = async (phoneNumber, otp, sessionInfo) => {
  if (!process.env.FIREBASE_PROJECT_ID) {
    // Fallback to local verification
    return verifyOTP(phoneNumber, otp);
  }

  try {
    // If sessionInfo starts with 'fallback_', use local verification
    if (sessionInfo && sessionInfo.startsWith('fallback_')) {
      return verifyOTP(phoneNumber, otp);
    }

    // Use Firebase Auth REST API to verify OTP
    const response = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:signInWithPhoneNumber?key=${process.env.FIREBASE_WEB_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionInfo: sessionInfo,
        code: otp
      })
    });

    const data = await response.json();

    if (response.ok && data.idToken) {
      console.log(`✅ Firebase OTP verified for ${phoneNumber}`);

      // Create custom token for the user
      let customToken = null;
      if (firebaseApp) {
        try {
          customToken = await admin.auth().createCustomToken(phoneNumber);
        } catch (tokenError) {
          console.log('Could not create Firebase custom token:', tokenError.message);
        }
      }

      return {
        success: true,
        firebaseToken: customToken,
        idToken: data.idToken
      };
    } else {
      console.error('Firebase OTP verification failed:', data);

      // Fallback to local verification
      return verifyOTP(phoneNumber, otp);
    }
  } catch (error) {
    console.error('Firebase OTP verification error:', error);

    // Fallback to local verification
    return verifyOTP(phoneNumber, otp);
  }
};

// Main function to send OTP
const sendOTP = async (phoneNumber) => {
  try {
    if (firebaseApp) {
      // Use Firebase Auth for OTP
      return await sendFirebaseOTP(phoneNumber);
    } else {
      // Fallback to mock OTP for development
      const otp = generateOTP();
      storeOTP(phoneNumber, otp);

      console.log(`📱 Mock OTP for ${phoneNumber}: ${otp} (Firebase not configured)`);

      return {
        success: true,
        message: 'OTP sent successfully (mock)',
        sessionInfo: `mock_session_${Date.now()}`
      };
    }
  } catch (error) {
    console.error('Send OTP error:', error);
    return {
      success: false,
      error: 'Failed to send OTP'
    };
  }
};

// Main function to verify OTP
const verifyOTPWithFirebase = async (phoneNumber, otp, sessionInfo = null) => {
  try {
    if (firebaseApp) {
      return await verifyFirebaseOTP(phoneNumber, otp, sessionInfo);
    } else {
      // Fallback to local verification
      return verifyOTP(phoneNumber, otp);
    }
  } catch (error) {
    console.error('Verify OTP error:', error);
    return {
      success: false,
      error: 'Failed to verify OTP'
    };
  }
};

// Clean expired OTPs (call periodically)
const cleanExpiredOTPs = () => {
  const now = new Date();
  for (const [identifier, data] of otpStore.entries()) {
    if (now > data.expiresAt) {
      otpStore.delete(identifier);
    }
  }
};

// Clean expired OTPs every 5 minutes
setInterval(cleanExpiredOTPs, 5 * 60 * 1000);

// Verify phone number format
const isValidPhoneNumber = (phoneNumber) => {
  // Basic phone number validation (E.164 format)
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
};

// Create Firebase user (if needed)
const createFirebaseUser = async (phoneNumber) => {
  if (!firebaseApp) {
    return null;
  }

  try {
    const userRecord = await admin.auth().createUser({
      phoneNumber: phoneNumber,
    });
    return userRecord;
  } catch (error) {
    if (error.code === 'auth/phone-number-already-exists') {
      // User already exists, get the user
      try {
        const userRecord = await admin.auth().getUserByPhoneNumber(phoneNumber);
        return userRecord;
      } catch (getUserError) {
        console.error('Error getting existing user:', getUserError);
        return null;
      }
    }
    console.error('Error creating Firebase user:', error);
    return null;
  }
};

module.exports = {
  sendOTP,
  verifyOTP: verifyOTPWithFirebase, // Use Firebase-enabled verification
  generateOTP,
  cleanExpiredOTPs,
  isValidPhoneNumber,
  createFirebaseUser,
  // Export Firebase app for other services if needed
  getFirebaseApp: () => firebaseApp
};
