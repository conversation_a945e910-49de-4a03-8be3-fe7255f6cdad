const speakeasy = require('speakeasy');
const twilio = require('twilio');
const nodemailer = require('nodemailer');

// In-memory OTP storage (use Redis in production)
const otpStore = new Map();

// Twilio client (configure with your credentials)
const twilioClient = process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN
  ? twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN)
  : null;

// Email transporter (configure with your SMTP settings)
const emailTransporter = process.env.SMTP_HOST
  ? nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    })
  : null;

// Generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Store OTP with expiration
const storeOTP = (identifier, otp, expiresInMinutes = 10) => {
  const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);
  otpStore.set(identifier, {
    otp,
    expiresAt,
    attempts: 0,
    maxAttempts: 3
  });
};

// Verify OTP
const verifyOTP = (identifier, providedOTP) => {
  const stored = otpStore.get(identifier);
  
  if (!stored) {
    return { success: false, error: 'OTP not found or expired' };
  }
  
  if (new Date() > stored.expiresAt) {
    otpStore.delete(identifier);
    return { success: false, error: 'OTP expired' };
  }
  
  if (stored.attempts >= stored.maxAttempts) {
    otpStore.delete(identifier);
    return { success: false, error: 'Maximum attempts exceeded' };
  }
  
  stored.attempts++;
  
  if (stored.otp !== providedOTP) {
    otpStore.set(identifier, stored);
    return { 
      success: false, 
      error: 'Invalid OTP',
      attemptsLeft: stored.maxAttempts - stored.attempts
    };
  }
  
  // OTP verified successfully
  otpStore.delete(identifier);
  return { success: true };
};

// Send OTP via SMS
const sendSMSOTP = async (phone, otp) => {
  if (!twilioClient) {
    console.log(`SMS OTP for ${phone}: ${otp} (Twilio not configured)`);
    return { success: true, message: 'OTP sent (mock)' };
  }
  
  try {
    await twilioClient.messages.create({
      body: `Your SwiftCheckout verification code is: ${otp}. Valid for 10 minutes.`,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone
    });
    
    return { success: true, message: 'OTP sent successfully' };
  } catch (error) {
    console.error('SMS sending error:', error);
    return { success: false, error: 'Failed to send SMS' };
  }
};

// Send OTP via Email
const sendEmailOTP = async (email, otp) => {
  if (!emailTransporter) {
    console.log(`Email OTP for ${email}: ${otp} (SMTP not configured)`);
    return { success: true, message: 'OTP sent (mock)' };
  }
  
  try {
    await emailTransporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'SwiftCheckout - Verification Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>SwiftCheckout Verification</h2>
          <p>Your verification code is:</p>
          <div style="background: #f5f5f5; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 3px; margin: 20px 0;">
            ${otp}
          </div>
          <p>This code will expire in 10 minutes.</p>
          <p>If you didn't request this code, please ignore this email.</p>
        </div>
      `
    });
    
    return { success: true, message: 'OTP sent successfully' };
  } catch (error) {
    console.error('Email sending error:', error);
    return { success: false, error: 'Failed to send email' };
  }
};

// Main function to send OTP
const sendOTP = async (identifier, method = 'sms') => {
  const otp = generateOTP();
  storeOTP(identifier, otp);
  
  if (method === 'email') {
    return await sendEmailOTP(identifier, otp);
  } else {
    return await sendSMSOTP(identifier, otp);
  }
};

// Clean expired OTPs (call periodically)
const cleanExpiredOTPs = () => {
  const now = new Date();
  for (const [identifier, data] of otpStore.entries()) {
    if (now > data.expiresAt) {
      otpStore.delete(identifier);
    }
  }
};

// Clean expired OTPs every 5 minutes
setInterval(cleanExpiredOTPs, 5 * 60 * 1000);

module.exports = {
  sendOTP,
  verifyOTP,
  generateOTP,
  cleanExpiredOTPs
};
