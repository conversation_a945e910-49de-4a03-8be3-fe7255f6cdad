const Razorpay = require('razorpay');
const crypto = require('crypto');

// Initialize Razorpay (will be null if credentials not provided)
const razorpay = process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET
  ? new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET
    })
  : null;

// Create Razorpay order
const createRazorpayOrder = async (amount, currency = 'INR', receipt) => {
  if (!razorpay) {
    throw new Error('Razorpay not configured');
  }

  try {
    const order = await razorpay.orders.create({
      amount: Math.round(amount * 100), // Convert to paise
      currency,
      receipt,
      payment_capture: 1
    });

    return {
      success: true,
      order
    };
  } catch (error) {
    console.error('Razorpay order creation error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Verify Razorpay payment signature
const verifyRazorpaySignature = (orderId, paymentId, signature) => {
  if (!process.env.RAZORPAY_KEY_SECRET) {
    throw new Error('Razorpay key secret not configured');
  }

  const body = orderId + '|' + paymentId;
  const expectedSignature = crypto
    .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
    .update(body.toString())
    .digest('hex');

  return expectedSignature === signature;
};

// Get payment details from Razorpay
const getPaymentDetails = async (paymentId) => {
  if (!razorpay) {
    throw new Error('Razorpay not configured');
  }

  try {
    const payment = await razorpay.payments.fetch(paymentId);
    return {
      success: true,
      payment
    };
  } catch (error) {
    console.error('Get payment details error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Refund payment
const refundPayment = async (paymentId, amount = null) => {
  if (!razorpay) {
    throw new Error('Razorpay not configured');
  }

  try {
    const refundData = { payment_id: paymentId };
    if (amount) {
      refundData.amount = Math.round(amount * 100); // Convert to paise
    }

    const refund = await razorpay.payments.refund(paymentId, refundData);
    return {
      success: true,
      refund
    };
  } catch (error) {
    console.error('Refund payment error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Mock payment for development/testing
const createMockPayment = (amount, currency = 'INR') => {
  const orderId = 'mock_order_' + Date.now();
  const paymentId = 'mock_payment_' + Date.now();
  
  return {
    success: true,
    order: {
      id: orderId,
      amount: Math.round(amount * 100),
      currency,
      status: 'created',
      receipt: 'mock_receipt_' + Date.now()
    },
    mockPayment: {
      id: paymentId,
      order_id: orderId,
      amount: Math.round(amount * 100),
      currency,
      status: 'captured',
      method: 'card'
    }
  };
};

// Process payment based on method
const processPayment = async (paymentData) => {
  const { method, amount, currency, receipt, orderId } = paymentData;

  if (method === 'cod') {
    // COD doesn't require payment processing
    return {
      success: true,
      method: 'cod',
      status: 'pending',
      message: 'COD order created successfully'
    };
  }

  if (method === 'prepaid') {
    if (razorpay) {
      return await createRazorpayOrder(amount, currency, receipt);
    } else {
      // Use mock payment for development
      console.log('Using mock payment - Razorpay not configured');
      return createMockPayment(amount, currency);
    }
  }

  throw new Error('Invalid payment method');
};

// Calculate platform fee
const calculatePlatformFee = (amount, plan = 'free') => {
  const feeStructure = {
    free: 10, // ₹10 flat fee
    basic: 0.02, // 2% of amount
    premium: 0.015, // 1.5% of amount
    enterprise: 0.01 // 1% of amount
  };

  const feeRate = feeStructure[plan] || feeStructure.free;
  
  if (plan === 'free') {
    return feeRate; // Flat fee
  } else {
    return Math.max(amount * feeRate, 5); // Percentage with minimum ₹5
  }
};

// Calculate total order amount
const calculateOrderTotal = (productPrice, quantity, shippingCharges = 0, codCharges = 0, sellerPlan = 'free') => {
  const subtotal = productPrice * quantity;
  const platformFee = calculatePlatformFee(subtotal, sellerPlan);
  const total = subtotal + shippingCharges + codCharges + platformFee;

  return {
    subtotal,
    shippingCharges,
    codCharges,
    platformFee,
    total
  };
};

module.exports = {
  createRazorpayOrder,
  verifyRazorpaySignature,
  getPaymentDetails,
  refundPayment,
  processPayment,
  calculatePlatformFee,
  calculateOrderTotal,
  createMockPayment
};
