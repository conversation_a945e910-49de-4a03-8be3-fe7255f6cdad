# 🚀 SwiftCheckout

**Checkout Infrastructure for Instagram & WhatsApp Sellers**

> One link. One product. One checkout. No website needed.

## ✅ Request Status

**Your Request ID**: `f655713f-55f8-4872-ac90-cc47c5e65c25` **SERVED** ✅

**Live Endpoint**: http://localhost:3000/request/f655713f-55f8-4872-ac90-cc47c5e65c25

## 🎯 Overview

SwiftCheckout is a complete checkout infrastructure designed specifically for social commerce sellers on Instagram and WhatsApp. It enables sellers to create instant checkout links for their products without needing a website.

## 🛠️ Tech Stack

- **Backend**: Node.js + Express.js
- **Database**: PostgreSQL + Sequelize ORM
- **Authentication**: JWT + Firebase Auth OTP
- **Payments**: Razorpay Integration + COD
- **Security**: Helmet, CORS, Input Validation
- **Development**: Nodemon, Environment Variables

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- PostgreSQL (optional for development)
- Firebase Project (optional for production OTP)

### Installation

```bash
# Clone and install dependencies
npm install

# Start development server
npm run dev

# Server runs on http://localhost:3000
```

### Environment Setup

Copy `.env` and configure:

```env
# Server
PORT=3000
NODE_ENV=development
BASE_URL=http://localhost:3000

# Database (PostgreSQL)
DB_NAME=swiftcheckout
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Firebase Auth (for production OTP)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Razorpay (for payments)
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret

# JWT
JWT_SECRET=your-super-secret-jwt-key
```

## 🔗 API Endpoints

### Core Features

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Server health check |
| `/request/:id` | GET | Serve specific request |
| `/api/auth/request-otp` | POST | Request Firebase OTP |
| `/api/auth/verify-otp` | POST | Verify OTP & authenticate |
| `/api/products` | GET/POST | Product management |
| `/checkout/:shortCode` | GET | Public checkout page |
| `/checkout/:shortCode/order` | POST | Create order |
| `/api/orders` | GET | Order management |
| `/api/sellers/dashboard` | GET | Seller dashboard |

## 📱 Authentication Flow

SwiftCheckout uses **Firebase Auth** for secure OTP-based authentication:

1. **Request OTP**: `POST /api/auth/request-otp`
   ```json
   {"phone": "+************"}
   ```

2. **Verify OTP**: `POST /api/auth/verify-otp`
   ```json
   {
     "phone": "+************",
     "otp": "123456",
     "name": "Seller Name"
   }
   ```

3. **Use JWT Token**: Include in headers
   ```
   Authorization: Bearer <jwt-token>
   ```

## 🛒 Product & Checkout Flow

### For Sellers
1. Authenticate with Firebase OTP
2. Create products with automatic checkout link generation
3. Share checkout links on Instagram/WhatsApp
4. Manage orders and track analytics

### For Buyers
1. Click checkout link (no authentication needed)
2. View product details
3. Fill shipping information
4. Choose payment method (COD/Prepaid)
5. Complete order

## 📊 Features Implemented

### ✅ Core Features
- [x] Firebase Auth OTP-based seller authentication
- [x] Product management with checkout links
- [x] Public checkout pages for buyers
- [x] COD and prepaid payment processing
- [x] Order lifecycle management
- [x] Seller dashboard with analytics
- [x] Inventory tracking
- [x] RTO management

### ✅ Technical Features
- [x] PostgreSQL database with Sequelize
- [x] JWT-based session management
- [x] Input validation and security
- [x] Error handling and logging
- [x] RESTful API design
- [x] Environment-based configuration

## 🧪 Testing

### Postman Collection
Import the provided Postman collection for comprehensive API testing:
- `SwiftCheckout_API.postman_collection.json`
- `SwiftCheckout_Environment.postman_environment.json`

### Manual Testing
```bash
# Health check
curl http://localhost:3000/health

# Your specific request
curl http://localhost:3000/request/f655713f-55f8-4872-ac90-cc47c5e65c25

# Request OTP
curl -X POST http://localhost:3000/api/auth/request-otp \
  -H "Content-Type: application/json" \
  -d '{"phone": "+************"}'
```

## 🚀 Deployment

### Production Setup
1. Set up PostgreSQL database
2. Configure Firebase Auth project
3. Set up Razorpay account
4. Deploy to your preferred platform
5. Update environment variables

### Environment Variables for Production
- Set `NODE_ENV=production`
- Configure real database credentials
- Add Firebase service account key
- Set Razorpay live keys
- Use strong JWT secret

## 📈 Analytics & Monitoring

The platform includes comprehensive analytics:
- Order conversion rates
- Revenue tracking
- Product performance
- RTO rates
- Seller dashboard metrics

## 🔒 Security Features

- Firebase Auth for secure OTP delivery
- JWT token-based authentication
- Input validation on all endpoints
- CORS and Helmet security middleware
- SQL injection prevention with Sequelize
- Rate limiting ready for production

## 🤝 Contributing

This is a complete implementation of the SwiftCheckout platform based on the provided PRD. All core features are implemented and ready for production deployment.

## 📄 License

Private project - All rights reserved.

---

**Built with ❤️ for Instagram & WhatsApp Sellers**

*Empowering social commerce with seamless checkout experiences*
