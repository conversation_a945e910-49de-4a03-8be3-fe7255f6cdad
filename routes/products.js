const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Op } = require('sequelize');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { Product, Seller } = require('../models');
const { authenticate, requireVerified } = require('../middleware/auth');

const router = express.Router();

// Helper function to generate unique short code
const generateShortCode = () => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Helper function to generate checkout link
const generateCheckoutLink = (shortCode) => {
  return `${process.env.FRONTEND_URL || 'https://swiftcheckout.com'}/buy/${shortCode}`;
};

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/products');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'product-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    // Check file type
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

// Upload product image
router.post('/upload-image', authenticate, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No image file provided'
      });
    }

    // Generate the URL for the uploaded image
    const imageUrl = `/uploads/products/${req.file.filename}`;

    res.json({
      message: 'Image uploaded successfully',
      imageUrl: imageUrl,
      filename: req.file.filename
    });

  } catch (error) {
    console.error('Image upload error:', error);
    res.status(500).json({
      error: 'Failed to upload image'
    });
  }
});

// Get all products for a seller
router.get('/', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 10, category, isActive, search } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { sellerId: req.seller.id };

    // Add filters
    if (category) whereClause.category = category;
    if (isActive !== undefined) whereClause.isActive = isActive === 'true';
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const products = await Product.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      products: products.rows.map(p => p.getSummary()),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: products.count,
        pages: Math.ceil(products.count / limit)
      }
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      error: 'Failed to fetch products'
    });
  }
});

// Get single product
router.get('/:id', authenticate, async (req, res) => {
  try {
    const product = await Product.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    res.json({ product });

  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      error: 'Failed to fetch product'
    });
  }
});

// Create new product
router.post('/', authenticate, [
  body('name').isLength({ min: 2, max: 200 }).withMessage('Product name must be 2-200 characters'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description too long'),
  body('category').optional().isLength({ max: 100 }).withMessage('Category too long'),
  body('inventory.quantity').optional().isInt({ min: 0 }).withMessage('Quantity must be non-negative'),
  body('inventory.trackInventory').optional().isBoolean().withMessage('Track inventory must be boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      name,
      description,
      price,
      originalPrice,
      category,
      tags = [],
      inventory = {},
      paymentOptions = {},
      shipping = {},
      images = [],
      image // Single image from frontend
    } = req.body;

    // Handle single image or images array
    const productImages = image ? [image] : images;

    // Generate unique short code and checkout link
    let shortCode;
    let checkoutLink;
    let isUnique = false;

    // Ensure unique short code
    while (!isUnique) {
      shortCode = generateShortCode();
      const existingProduct = await Product.findOne({ where: { shortCode } });
      if (!existingProduct) {
        isUnique = true;
        checkoutLink = generateCheckoutLink(shortCode);
      }
    }

    const product = await Product.create({
      sellerId: req.seller.id,
      name,
      description,
      price,
      originalPrice,
      category,
      tags,
      images: productImages,
      shortCode,
      checkoutLink,
      inventory: {
        quantity: inventory.quantity || 0,
        trackInventory: inventory.trackInventory !== false,
        lowStockThreshold: inventory.lowStockThreshold || 5
      },
      paymentOptions: {
        cod: paymentOptions.cod !== false,
        prepaid: paymentOptions.prepaid !== false,
        codCharges: paymentOptions.codCharges || 0
      },
      shipping: {
        weight: shipping.weight || 0,
        dimensions: shipping.dimensions || {},
        freeShipping: shipping.freeShipping || false,
        shippingCharges: shipping.shippingCharges || 0
      }
    });

    res.status(201).json({
      message: 'Product created successfully',
      product: product.getSummary()
    });

  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({
      error: 'Failed to create product'
    });
  }
});

// Update product
router.put('/:id', authenticate, [
  body('name').optional().isLength({ min: 2, max: 200 }).withMessage('Product name must be 2-200 characters'),
  body('price').optional().isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description too long'),
  body('category').optional().isLength({ max: 100 }).withMessage('Category too long')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const product = await Product.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    const updateData = {};
    const allowedFields = [
      'name', 'description', 'price', 'originalPrice', 'category',
      'tags', 'images', 'inventory', 'paymentOptions', 'shipping',
      'isActive', 'isVisible'
    ];

    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    await product.update(updateData);

    res.json({
      message: 'Product updated successfully',
      product: product.getSummary()
    });

  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({
      error: 'Failed to update product'
    });
  }
});

// Delete product
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const product = await Product.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    await product.destroy();

    res.json({
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      error: 'Failed to delete product'
    });
  }
});

// Toggle product active status
router.patch('/:id/toggle-active', authenticate, async (req, res) => {
  try {
    const product = await Product.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    await product.update({ isActive: !product.isActive });

    res.json({
      message: `Product ${product.isActive ? 'activated' : 'deactivated'} successfully`,
      product: product.getSummary()
    });

  } catch (error) {
    console.error('Toggle product error:', error);
    res.status(500).json({
      error: 'Failed to toggle product status'
    });
  }
});

// Update inventory
router.patch('/:id/inventory', authenticate, [
  body('quantity').isInt({ min: 0 }).withMessage('Quantity must be non-negative integer'),
  body('trackInventory').optional().isBoolean().withMessage('Track inventory must be boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const product = await Product.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    const { quantity, trackInventory } = req.body;
    const newInventory = { ...product.inventory };

    if (quantity !== undefined) newInventory.quantity = quantity;
    if (trackInventory !== undefined) newInventory.trackInventory = trackInventory;

    await product.update({ inventory: newInventory });

    res.json({
      message: 'Inventory updated successfully',
      inventory: newInventory
    });

  } catch (error) {
    console.error('Update inventory error:', error);
    res.status(500).json({
      error: 'Failed to update inventory'
    });
  }
});

// Get product analytics
router.get('/:id/analytics', authenticate, async (req, res) => {
  try {
    const product = await Product.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    res.json({
      analytics: product.analytics,
      performance: {
        conversionRate: product.analytics.clicks > 0
          ? ((product.analytics.orders / product.analytics.clicks) * 100).toFixed(2) + '%'
          : '0%',
        averageOrderValue: product.analytics.orders > 0
          ? (product.analytics.revenue / product.analytics.orders).toFixed(2)
          : '0'
      }
    });

  } catch (error) {
    console.error('Get analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch analytics'
    });
  }
});

// Public route to get product by checkout link (for buyers)
router.get('/checkout/:shortCode', async (req, res) => {
  try {
    const product = await Product.findOne({
      where: {
        shortCode: req.params.shortCode,
        isActive: true,
        isVisible: true
      },
      include: [{
        model: Seller,
        as: 'seller',
        attributes: ['name', 'isVerified', 'verificationBadge']
      }]
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found or unavailable'
      });
    }

    // Track view
    const analytics = { ...product.analytics };
    analytics.views += 1;
    await product.update({ analytics });

    res.json({
      product: {
        ...product.getSummary(),
        description: product.description,
        images: product.images,
        paymentOptions: product.paymentOptions,
        shipping: product.shipping,
        seller: product.seller
      }
    });

  } catch (error) {
    console.error('Get checkout product error:', error);
    res.status(500).json({
      error: 'Failed to fetch product'
    });
  }
});

module.exports = router;
