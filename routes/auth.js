const express = require('express');
const { body, validationResult } = require('express-validator');
const { Seller, Buyer } = require('../models');
const { generateToken, authenticate } = require('../middleware/auth');
const { sendOTP, verifyOTP, isValidPhoneNumber, createFirebaseUser } = require('../services/otpService');

const router = express.Router();

// Check user type by phone number (before OTP)
router.post('/check-user-type', [
  body('phone').isMobilePhone().withMessage('Valid phone number required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { phone } = req.body;

    // Check if buyer exists
    const buyer = await Buyer.findOne({
      where: { phone }
    });

    if (buyer) {
      return res.json({
        userType: 'buyer',
        exists: true,
        user: {
          name: buyer.name,
          email: buyer.email,
          totalOrders: buyer.profile.totalOrders,
          isVerified: buyer.verification.phoneVerified
        }
      });
    }

    // Check if seller exists
    const seller = await Seller.findOne({
      where: { phone }
    });

    if (seller) {
      return res.json({
        userType: 'seller',
        exists: true,
        user: {
          name: seller.name,
          email: seller.email,
          isVerified: seller.isVerified
        }
      });
    }

    // New user - will be registered as buyer by default
    res.json({
      userType: 'new',
      exists: false,
      message: 'New user - will be registered as buyer'
    });

  } catch (error) {
    console.error('Check user type error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});


// Request OTP for login/signup
router.post('/request-otp', [
  body('phone').isMobilePhone().withMessage('Valid phone number required')
], async (req, res) => {
  try {
    console.log('🚀 OTP request received:', req.body);
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('❌ Validation errors:', errors.array());
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { phone } = req.body;
    console.log('📞 Processing OTP request for phone:', phone);

    // Validate phone number format
    if (!isValidPhoneNumber(phone)) {
      return res.status(400).json({
        error: 'Invalid phone number format. Please use E.164 format (e.g., +919876543210)'
      });
    }

    // Send OTP via Firebase Auth
    console.log('📤 Calling sendOTP service...');
    const result = await sendOTP(phone);
    console.log('📥 sendOTP result:', result);

    if (result.success) {
      res.json({
        message: 'OTP sent successfully via Firebase Auth',
        phone: phone.replace(/(\+\d{2})\d{6}(\d{4})/, '$1******$2'), // Mask phone number
        sessionInfo: result.sessionInfo
      });
    } else {
      res.status(500).json({
        error: 'Failed to send OTP',
        details: result.error
      });
    }
  } catch (error) {
    console.error('OTP request error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Verify OTP and login/signup
router.post('/verify-otp', [
  body('phone').isMobilePhone().withMessage('Valid phone number required'),
  body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits'),
  body('sessionInfo').optional().isString().withMessage('Session info must be string'),
  body('name').optional().isLength({ min: 2 }).withMessage('Name must be at least 2 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { phone, otp, sessionInfo, name } = req.body;

    // Verify OTP with Firebase
    const otpResult = await verifyOTP(phone, otp, sessionInfo);

    if (!otpResult.success) {
      return res.status(400).json({
        error: 'OTP verification failed',
        details: otpResult.error,
        attemptsLeft: otpResult.attemptsLeft
      });
    }

    // First, check if this phone number belongs to a buyer
    const buyer = await Buyer.findOne({
      where: { phone }
    });

    if (buyer) {
      // This is a buyer - generate buyer token and redirect to buyer dashboard
      const jwt = require('jsonwebtoken');
      const buyerToken = jwt.sign(
        { id: buyer.id, phone: buyer.phone, type: 'buyer' },
        process.env.JWT_SECRET,
        { expiresIn: '30d' }
      );

      // Update buyer verification and analytics
      await buyer.update({
        verification: {
          ...buyer.verification,
          phoneVerified: true
        },
        analytics: {
          ...buyer.analytics,
          lastActiveDate: new Date(),
          sessionCount: (buyer.analytics.sessionCount || 0) + 1
        }
      });

      return res.json({
        message: 'Buyer authentication successful',
        token: buyerToken,
        userType: 'buyer',
        buyer: {
          id: buyer.id,
          phone: buyer.phone,
          name: buyer.name,
          email: buyer.email,
          addresses: buyer.addresses,
          preferences: buyer.preferences,
          profile: buyer.profile
        },
        redirectTo: '/buyer-dashboard'
      });
    }

    // If not a buyer, check if it's a seller
    let seller = await Seller.findOne({
      where: { phone }
    });

    if (seller) {
      // Existing seller - login
      seller.lastLogin = new Date();
      if (!seller.isVerified) {
        seller.isVerified = true;
      }
      await seller.save();

      // Generate JWT token for seller
      const token = generateToken(seller.id);

      return res.json({
        message: 'Seller authentication successful',
        token,
        userType: 'seller',
        seller: seller.getProfileSummary(),
        redirectTo: '/dashboard',
        firebaseToken: otpResult.firebaseToken
      });
    }

    // If neither buyer nor seller exists, create new buyer
    if (!name) {
      return res.status(400).json({
        error: 'Name required for new user registration'
      });
    }

    // Create Firebase user if not exists
    await createFirebaseUser(phone);

    // Create new buyer (default for new users)
    const newBuyer = await Buyer.create({
      phone,
      name,
      verification: {
        phoneVerified: true,
        emailVerified: false
      },
      analytics: {
        firstOrderDate: null,
        lastActiveDate: new Date(),
        sessionCount: 1
      }
    });

    // Generate JWT token for new buyer
    const jwt = require('jsonwebtoken');
    const buyerToken = jwt.sign(
      { id: newBuyer.id, phone: newBuyer.phone, type: 'buyer' },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );

    res.json({
      message: 'New buyer account created successfully',
      token: buyerToken,
      userType: 'buyer',
      buyer: {
        id: newBuyer.id,
        phone: newBuyer.phone,
        name: newBuyer.name,
        email: newBuyer.email,
        addresses: newBuyer.addresses,
        preferences: newBuyer.preferences,
        profile: newBuyer.profile,
        verification: newBuyer.verification
      },
      isNewUser: true,
      redirectTo: '/buyer-dashboard',
      firebaseToken: otpResult.firebaseToken
    });

  } catch (error) {
    console.error('OTP verification error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get current seller profile
router.get('/profile', authenticate, async (req, res) => {
  try {
    res.json({
      seller: req.seller.getProfileSummary()
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update seller profile
router.put('/profile', authenticate, [
  body('name').optional().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').optional().isEmail().withMessage('Valid email required'),
  body('upiId').optional().isLength({ min: 1 }).withMessage('UPI ID required'),
  body('gstin').optional().isLength({ min: 15, max: 15 }).withMessage('GSTIN must be 15 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { name, email, upiId, gstin, bankDetails } = req.body;
    const seller = req.seller;

    // Update allowed fields
    if (name) seller.name = name;
    if (email) seller.email = email;
    if (upiId) seller.upiId = upiId;
    if (gstin) seller.gstin = gstin;
    if (bankDetails) seller.bankDetails = bankDetails;

    await seller.save();

    res.json({
      message: 'Profile updated successfully',
      seller: seller.getProfileSummary()
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Logout (client-side token removal, but we can track it)
router.post('/logout', authenticate, async (req, res) => {
  try {
    // In a production app, you might want to blacklist the token
    // For now, we'll just respond with success
    res.json({
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

module.exports = router;
