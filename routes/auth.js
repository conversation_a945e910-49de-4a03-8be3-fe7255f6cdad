const express = require('express');
const { body, validationResult } = require('express-validator');
const { Seller } = require('../models');
const { generateToken, authenticate } = require('../middleware/auth');
const { sendOTP, verifyOTP } = require('../services/otpService');

const router = express.Router();

// Request OTP for login/signup
router.post('/request-otp', [
  body('phone').isMobilePhone().withMessage('Valid phone number required'),
  body('email').optional().isEmail().withMessage('Valid email required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { phone, email } = req.body;
    const method = email ? 'email' : 'sms';
    const identifier = email || phone;

    // Send OTP
    const result = await sendOTP(identifier, method);

    if (result.success) {
      res.json({
        message: 'OTP sent successfully',
        method,
        identifier: method === 'email' ? email : phone.replace(/(\d{3})\d{4}(\d{3})/, '$1****$2')
      });
    } else {
      res.status(500).json({
        error: 'Failed to send OTP',
        details: result.error
      });
    }
  } catch (error) {
    console.error('OTP request error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Verify OTP and login/signup
router.post('/verify-otp', [
  body('phone').isMobilePhone().withMessage('Valid phone number required'),
  body('email').optional().isEmail().withMessage('Valid email required'),
  body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits'),
  body('name').optional().isLength({ min: 2 }).withMessage('Name must be at least 2 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { phone, email, otp, name } = req.body;
    const identifier = email || phone;

    // Verify OTP
    const otpResult = verifyOTP(identifier, otp);

    if (!otpResult.success) {
      return res.status(400).json({
        error: 'OTP verification failed',
        details: otpResult.error,
        attemptsLeft: otpResult.attemptsLeft
      });
    }

    // Find or create seller
    let seller = await Seller.findOne({
      where: email ? { email } : { phone }
    });

    if (!seller) {
      // Create new seller
      if (!name) {
        return res.status(400).json({
          error: 'Name required for new seller registration'
        });
      }

      seller = await Seller.create({
        name,
        phone,
        email: email || null,
        isVerified: true, // Phone/email verified via OTP
        lastLogin: new Date()
      });
    } else {
      // Update existing seller
      seller.lastLogin = new Date();
      if (!seller.isVerified) {
        seller.isVerified = true;
      }
      await seller.save();
    }

    // Generate JWT token
    const token = generateToken(seller.id);

    res.json({
      message: 'Authentication successful',
      token,
      seller: seller.getProfileSummary(),
      isNewSeller: !seller.createdAt || seller.createdAt.getTime() === seller.updatedAt.getTime()
    });

  } catch (error) {
    console.error('OTP verification error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get current seller profile
router.get('/profile', authenticate, async (req, res) => {
  try {
    res.json({
      seller: req.seller.getProfileSummary()
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update seller profile
router.put('/profile', authenticate, [
  body('name').optional().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').optional().isEmail().withMessage('Valid email required'),
  body('upiId').optional().isLength({ min: 1 }).withMessage('UPI ID required'),
  body('gstin').optional().isLength({ min: 15, max: 15 }).withMessage('GSTIN must be 15 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { name, email, upiId, gstin, bankDetails } = req.body;
    const seller = req.seller;

    // Update allowed fields
    if (name) seller.name = name;
    if (email) seller.email = email;
    if (upiId) seller.upiId = upiId;
    if (gstin) seller.gstin = gstin;
    if (bankDetails) seller.bankDetails = bankDetails;

    await seller.save();

    res.json({
      message: 'Profile updated successfully',
      seller: seller.getProfileSummary()
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Logout (client-side token removal, but we can track it)
router.post('/logout', authenticate, async (req, res) => {
  try {
    // In a production app, you might want to blacklist the token
    // For now, we'll just respond with success
    res.json({
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

module.exports = router;
