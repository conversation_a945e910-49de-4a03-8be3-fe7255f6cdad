const express = require('express');
const { body, validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const { Buyer, Order, Product } = require('../models');
const otpService = require('../services/otpService');

const router = express.Router();

// Middleware to authenticate buyer
const authenticateBuyer = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'buyer') {
      return res.status(403).json({ error: 'Access denied. Invalid token type.' });
    }

    const buyer = await Buyer.findByPk(decoded.id);
    if (!buyer || !buyer.isActive) {
      return res.status(404).json({ error: 'Buyer not found or inactive.' });
    }

    req.buyer = buyer;
    next();
  } catch (error) {
    console.error('Buyer authentication error:', error);
    res.status(401).json({ error: 'Invalid token.' });
  }
};

// Step 1: Send OTP for phone verification
router.post('/send-otp', [
  body('phone').matches(/^[+]?[0-9]{10,15}$/).withMessage('Invalid phone number format')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      });
    }

    const { phone } = req.body;

    // Send OTP
    const result = await otpService.sendOTP(phone);
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        sessionInfo: result.sessionInfo
      });
    } else {
      res.status(400).json({
        error: result.error || 'Failed to send OTP'
      });
    }

  } catch (error) {
    console.error('Send OTP error:', error);
    res.status(500).json({ error: 'Failed to send OTP' });
  }
});

// Step 2: Verify OTP and check if buyer exists
router.post('/verify-otp', [
  body('phone').matches(/^[+]?[0-9]{10,15}$/).withMessage('Invalid phone number'),
  body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits'),
  body('sessionInfo').notEmpty().withMessage('Session info is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      });
    }

    const { phone, otp, sessionInfo } = req.body;

    // Verify OTP
    const otpResult = await otpService.verifyOTP(phone, otp, sessionInfo);
    
    if (!otpResult.success) {
      return res.status(400).json({
        error: otpResult.error || 'OTP verification failed'
      });
    }

    // Check if buyer exists
    let buyer = await Buyer.findOne({ where: { phone } });
    
    if (buyer) {
      // Existing buyer - generate token and return profile
      const token = jwt.sign(
        { id: buyer.id, phone: buyer.phone, type: 'buyer' },
        process.env.JWT_SECRET,
        { expiresIn: '30d' }
      );

      // Update verification status
      await buyer.update({
        verification: {
          ...buyer.verification,
          phoneVerified: true
        },
        analytics: {
          ...buyer.analytics,
          lastActiveDate: new Date(),
          sessionCount: (buyer.analytics.sessionCount || 0) + 1
        }
      });

      res.json({
        success: true,
        isNewBuyer: false,
        token,
        buyer: {
          id: buyer.id,
          phone: buyer.phone,
          name: buyer.name,
          email: buyer.email,
          addresses: buyer.addresses,
          preferences: buyer.preferences,
          profile: buyer.profile
        }
      });
    } else {
      // New buyer - return verification success but no profile
      res.json({
        success: true,
        isNewBuyer: true,
        message: 'Phone verified. Please complete your profile.'
      });
    }

  } catch (error) {
    console.error('Verify OTP error:', error);
    res.status(500).json({ error: 'OTP verification failed' });
  }
});

// Step 3: Complete buyer profile (for new buyers)
router.post('/complete-profile', [
  body('phone').matches(/^[+]?[0-9]{10,15}$/).withMessage('Invalid phone number'),
  body('name').isLength({ min: 2, max: 100 }).withMessage('Name must be 2-100 characters'),
  body('email').isEmail().withMessage('Invalid email address'),
  body('address.street').isLength({ min: 10, max: 500 }).withMessage('Address must be 10-500 characters'),
  body('address.city').isLength({ min: 2, max: 100 }).withMessage('City must be 2-100 characters'),
  body('address.state').isLength({ min: 2, max: 100 }).withMessage('State must be 2-100 characters'),
  body('address.pincode').matches(/^[0-9]{6}$/).withMessage('Invalid pincode')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      });
    }

    const { phone, name, email, address } = req.body;

    // Check if buyer already exists
    let buyer = await Buyer.findOne({ where: { phone } });
    
    if (buyer) {
      return res.status(400).json({ 
        error: 'Buyer already exists. Please use login instead.' 
      });
    }

    // Create new buyer
    buyer = await Buyer.create({
      phone,
      name,
      email,
      addresses: [{
        id: Date.now().toString(),
        ...address,
        isDefault: true,
        createdAt: new Date()
      }],
      verification: {
        phoneVerified: true,
        emailVerified: false
      },
      analytics: {
        firstOrderDate: null,
        lastActiveDate: new Date(),
        sessionCount: 1
      }
    });

    // Generate token
    const token = jwt.sign(
      { id: buyer.id, phone: buyer.phone, type: 'buyer' },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );

    res.status(201).json({
      success: true,
      message: 'Profile created successfully',
      token,
      buyer: {
        id: buyer.id,
        phone: buyer.phone,
        name: buyer.name,
        email: buyer.email,
        addresses: buyer.addresses,
        preferences: buyer.preferences,
        profile: buyer.profile
      }
    });

  } catch (error) {
    console.error('Complete profile error:', error);
    res.status(500).json({ error: 'Failed to create profile' });
  }
});

// Get buyer profile
router.get('/profile', authenticateBuyer, async (req, res) => {
  try {
    res.json({
      success: true,
      buyer: {
        id: req.buyer.id,
        phone: req.buyer.phone,
        name: req.buyer.name,
        email: req.buyer.email,
        addresses: req.buyer.addresses,
        preferences: req.buyer.preferences,
        profile: req.buyer.profile,
        verification: req.buyer.verification,
        createdAt: req.buyer.createdAt
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Failed to get profile' });
  }
});

// Update buyer profile
router.put('/profile', authenticateBuyer, [
  body('name').optional().isLength({ min: 2, max: 100 }),
  body('email').optional().isEmail()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: errors.array() 
      });
    }

    const { name, email } = req.body;
    const updateData = {};

    if (name) updateData.name = name;
    if (email) updateData.email = email;

    await req.buyer.update(updateData);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      buyer: {
        id: req.buyer.id,
        phone: req.buyer.phone,
        name: req.buyer.name,
        email: req.buyer.email,
        addresses: req.buyer.addresses,
        preferences: req.buyer.preferences,
        profile: req.buyer.profile
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Address Management Routes

// Get all addresses
router.get('/addresses', authenticateBuyer, async (req, res) => {
  try {
    res.json({
      success: true,
      addresses: req.buyer.addresses || []
    });
  } catch (error) {
    console.error('Get addresses error:', error);
    res.status(500).json({ error: 'Failed to get addresses' });
  }
});

// Add new address
router.post('/addresses', authenticateBuyer, [
  body('street').isLength({ min: 10, max: 500 }).withMessage('Address must be 10-500 characters'),
  body('city').isLength({ min: 2, max: 100 }).withMessage('City must be 2-100 characters'),
  body('state').isLength({ min: 2, max: 100 }).withMessage('State must be 2-100 characters'),
  body('pincode').matches(/^[0-9]{6}$/).withMessage('Invalid pincode'),
  body('label').optional().isLength({ min: 1, max: 50 }).withMessage('Label must be 1-50 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { street, city, state, pincode, label } = req.body;

    await req.buyer.addAddress({
      street,
      city,
      state,
      pincode,
      label: label || 'Home'
    });

    res.json({
      success: true,
      message: 'Address added successfully',
      addresses: req.buyer.addresses
    });

  } catch (error) {
    console.error('Add address error:', error);
    res.status(500).json({ error: 'Failed to add address' });
  }
});

// Update address
router.put('/addresses/:addressId', authenticateBuyer, [
  body('street').optional().isLength({ min: 10, max: 500 }),
  body('city').optional().isLength({ min: 2, max: 100 }),
  body('state').optional().isLength({ min: 2, max: 100 }),
  body('pincode').optional().matches(/^[0-9]{6}$/),
  body('label').optional().isLength({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { addressId } = req.params;
    const updates = req.body;

    await req.buyer.updateAddress(addressId, updates);

    res.json({
      success: true,
      message: 'Address updated successfully',
      addresses: req.buyer.addresses
    });

  } catch (error) {
    console.error('Update address error:', error);
    res.status(500).json({ error: 'Failed to update address' });
  }
});

// Set default address
router.patch('/addresses/:addressId/default', authenticateBuyer, async (req, res) => {
  try {
    const { addressId } = req.params;

    await req.buyer.setDefaultAddress(addressId);

    res.json({
      success: true,
      message: 'Default address updated successfully',
      addresses: req.buyer.addresses
    });

  } catch (error) {
    console.error('Set default address error:', error);
    res.status(500).json({ error: 'Failed to set default address' });
  }
});

// Delete address
router.delete('/addresses/:addressId', authenticateBuyer, async (req, res) => {
  try {
    const { addressId } = req.params;

    const addresses = req.buyer.addresses.filter(addr => addr.id !== addressId);

    // If deleted address was default, set first address as default
    if (addresses.length > 0) {
      const wasDefault = req.buyer.addresses.find(addr => addr.id === addressId)?.isDefault;
      if (wasDefault) {
        addresses[0].isDefault = true;
      }
    }

    await req.buyer.update({ addresses });

    res.json({
      success: true,
      message: 'Address deleted successfully',
      addresses: addresses
    });

  } catch (error) {
    console.error('Delete address error:', error);
    res.status(500).json({ error: 'Failed to delete address' });
  }
});

module.exports = router;
