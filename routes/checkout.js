const express = require('express');
const { body, validationResult } = require('express-validator');
const { Product, Order, Seller } = require('../models');
const { processPayment, calculateOrderTotal, verifyRazorpaySignature } = require('../services/paymentService');
const { optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Get product for checkout
router.get('/:shortCode', async (req, res) => {
  try {
    const product = await Product.findOne({
      where: { 
        shortCode: req.params.shortCode,
        isActive: true,
        isVisible: true
      },
      include: [{
        model: Seller,
        as: 'seller',
        attributes: ['name', 'isVerified', 'verificationBadge', 'plan']
      }]
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found or unavailable'
      });
    }

    // Check inventory
    if (!product.isInStock()) {
      return res.status(400).json({
        error: 'Product is out of stock'
      });
    }

    // Track view
    const analytics = { ...product.analytics };
    analytics.views += 1;
    await product.update({ analytics });

    res.json({
      product: {
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        originalPrice: product.originalPrice,
        currency: product.currency,
        images: product.images,
        paymentOptions: product.paymentOptions,
        shipping: product.shipping,
        inventory: {
          quantity: product.inventory.quantity,
          inStock: product.isInStock()
        },
        seller: product.seller
      }
    });

  } catch (error) {
    console.error('Get checkout product error:', error);
    res.status(500).json({
      error: 'Failed to fetch product'
    });
  }
});

// Create order
router.post('/:shortCode/order', [
  body('buyer.name').isLength({ min: 2, max: 100 }).withMessage('Buyer name required'),
  body('buyer.phone').isMobilePhone().withMessage('Valid phone number required'),
  body('buyer.email').optional().isEmail().withMessage('Valid email required'),
  body('buyer.address.line1').isLength({ min: 5, max: 200 }).withMessage('Address line 1 required'),
  body('buyer.address.city').isLength({ min: 2, max: 100 }).withMessage('City required'),
  body('buyer.address.state').isLength({ min: 2, max: 100 }).withMessage('State required'),
  body('buyer.address.pincode').isLength({ min: 6, max: 6 }).withMessage('Valid pincode required'),
  body('quantity').isInt({ min: 1, max: 10 }).withMessage('Quantity must be 1-10'),
  body('paymentMethod').isIn(['cod', 'prepaid']).withMessage('Invalid payment method')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { buyer, quantity, paymentMethod, notes } = req.body;

    // Get product
    const product = await Product.findOne({
      where: { 
        shortCode: req.params.shortCode,
        isActive: true,
        isVisible: true
      },
      include: [{
        model: Seller,
        as: 'seller',
        attributes: ['id', 'name', 'plan']
      }]
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found or unavailable'
      });
    }

    // Check inventory
    if (!product.isInStock(quantity)) {
      return res.status(400).json({
        error: 'Insufficient stock'
      });
    }

    // Check payment method availability
    if (paymentMethod === 'cod' && !product.paymentOptions.cod) {
      return res.status(400).json({
        error: 'COD not available for this product'
      });
    }

    if (paymentMethod === 'prepaid' && !product.paymentOptions.prepaid) {
      return res.status(400).json({
        error: 'Prepaid payment not available for this product'
      });
    }

    // Calculate pricing
    const codCharges = paymentMethod === 'cod' ? product.paymentOptions.codCharges : 0;
    const pricing = calculateOrderTotal(
      product.price,
      quantity,
      product.shipping.shippingCharges,
      codCharges,
      product.seller.plan
    );

    // Create order
    const order = await Order.create({
      sellerId: product.sellerId,
      productId: product.id,
      buyer,
      product: {
        name: product.name,
        price: product.price,
        quantity,
        image: product.images[0]?.url
      },
      pricing,
      payment: {
        method: paymentMethod,
        status: paymentMethod === 'cod' ? 'pending' : 'pending'
      },
      notes: {
        buyerNotes: notes
      }
    });

    // Update inventory
    await product.updateInventory(quantity);

    // Track analytics
    const analytics = { ...product.analytics };
    analytics.orders += 1;
    analytics.revenue += pricing.total;
    if (analytics.clicks > 0) {
      analytics.conversionRate = (analytics.orders / analytics.clicks) * 100;
    }
    await product.update({ analytics });

    // Process payment
    if (paymentMethod === 'prepaid') {
      const paymentResult = await processPayment({
        method: 'prepaid',
        amount: pricing.total,
        currency: product.currency,
        receipt: order.orderId,
        orderId: order.id
      });

      if (paymentResult.success) {
        await order.update({
          payment: {
            ...order.payment,
            gatewayOrderId: paymentResult.order.id,
            status: 'pending'
          }
        });

        res.status(201).json({
          message: 'Order created successfully',
          order: order.getSummary(),
          payment: {
            gateway: 'razorpay',
            orderId: paymentResult.order.id,
            amount: paymentResult.order.amount,
            currency: paymentResult.order.currency,
            key: process.env.RAZORPAY_KEY_ID
          }
        });
      } else {
        await order.destroy();
        res.status(500).json({
          error: 'Payment processing failed',
          details: paymentResult.error
        });
      }
    } else {
      // COD order
      await order.updateStatus('confirmed', 'COD order confirmed');
      
      res.status(201).json({
        message: 'COD order created successfully',
        order: order.getSummary()
      });
    }

  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({
      error: 'Failed to create order'
    });
  }
});

// Verify payment
router.post('/verify-payment', [
  body('razorpay_order_id').notEmpty().withMessage('Order ID required'),
  body('razorpay_payment_id').notEmpty().withMessage('Payment ID required'),
  body('razorpay_signature').notEmpty().withMessage('Signature required'),
  body('order_id').isUUID().withMessage('Valid order ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { razorpay_order_id, razorpay_payment_id, razorpay_signature, order_id } = req.body;

    // Find order
    const order = await Order.findByPk(order_id);
    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    // Verify signature
    const isValidSignature = verifyRazorpaySignature(
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature
    );

    if (!isValidSignature) {
      return res.status(400).json({
        error: 'Invalid payment signature'
      });
    }

    // Update order
    await order.update({
      payment: {
        ...order.payment,
        status: 'paid',
        gatewayPaymentId: razorpay_payment_id,
        paidAt: new Date()
      }
    });

    await order.updateStatus('confirmed', 'Payment verified and confirmed');

    res.json({
      message: 'Payment verified successfully',
      order: order.getSummary()
    });

  } catch (error) {
    console.error('Verify payment error:', error);
    res.status(500).json({
      error: 'Payment verification failed'
    });
  }
});

// Get order status
router.get('/order/:orderId', async (req, res) => {
  try {
    const order = await Order.findOne({
      where: { orderId: req.params.orderId }
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    res.json({
      order: order.getSummary(),
      timeline: order.timeline
    });

  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      error: 'Failed to fetch order'
    });
  }
});

module.exports = router;
