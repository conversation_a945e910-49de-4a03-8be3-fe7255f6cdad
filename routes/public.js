const express = require('express');
const { body, validationResult } = require('express-validator');
const { Product, Order, Seller } = require('../models');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Get product by short code (public endpoint)
router.get('/product/:shortCode', async (req, res) => {
  try {
    const { shortCode } = req.params;
    
    console.log(`🔍 Public: Fetching product with shortCode: ${shortCode}`);
    
    const product = await Product.findOne({
      where: { 
        shortCode,
        isActive: true,
        isVisible: true
      },
      include: [{
        model: Seller,
        attributes: ['name', 'isVerified', 'verificationBadge']
      }]
    });

    if (!product) {
      console.log(`❌ Public: Product not found for shortCode: ${shortCode}`);
      return res.status(404).json({
        error: 'Product not found or not available'
      });
    }

    // Increment view count
    await product.increment('analytics.views');

    console.log(`✅ Public: Product found: ${product.name}`);
    
    res.json({
      success: true,
      product: {
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        originalPrice: product.originalPrice,
        currency: product.currency,
        images: product.images,
        category: product.category,
        inventory: product.inventory,
        paymentOptions: product.paymentOptions,
        shipping: product.shipping,
        shortCode: product.shortCode,
        checkoutLink: product.checkoutLink,
        seller: product.Seller ? {
          name: product.Seller.name,
          isVerified: product.Seller.isVerified,
          verificationBadge: product.Seller.verificationBadge
        } : null
      }
    });

  } catch (error) {
    console.error('Get product by shortCode error:', error);
    res.status(500).json({
      error: 'Failed to fetch product'
    });
  }
});

// Create order (public endpoint)
router.post('/orders', [
  body('shortCode').notEmpty().withMessage('Product shortCode is required'),
  body('buyer.name').isLength({ min: 2, max: 100 }).withMessage('Name must be 2-100 characters'),
  body('buyer.phone').matches(/^[+]?[0-9]{10,15}$/).withMessage('Invalid phone number'),
  body('buyer.email').isEmail().withMessage('Invalid email address'),
  body('buyer.address.street').isLength({ min: 10, max: 500 }).withMessage('Address must be 10-500 characters'),
  body('buyer.address.city').isLength({ min: 2, max: 100 }).withMessage('City must be 2-100 characters'),
  body('buyer.address.state').isLength({ min: 2, max: 100 }).withMessage('State must be 2-100 characters'),
  body('buyer.address.pincode').matches(/^[0-9]{6}$/).withMessage('Invalid pincode'),
  body('quantity').isInt({ min: 1, max: 100 }).withMessage('Quantity must be between 1-100'),
  body('paymentMethod').isIn(['cod', 'prepaid']).withMessage('Invalid payment method')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      shortCode,
      buyer,
      quantity,
      paymentMethod,
      pricing
    } = req.body;

    console.log(`🛒 Public: Creating order for product: ${shortCode}`);

    // Find the product
    const product = await Product.findOne({
      where: { 
        shortCode,
        isActive: true,
        isVisible: true
      },
      include: [{
        model: Seller,
        attributes: ['id', 'name', 'upiId', 'bankDetails']
      }]
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found or not available'
      });
    }

    // Check inventory
    if (product.inventory.quantity < quantity) {
      return res.status(400).json({
        error: 'Insufficient stock available'
      });
    }

    // Generate unique order ID
    const orderId = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // Calculate pricing
    const productPrice = product.price;
    const subtotal = productPrice * quantity;
    const shippingCharges = product.shipping.freeShipping ? 0 : (product.shipping.shippingCharges || 50);
    const codCharges = paymentMethod === 'cod' ? (product.paymentOptions.codCharges || 0) : 0;
    const platformFee = 10; // Fixed platform fee
    const total = subtotal + shippingCharges + codCharges + platformFee;

    // Create order
    const order = await Order.create({
      orderId,
      sellerId: product.sellerId,
      productId: product.id,
      buyer: {
        name: buyer.name,
        phone: buyer.phone,
        email: buyer.email,
        address: buyer.address
      },
      product: {
        id: product.id,
        name: product.name,
        price: productPrice,
        image: product.images[0] || null,
        shortCode: product.shortCode
      },
      pricing: {
        productPrice,
        quantity,
        subtotal,
        shippingCharges,
        codCharges,
        platformFee,
        total
      },
      payment: {
        method: paymentMethod,
        status: 'pending'
      },
      status: 'pending',
      timeline: [{
        status: 'pending',
        timestamp: new Date(),
        message: 'Order placed successfully'
      }]
    });

    // Update product inventory
    await product.decrement('inventory.quantity', { by: quantity });
    
    // Update product analytics
    await product.increment([
      'analytics.orders',
      ['analytics.revenue', total]
    ]);

    // Update seller analytics
    await product.Seller.increment([
      'totalOrders',
      ['totalRevenue', total]
    ]);

    console.log(`✅ Public: Order created successfully: ${orderId}`);

    // For prepaid orders, you would integrate with payment gateway here
    let paymentUrl = null;
    if (paymentMethod === 'prepaid') {
      // TODO: Integrate with Razorpay or other payment gateway
      // paymentUrl = await createPaymentLink(order);
      console.log('💳 Prepaid payment integration needed');
    }

    res.status(201).json({
      success: true,
      message: 'Order placed successfully',
      orderId: order.orderId,
      order: {
        id: order.orderId,
        status: order.status,
        total: total,
        paymentMethod: paymentMethod,
        estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
      },
      paymentUrl
    });

  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({
      error: 'Failed to create order'
    });
  }
});

// Get order status (public endpoint)
router.get('/order/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    console.log(`📋 Public: Fetching order: ${orderId}`);
    
    const order = await Order.findOne({
      where: { orderId },
      include: [{
        model: Product,
        attributes: ['name', 'images', 'shortCode']
      }, {
        model: Seller,
        attributes: ['name', 'phone', 'email']
      }]
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    console.log(`✅ Public: Order found: ${orderId}`);
    
    res.json({
      success: true,
      order: {
        orderId: order.orderId,
        status: order.status,
        buyer: order.buyer,
        product: order.product,
        pricing: order.pricing,
        payment: order.payment,
        timeline: order.timeline,
        createdAt: order.createdAt,
        seller: order.Seller ? {
          name: order.Seller.name,
          phone: order.Seller.phone,
          email: order.Seller.email
        } : null
      }
    });

  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      error: 'Failed to fetch order'
    });
  }
});

module.exports = router;
