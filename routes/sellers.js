const express = require('express');
const { body, validationResult } = require('express-validator');
const { Op } = require('sequelize');
const { Seller, Product, Order } = require('../models');
const { authenticate, requireVerified } = require('../middleware/auth');

const router = express.Router();

// Get seller dashboard overview
router.get('/dashboard', authenticate, async (req, res) => {
  try {
    const sellerId = req.seller.id;

    // Get product stats
    const productStats = await Product.findAll({
      where: { sellerId },
      attributes: [
        [Product.sequelize.fn('COUNT', Product.sequelize.col('id')), 'totalProducts'],
        [Product.sequelize.fn('COUNT', Product.sequelize.literal("CASE WHEN \"isActive\" = true THEN 1 END")), 'activeProducts'],
        [Product.sequelize.fn('SUM', Product.sequelize.col('inventory.quantity')), 'totalInventory']
      ],
      raw: true
    });

    // Get order stats
    const orderStats = await Order.findAll({
      where: { sellerId },
      attributes: [
        [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'totalOrders'],
        [Order.sequelize.fn('SUM', Order.sequelize.col('pricing.total')), 'totalRevenue'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN status = 'pending' THEN 1 END")), 'pendingOrders'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN status = 'delivered' THEN 1 END")), 'deliveredOrders']
      ],
      raw: true
    });

    // Get recent orders
    const recentOrders = await Order.findAll({
      where: { sellerId },
      limit: 5,
      order: [['createdAt', 'DESC']],
      include: [{
        model: Product,
        as: 'productDetails',
        attributes: ['name', 'shortCode']
      }]
    });

    // Get low stock products
    const lowStockProducts = await Product.findAll({
      where: {
        sellerId,
        isActive: true,
        [Op.and]: [
          Product.sequelize.literal('"inventory"->\'trackInventory\' = \'true\''),
          Product.sequelize.literal('CAST("inventory"->\'quantity\' AS INTEGER) <= CAST("inventory"->\'lowStockThreshold\' AS INTEGER)')
        ]
      },
      limit: 10,
      attributes: ['id', 'name', 'inventory', 'shortCode']
    });

    res.json({
      seller: req.seller.getProfileSummary(),
      stats: {
        products: productStats[0] || { totalProducts: 0, activeProducts: 0, totalInventory: 0 },
        orders: orderStats[0] || { totalOrders: 0, totalRevenue: 0, pendingOrders: 0, deliveredOrders: 0 }
      },
      recentOrders: recentOrders.map(order => ({
        ...order.getSummary(),
        product: order.productDetails
      })),
      lowStockProducts: lowStockProducts.map(product => ({
        id: product.id,
        name: product.name,
        shortCode: product.shortCode,
        currentStock: product.inventory.quantity,
        threshold: product.inventory.lowStockThreshold
      }))
    });

  } catch (error) {
    console.error('Get dashboard error:', error);
    res.status(500).json({
      error: 'Failed to fetch dashboard data'
    });
  }
});

// Update seller business details
router.put('/business', authenticate, [
  body('upiId').optional().isLength({ min: 1 }).withMessage('UPI ID required'),
  body('gstin').optional().isLength({ min: 15, max: 15 }).withMessage('GSTIN must be 15 characters'),
  body('bankDetails.accountNumber').optional().isLength({ min: 8 }).withMessage('Valid account number required'),
  body('bankDetails.ifscCode').optional().isLength({ min: 11, max: 11 }).withMessage('Valid IFSC code required'),
  body('bankDetails.accountHolderName').optional().isLength({ min: 2 }).withMessage('Account holder name required'),
  body('bankDetails.bankName').optional().isLength({ min: 2 }).withMessage('Bank name required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { upiId, gstin, bankDetails } = req.body;
    const seller = req.seller;

    const updateData = {};
    if (upiId !== undefined) updateData.upiId = upiId;
    if (gstin !== undefined) updateData.gstin = gstin;
    if (bankDetails !== undefined) updateData.bankDetails = bankDetails;

    await seller.update(updateData);

    res.json({
      message: 'Business details updated successfully',
      seller: seller.getProfileSummary()
    });

  } catch (error) {
    console.error('Update business details error:', error);
    res.status(500).json({
      error: 'Failed to update business details'
    });
  }
});

// Get seller analytics
router.get('/analytics', authenticate, async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const sellerId = req.seller.id;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get revenue trend
    const revenueTrend = await Order.findAll({
      where: {
        sellerId,
        createdAt: {
          [Op.gte]: startDate,
          [Op.lte]: endDate
        }
      },
      attributes: [
        [Order.sequelize.fn('DATE', Order.sequelize.col('createdAt')), 'date'],
        [Order.sequelize.fn('SUM', Order.sequelize.col('pricing.total')), 'revenue'],
        [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'orders']
      ],
      group: [Order.sequelize.fn('DATE', Order.sequelize.col('createdAt'))],
      order: [[Order.sequelize.fn('DATE', Order.sequelize.col('createdAt')), 'ASC']],
      raw: true
    });

    // Get top products
    const topProducts = await Order.findAll({
      where: {
        sellerId,
        createdAt: {
          [Op.gte]: startDate,
          [Op.lte]: endDate
        }
      },
      attributes: [
        'productId',
        [Order.sequelize.fn('COUNT', Order.sequelize.col('Order.id')), 'orderCount'],
        [Order.sequelize.fn('SUM', Order.sequelize.col('pricing.total')), 'revenue']
      ],
      include: [{
        model: Product,
        as: 'productDetails',
        attributes: ['name', 'shortCode', 'price']
      }],
      group: ['productId', 'productDetails.id'],
      order: [[Order.sequelize.fn('COUNT', Order.sequelize.col('Order.id')), 'DESC']],
      limit: 10
    });

    res.json({
      period,
      revenueTrend,
      topProducts: topProducts.map(item => ({
        product: item.productDetails,
        orderCount: parseInt(item.dataValues.orderCount),
        revenue: parseFloat(item.dataValues.revenue || 0)
      }))
    });

  } catch (error) {
    console.error('Get analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch analytics'
    });
  }
});

module.exports = router;
