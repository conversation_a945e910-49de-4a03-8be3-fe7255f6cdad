const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Op } = require('sequelize');
const { Order, Product, Seller } = require('../models');
const { authenticate } = require('../middleware/auth');
const { refundPayment } = require('../services/paymentService');

const router = express.Router();

// Get all orders for seller
router.get('/', authenticate, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      paymentMethod,
      paymentStatus,
      startDate,
      endDate,
      search
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = { sellerId: req.seller.id };

    // Add filters
    if (status) whereClause.status = status;
    if (paymentMethod) whereClause['payment.method'] = paymentMethod;
    if (paymentStatus) whereClause['payment.status'] = paymentStatus;

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt[Op.gte] = new Date(startDate);
      if (endDate) whereClause.createdAt[Op.lte] = new Date(endDate);
    }

    if (search) {
      whereClause[Op.or] = [
        { orderId: { [Op.iLike]: `%${search}%` } },
        { 'buyer.name': { [Op.iLike]: `%${search}%` } },
        { 'buyer.phone': { [Op.iLike]: `%${search}%` } }
      ];
    }

    const orders = await Order.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [{
        model: Product,
        as: 'productDetails',
        attributes: ['name', 'images', 'shortCode']
      }]
    });

    // Calculate summary stats
    const stats = await Order.findAll({
      where: { sellerId: req.seller.id },
      attributes: [
        [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'totalOrders'],
        [Order.sequelize.fn('SUM', Order.sequelize.literal("(pricing->>'total')::numeric")), 'totalRevenue'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN status = 'delivered' THEN 1 END")), 'deliveredOrders'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN (rto->>'isRto')::boolean = true THEN 1 END")), 'rtoOrders']
      ],
      raw: true
    });

    res.json({
      orders: orders.rows.map(order => ({
        ...order.getSummary(),
        product: order.productDetails
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: orders.count,
        pages: Math.ceil(orders.count / limit)
      },
      stats: stats[0] || {
        totalOrders: 0,
        totalRevenue: 0,
        deliveredOrders: 0,
        rtoOrders: 0
      }
    });

  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      error: 'Failed to fetch orders'
    });
  }
});

// Get single order
router.get('/:id', authenticate, async (req, res) => {
  try {
    const order = await Order.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      },
      include: [{
        model: Product,
        as: 'productDetails',
        attributes: ['name', 'images', 'shortCode', 'checkoutLink']
      }]
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    res.json({
      order: {
        ...order.toJSON(),
        product: order.productDetails
      }
    });

  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      error: 'Failed to fetch order'
    });
  }
});

// Update order status
router.patch('/:id/status', authenticate, [
  body('status').isIn([
    'pending', 'confirmed', 'processing', 'shipped',
    'delivered', 'cancelled', 'returned'
  ]).withMessage('Invalid status'),
  body('note').optional().isLength({ max: 500 }).withMessage('Note too long')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { status, note = '' } = req.body;

    const order = await Order.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    // Validate status transition
    const validTransitions = {
      pending: ['confirmed', 'cancelled'],
      confirmed: ['processing', 'cancelled'],
      processing: ['shipped', 'cancelled'],
      shipped: ['delivered', 'returned'],
      delivered: ['returned'],
      cancelled: [],
      returned: []
    };

    if (!validTransitions[order.status].includes(status)) {
      return res.status(400).json({
        error: `Cannot change status from ${order.status} to ${status}`
      });
    }

    await order.updateStatus(status, note, req.seller.name);

    // Handle special status updates
    if (status === 'shipped') {
      const fulfillment = { ...order.fulfillment };
      fulfillment.isShipped = true;
      fulfillment.shippedAt = new Date();
      await order.update({ fulfillment });
    }

    if (status === 'delivered') {
      const fulfillment = { ...order.fulfillment };
      fulfillment.isDelivered = true;
      fulfillment.deliveredAt = new Date();
      await order.update({ fulfillment });
    }

    res.json({
      message: 'Order status updated successfully',
      order: order.getSummary()
    });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      error: 'Failed to update order status'
    });
  }
});

// Add tracking information
router.patch('/:id/tracking', authenticate, [
  body('trackingNumber').isLength({ min: 5, max: 50 }).withMessage('Valid tracking number required'),
  body('carrier').isLength({ min: 2, max: 100 }).withMessage('Carrier name required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { trackingNumber, carrier } = req.body;

    const order = await Order.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    if (!['processing', 'shipped'].includes(order.status)) {
      return res.status(400).json({
        error: 'Can only add tracking for processing or shipped orders'
      });
    }

    const fulfillment = { ...order.fulfillment };
    fulfillment.trackingNumber = trackingNumber;
    fulfillment.carrier = carrier;

    await order.update({ fulfillment });

    res.json({
      message: 'Tracking information added successfully',
      tracking: {
        trackingNumber,
        carrier
      }
    });

  } catch (error) {
    console.error('Add tracking error:', error);
    res.status(500).json({
      error: 'Failed to add tracking information'
    });
  }
});

// Cancel order
router.post('/:id/cancel', authenticate, [
  body('reason').isLength({ min: 5, max: 200 }).withMessage('Cancellation reason required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { reason } = req.body;

    const order = await Order.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    if (!order.canBeCancelled()) {
      return res.status(400).json({
        error: 'Order cannot be cancelled at this stage'
      });
    }

    // Process refund if payment was made
    if (order.payment.method === 'prepaid' && order.payment.status === 'paid') {
      try {
        const refundResult = await refundPayment(order.payment.gatewayPaymentId);
        if (refundResult.success) {
          const payment = { ...order.payment };
          payment.status = 'refunded';
          await order.update({ payment });
        }
      } catch (refundError) {
        console.error('Refund error:', refundError);
        // Continue with cancellation even if refund fails
      }
    }

    await order.updateStatus('cancelled', reason, req.seller.name);

    // Restore inventory
    const product = await Product.findByPk(order.productId);
    if (product && product.inventory.trackInventory) {
      const inventory = { ...product.inventory };
      inventory.quantity += order.product.quantity;
      await product.update({ inventory });
    }

    res.json({
      message: 'Order cancelled successfully',
      order: order.getSummary()
    });

  } catch (error) {
    console.error('Cancel order error:', error);
    res.status(500).json({
      error: 'Failed to cancel order'
    });
  }
});

// Mark order as RTO
router.post('/:id/rto', authenticate, [
  body('reason').isLength({ min: 5, max: 200 }).withMessage('RTO reason required'),
  body('rtoCharges').optional().isFloat({ min: 0 }).withMessage('RTO charges must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { reason, rtoCharges = 0 } = req.body;

    const order = await Order.findOne({
      where: {
        id: req.params.id,
        sellerId: req.seller.id
      }
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found'
      });
    }

    if (order.status !== 'shipped') {
      return res.status(400).json({
        error: 'Only shipped orders can be marked as RTO'
      });
    }

    const rto = {
      isRto: true,
      reason,
      rtoDate: new Date(),
      rtoCharges
    };

    await order.update({ rto });
    await order.updateStatus('returned', `RTO: ${reason}`, req.seller.name);

    // Restore inventory
    const product = await Product.findByPk(order.productId);
    if (product && product.inventory.trackInventory) {
      const inventory = { ...product.inventory };
      inventory.quantity += order.product.quantity;
      await product.update({ inventory });
    }

    res.json({
      message: 'Order marked as RTO successfully',
      order: order.getSummary()
    });

  } catch (error) {
    console.error('Mark RTO error:', error);
    res.status(500).json({
      error: 'Failed to mark order as RTO'
    });
  }
});

// Get order analytics/dashboard
router.get('/analytics/dashboard', authenticate, async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    const whereClause = {
      sellerId: req.seller.id,
      createdAt: {
        [Op.gte]: startDate,
        [Op.lte]: endDate
      }
    };

    // Get overall stats
    const stats = await Order.findAll({
      where: { sellerId: req.seller.id },
      attributes: [
        [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'totalOrders'],
        [Order.sequelize.fn('SUM', Order.sequelize.literal("(pricing->>'total')::numeric")), 'totalRevenue'],
        [Order.sequelize.fn('AVG', Order.sequelize.literal("(pricing->>'total')::numeric")), 'averageOrderValue'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN status = 'delivered' THEN 1 END")), 'deliveredOrders'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN (rto->>'isRto')::boolean = true THEN 1 END")), 'rtoOrders'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN payment->>'method' = 'cod' THEN 1 END")), 'codOrders'],
        [Order.sequelize.fn('COUNT', Order.sequelize.literal("CASE WHEN payment->>'method' = 'prepaid' THEN 1 END")), 'prepaidOrders']
      ],
      raw: true
    });

    // Get recent orders
    const recentOrders = await Order.findAll({
      where: whereClause,
      limit: 10,
      order: [['createdAt', 'DESC']],
      include: [{
        model: Product,
        as: 'productDetails',
        attributes: ['name', 'shortCode']
      }]
    });

    // Get status distribution
    const statusStats = await Order.findAll({
      where: whereClause,
      attributes: [
        'status',
        [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    const result = stats[0] || {};
    result.rtoRate = result.totalOrders > 0 ? ((result.rtoOrders / result.totalOrders) * 100).toFixed(2) : '0';
    result.deliveryRate = result.totalOrders > 0 ? ((result.deliveredOrders / result.totalOrders) * 100).toFixed(2) : '0';

    res.json({
      stats: result,
      recentOrders: recentOrders.map(order => ({
        ...order.getSummary(),
        product: order.productDetails
      })),
      statusDistribution: statusStats,
      period
    });

  } catch (error) {
    console.error('Get dashboard error:', error);
    res.status(500).json({
      error: 'Failed to fetch dashboard data'
    });
  }
});

module.exports = router;
