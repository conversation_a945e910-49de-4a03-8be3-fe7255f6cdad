{"info": {"_postman_id": "swiftcheckout-api-collection", "name": "SwiftCheckout API", "description": "Complete API collection for SwiftCheckout - Checkout Infrastructure for Instagram & WhatsApp Sellers", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Serve Specific Request ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/request/f655713f-55f8-4872-ac90-cc47c5e65c25", "host": ["{{baseUrl}}"], "path": ["request", "f655713f-55f8-4872-ac90-cc47c5e65c25"]}}}]}, {"name": "Authentication", "item": [{"name": "Request OTP (Phone)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+91**********\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/request-otp", "host": ["{{baseUrl}}"], "path": ["api", "auth", "request-otp"]}}}, {"name": "Request OTP (Email)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+91**********\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/request-otp", "host": ["{{baseUrl}}"], "path": ["api", "auth", "request-otp"]}}}, {"name": "Verify OTP & Login/Signup", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('authToken', response.token);", "        pm.environment.set('sellerId', response.seller.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+91**********\",\n  \"otp\": \"123456\",\n  \"name\": \"<PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-otp", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-otp"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"upiId\": \"john@paytm\",\n  \"gstin\": \"22AAAAA0000A1Z5\",\n  \"bankDetails\": {\n    \"accountNumber\": \"**********\",\n    \"ifscCode\": \"HDFC0000123\",\n    \"accountHolderName\": \"<PERSON>\",\n    \"bankName\": \"HDFC Bank\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}}]}, {"name": "Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/products?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Create Product", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.product && response.product.id) {", "        pm.environment.set('productId', response.product.id);", "        pm.environment.set('shortCode', response.product.shortCode);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Wireless Headphones\",\n  \"description\": \"High-quality wireless headphones with noise cancellation\",\n  \"price\": 2999,\n  \"originalPrice\": 3999,\n  \"category\": \"Electronics\",\n  \"tags\": [\"wireless\", \"headphones\", \"audio\"],\n  \"inventory\": {\n    \"quantity\": 50,\n    \"trackInventory\": true,\n    \"lowStockThreshold\": 10\n  },\n  \"paymentOptions\": {\n    \"cod\": true,\n    \"prepaid\": true,\n    \"codCharges\": 50\n  },\n  \"shipping\": {\n    \"weight\": 0.5,\n    \"freeShipping\": false,\n    \"shippingCharges\": 99\n  },\n  \"images\": [\n    {\n      \"url\": \"https://example.com/headphones1.jpg\",\n      \"alt\": \"Wireless Headphones Front View\",\n      \"isPrimary\": true\n    },\n    {\n      \"url\": \"https://example.com/headphones2.jpg\",\n      \"alt\": \"Wireless Headphones Side View\",\n      \"isPrimary\": false\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/products", "host": ["{{baseUrl}}"], "path": ["api", "products"]}}}, {"name": "Get Single Product", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}"]}}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Wireless Headphones - Updated\",\n  \"price\": 2799,\n  \"description\": \"Updated description with new features\",\n  \"inventory\": {\n    \"quantity\": 45,\n    \"trackInventory\": true,\n    \"lowStockThreshold\": 5\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}"]}}}, {"name": "Toggle Product Active Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/products/{{productId}}/toggle-active", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}", "toggle-active"]}}}, {"name": "Update Inventory", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 30,\n  \"trackInventory\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/products/{{productId}}/inventory", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}", "inventory"]}}}, {"name": "Get Product Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/products/{{productId}}/analytics", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}", "analytics"]}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}"]}}}]}, {"name": "Checkout (Public)", "item": [{"name": "Get Product for Checkout", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/checkout/{{shortCode}}", "host": ["{{baseUrl}}"], "path": ["checkout", "{{shortCode}}"]}}}, {"name": "Create COD Order", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.order && response.order.orderId) {", "        pm.environment.set('orderId', response.order.orderId);", "        pm.environment.set('orderUuid', response.order.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"buyer\": {\n    \"name\": \"<PERSON>er\",\n    \"phone\": \"+************\",\n    \"email\": \"<EMAIL>\",\n    \"address\": {\n      \"line1\": \"123 Main Street, Apartment 4B\",\n      \"line2\": \"Near City Mall\",\n      \"city\": \"Mumbai\",\n      \"state\": \"Maharashtra\",\n      \"pincode\": \"400001\",\n      \"country\": \"India\"\n    }\n  },\n  \"quantity\": 2,\n  \"paymentMethod\": \"cod\",\n  \"notes\": \"Please call before delivery\"\n}"}, "url": {"raw": "{{baseUrl}}/checkout/{{shortCode}}/order", "host": ["{{baseUrl}}"], "path": ["checkout", "{{shortCode}}", "order"]}}}, {"name": "Create Prepaid Order", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.payment && response.payment.orderId) {", "        pm.environment.set('razorpayOrderId', response.payment.orderId);", "    }", "    if (response.order && response.order.orderId) {", "        pm.environment.set('orderId', response.order.orderId);", "        pm.environment.set('orderUuid', response.order.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"buyer\": {\n    \"name\": \"<PERSON>er\",\n    \"phone\": \"+************\",\n    \"email\": \"<EMAIL>\",\n    \"address\": {\n      \"line1\": \"456 Oak Avenue\",\n      \"city\": \"Delhi\",\n      \"state\": \"Delhi\",\n      \"pincode\": \"110001\",\n      \"country\": \"India\"\n    }\n  },\n  \"quantity\": 1,\n  \"paymentMethod\": \"prepaid\"\n}"}, "url": {"raw": "{{baseUrl}}/checkout/{{shortCode}}/order", "host": ["{{baseUrl}}"], "path": ["checkout", "{{shortCode}}", "order"]}}}, {"name": "Verify Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"razorpay_order_id\": \"{{razorpayOrderId}}\",\n  \"razorpay_payment_id\": \"pay_mock123456789\",\n  \"razorpay_signature\": \"mock_signature_hash\",\n  \"order_id\": \"{{orderUuid}}\"\n}"}, "url": {"raw": "{{baseUrl}}/checkout/verify-payment", "host": ["{{baseUrl}}"], "path": ["checkout", "verify-payment"]}}}, {"name": "Get Order Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/checkout/order/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["checkout", "order", "{{orderId}}"]}}}]}, {"name": "Orders", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders?page=1&limit=10&status=pending", "host": ["{{baseUrl}}"], "path": ["api", "orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "pending"}]}}}, {"name": "Get Single Order", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders/{{orderUuid}}", "host": ["{{baseUrl}}"], "path": ["api", "orders", "{{orderUuid}}"]}}}, {"name": "Update Order Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"confirmed\",\n  \"note\": \"Order confirmed and ready for processing\"\n}"}, "url": {"raw": "{{baseUrl}}/api/orders/{{orderUuid}}/status", "host": ["{{baseUrl}}"], "path": ["api", "orders", "{{orderUuid}}", "status"]}}}, {"name": "Add Tracking Information", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"trackingNumber\": \"TRK123456789\",\n  \"carrier\": \"BlueDart Express\"\n}"}, "url": {"raw": "{{baseUrl}}/api/orders/{{orderUuid}}/tracking", "host": ["{{baseUrl}}"], "path": ["api", "orders", "{{orderUuid}}", "tracking"]}}}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Customer requested cancellation\"\n}"}, "url": {"raw": "{{baseUrl}}/api/orders/{{orderUuid}}/cancel", "host": ["{{baseUrl}}"], "path": ["api", "orders", "{{orderUuid}}", "cancel"]}}}, {"name": "<PERSON> as <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Customer not available for delivery\",\n  \"rtoCharges\": 150\n}"}, "url": {"raw": "{{baseUrl}}/api/orders/{{orderUuid}}/rto", "host": ["{{baseUrl}}"], "path": ["api", "orders", "{{orderUuid}}", "rto"]}}}, {"name": "Get Order Analytics Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders/analytics/dashboard?period=30d", "host": ["{{baseUrl}}"], "path": ["api", "orders", "analytics", "dashboard"], "query": [{"key": "period", "value": "30d"}]}}}]}, {"name": "Sellers", "item": [{"name": "Get Seller Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/sellers/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "sellers", "dashboard"]}}}, {"name": "Update Business Details", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"upiId\": \"seller@paytm\",\n  \"gstin\": \"22AAAAA0000A1Z5\",\n  \"bankDetails\": {\n    \"accountNumber\": \"**********\",\n    \"ifscCode\": \"ICIC0000123\",\n    \"accountHolderName\": \"<PERSON>\",\n    \"bankName\": \"ICICI Bank\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/sellers/business", "host": ["{{baseUrl}}"], "path": ["api", "sellers", "business"]}}}, {"name": "<PERSON> Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/sellers/analytics?period=30d", "host": ["{{baseUrl}}"], "path": ["api", "sellers", "analytics"], "query": [{"key": "period", "value": "30d"}]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}