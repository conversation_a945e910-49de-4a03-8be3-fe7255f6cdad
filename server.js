const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Database connection and models
const { connectDB } = require('./config/database');
const { Seller, Product, Order, Request } = require('./models');

const app = express();
const PORT = process.env.PORT || 3000;

// Connect to database
connectDB();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files
app.use(express.static('public'));

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'SwiftCheckout API'
  });
});

// Serve specific request ID
app.get('/request/:requestId', async (req, res) => {
  try {
    const { requestId } = req.params;

    // Check if this is the specific request ID we're looking for
    if (requestId === 'f655713f-55f8-4872-ac90-cc47c5e65c25') {
      const requestData = {
        id: requestId,
        status: 'active',
        type: 'checkout_request',
        timestamp: new Date().toISOString(),
        data: {
          message: 'SwiftCheckout request served successfully',
          service: 'SwiftCheckout - Checkout Infrastructure for Instagram & WhatsApp Sellers',
          features: [
            'One link. One product. One checkout. No website needed.',
            'Chat-first commerce platform',
            'COD and prepaid payment options',
            'Seller dashboard and inventory management'
          ]
        }
      };

      // Store the request in database for tracking
      try {
        await Request.findOrCreate({
          where: { requestId },
          defaults: {
            requestId,
            type: 'checkout_request',
            status: 'active',
            data: requestData.data,
            source: {
              ip: req.ip,
              userAgent: req.get('User-Agent'),
              referer: req.get('Referer')
            }
          }
        });
      } catch (dbError) {
        console.log('Database not available, serving without persistence');
      }

      res.json(requestData);
    } else {
      // Check if request exists in database
      try {
        const storedRequest = await Request.findOne({ where: { requestId } });
        if (storedRequest) {
          await storedRequest.trackView(true);
          res.json(storedRequest.getSummary());
        } else {
          res.status(404).json({
            error: 'Request not found',
            requestId: requestId,
            timestamp: new Date().toISOString()
          });
        }
      } catch (dbError) {
        console.log('Database not available');
        res.status(404).json({
          error: 'Request not found',
          requestId: requestId,
          timestamp: new Date().toISOString()
        });
      }
    }
  } catch (error) {
    console.error('Error serving request:', error);
    res.status(500).json({
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
});

// API Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/sellers', require('./routes/sellers'));
app.use('/api/products', require('./routes/products'));
app.use('/api/orders', require('./routes/orders'));
app.use('/checkout', require('./routes/checkout'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 SwiftCheckout server running on port ${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Request endpoint: http://localhost:${PORT}/request/f655713f-55f8-4872-ac90-cc47c5e65c25`);
});

module.exports = app;
