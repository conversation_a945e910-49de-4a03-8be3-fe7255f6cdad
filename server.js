const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Database connection
const connectDB = require('./config/database');

// Models
const Request = require('./models/Request');
const Seller = require('./models/Seller');
const Product = require('./models/Product');
const Order = require('./models/Order');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// In-memory storage for demo purposes (replace with database later)
const requests = new Map();
const sellers = new Map();
const products = new Map();
const orders = new Map();

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'SwiftCheckout API'
  });
});

// Serve specific request ID
app.get('/request/:requestId', (req, res) => {
  const { requestId } = req.params;
  
  // Check if this is the specific request ID we're looking for
  if (requestId === 'f655713f-55f8-4872-ac90-cc47c5e65c25') {
    const requestData = {
      id: requestId,
      status: 'active',
      type: 'checkout_request',
      timestamp: new Date().toISOString(),
      data: {
        message: 'SwiftCheckout request served successfully',
        service: 'SwiftCheckout - Checkout Infrastructure for Instagram & WhatsApp Sellers',
        features: [
          'One link. One product. One checkout. No website needed.',
          'Chat-first commerce platform',
          'COD and prepaid payment options',
          'Seller dashboard and inventory management'
        ]
      }
    };
    
    // Store the request for tracking
    requests.set(requestId, requestData);
    
    res.json(requestData);
  } else {
    // Check if request exists in storage
    const storedRequest = requests.get(requestId);
    if (storedRequest) {
      res.json(storedRequest);
    } else {
      res.status(404).json({
        error: 'Request not found',
        requestId: requestId,
        timestamp: new Date().toISOString()
      });
    }
  }
});

// API Routes placeholder
app.use('/api/auth', require('./routes/auth'));
app.use('/api/sellers', require('./routes/sellers'));
app.use('/api/products', require('./routes/products'));
app.use('/api/orders', require('./routes/orders'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 SwiftCheckout server running on port ${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Request endpoint: http://localhost:${PORT}/request/f655713f-55f8-4872-ac90-cc47c5e65c25`);
});

module.exports = app;
