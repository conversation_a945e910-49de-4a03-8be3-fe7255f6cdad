{"name": "swiftcheckout", "version": "1.0.0", "description": "Checkout Infrastructure for Instagram & WhatsApp Sellers", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["checkout", "ecommerce", "social-commerce", "instagram", "whatsapp"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "nodemailer": "^7.0.5", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "qrcode": "^1.5.4", "razorpay": "^2.9.6", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "speakeasy": "^2.0.0", "twilio": "^5.7.3", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}