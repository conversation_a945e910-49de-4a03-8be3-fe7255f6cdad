{"name": "swiftcheckout", "version": "1.0.0", "description": "Checkout Infrastructure for Instagram & WhatsApp Sellers", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["checkout", "ecommerce", "social-commerce", "instagram", "whatsapp"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}