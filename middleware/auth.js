const jwt = require('jsonwebtoken');
const { Seller } = require('../models');

// Generate JWT token
const generateToken = (sellerId) => {
  return jwt.sign(
    { sellerId },
    process.env.JWT_SECRET || 'fallback-secret',
    { expiresIn: '7d' }
  );
};

// Verify JWT token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');
  } catch (error) {
    return null;
  }
};

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        error: 'Access denied. No token provided.',
        code: 'NO_TOKEN'
      });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({
        error: 'Invalid token.',
        code: 'INVALID_TOKEN'
      });
    }

    // Check if seller exists and is active
    const seller = await Seller.findByPk(decoded.sellerId);
    if (!seller || !seller.isActive) {
      return res.status(401).json({
        error: 'Seller not found or inactive.',
        code: 'SELLER_NOT_FOUND'
      });
    }

    // Update last login
    seller.lastLogin = new Date();
    await seller.save();

    req.seller = seller;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      error: 'Authentication failed.',
      code: 'AUTH_ERROR'
    });
  }
};

// Optional authentication middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (token) {
      const decoded = verifyToken(token);
      if (decoded) {
        const seller = await Seller.findByPk(decoded.sellerId);
        if (seller && seller.isActive) {
          req.seller = seller;
        }
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

// Check if seller is verified
const requireVerified = (req, res, next) => {
  if (!req.seller?.isVerified) {
    return res.status(403).json({
      error: 'Seller verification required.',
      code: 'VERIFICATION_REQUIRED'
    });
  }
  next();
};

// Check seller plan permissions
const requirePlan = (requiredPlan) => {
  const planHierarchy = ['free', 'basic', 'premium', 'enterprise'];
  
  return (req, res, next) => {
    const sellerPlanIndex = planHierarchy.indexOf(req.seller?.plan || 'free');
    const requiredPlanIndex = planHierarchy.indexOf(requiredPlan);
    
    if (sellerPlanIndex < requiredPlanIndex) {
      return res.status(403).json({
        error: `${requiredPlan} plan required.`,
        code: 'PLAN_UPGRADE_REQUIRED',
        currentPlan: req.seller?.plan || 'free',
        requiredPlan
      });
    }
    
    next();
  };
};

module.exports = {
  generateToken,
  verifyToken,
  authenticate,
  optionalAuth,
  requireVerified,
  requirePlan
};
