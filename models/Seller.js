const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const sellerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  email: {
    type: String,
    required: false,
    unique: true,
    sparse: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: false // For OTP-based auth, password might not be needed
  },
  upiId: {
    type: String,
    required: false,
    trim: true
  },
  bankDetails: {
    accountNumber: String,
    ifscCode: String,
    accountHolderName: String,
    bankName: String
  },
  gstin: {
    type: String,
    required: false,
    trim: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationBadge: {
    type: Boolean,
    default: false
  },
  rtoRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  totalOrders: {
    type: Number,
    default: 0
  },
  totalRevenue: {
    type: Number,
    default: 0
  },
  plan: {
    type: String,
    enum: ['free', 'basic', 'premium', 'enterprise'],
    default: 'free'
  },
  planExpiry: {
    type: Date,
    required: false
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for faster queries
sellerSchema.index({ phone: 1 });
sellerSchema.index({ email: 1 });
sellerSchema.index({ isActive: 1 });

// Hash password before saving
sellerSchema.pre('save', async function(next) {
  if (!this.isModified('password') || !this.password) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
sellerSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

// Generate seller profile summary
sellerSchema.methods.getProfileSummary = function() {
  return {
    id: this._id,
    name: this.name,
    phone: this.phone,
    email: this.email,
    isVerified: this.isVerified,
    verificationBadge: this.verificationBadge,
    plan: this.plan,
    totalOrders: this.totalOrders,
    totalRevenue: this.totalRevenue,
    rtoRate: this.rtoRate
  };
};

module.exports = mongoose.model('Seller', sellerSchema);
