const { sequelize } = require('../config/database');

// Import models
const Seller = require('./Seller');
const Product = require('./Product');
const Order = require('./Order');
const Request = require('./Request');
const Buyer = require('./Buyer');

// Define associations
Seller.hasMany(Product, {
  foreignKey: 'sellerId',
  as: 'products'
});

Product.belongsTo(Seller, {
  foreignKey: 'sellerId',
  as: 'seller'
});

Seller.hasMany(Order, {
  foreignKey: 'sellerId',
  as: 'orders'
});

Order.belongsTo(Seller, {
  foreignKey: 'sellerId',
  as: 'seller'
});

Product.hasMany(Order, {
  foreignKey: 'productId',
  as: 'orders'
});

Order.belongsTo(Product, {
  foreignKey: 'productId',
  as: 'productDetails'
});

// Buyer associations
Buyer.hasMany(Order, {
  foreignKey: 'buyerId',
  as: 'orders'
});

Order.belongsTo(Buyer, {
  foreignKey: 'buyerId',
  as: 'buyerProfile'
});

// Export models and sequelize instance
module.exports = {
  sequelize,
  Seller,
  Product,
  Order,
  Request,
  Buyer
};
