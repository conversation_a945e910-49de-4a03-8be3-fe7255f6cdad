const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderId: {
    type: String,
    unique: true,
    required: true
  },
  sellerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Seller',
    required: true
  },
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  buyer: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    address: {
      line1: {
        type: String,
        required: true
      },
      line2: String,
      city: {
        type: String,
        required: true
      },
      state: {
        type: String,
        required: true
      },
      pincode: {
        type: String,
        required: true
      },
      country: {
        type: String,
        default: 'India'
      }
    }
  },
  product: {
    name: String,
    price: Number,
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    image: String
  },
  pricing: {
    productPrice: {
      type: Number,
      required: true
    },
    quantity: {
      type: Number,
      required: true
    },
    subtotal: {
      type: Number,
      required: true
    },
    shippingCharges: {
      type: Number,
      default: 0
    },
    codCharges: {
      type: Number,
      default: 0
    },
    platformFee: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      required: true
    }
  },
  payment: {
    method: {
      type: String,
      enum: ['cod', 'prepaid'],
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded'],
      default: 'pending'
    },
    gateway: {
      type: String,
      enum: ['razorpay', 'cashfree', 'stripe'],
      required: function() {
        return this.payment.method === 'prepaid';
      }
    },
    transactionId: String,
    gatewayOrderId: String,
    gatewayPaymentId: String,
    paidAt: Date,
    failureReason: String
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'],
    default: 'pending'
  },
  fulfillment: {
    isShipped: {
      type: Boolean,
      default: false
    },
    shippedAt: Date,
    trackingNumber: String,
    carrier: String,
    isDelivered: {
      type: Boolean,
      default: false
    },
    deliveredAt: Date,
    deliveryConfirmation: {
      method: String, // 'otp', 'signature', 'photo'
      value: String
    }
  },
  rto: {
    isRto: {
      type: Boolean,
      default: false
    },
    reason: String,
    rtoDate: Date,
    rtoCharges: {
      type: Number,
      default: 0
    }
  },
  buyerCredibility: {
    sibylScore: {
      type: Number,
      min: 0,
      max: 100,
      default: 50
    },
    previousOrders: {
      type: Number,
      default: 0
    },
    codAbuseFlags: {
      type: Number,
      default: 0
    }
  },
  notes: {
    sellerNotes: String,
    buyerNotes: String,
    internalNotes: String
  },
  timeline: [{
    status: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    updatedBy: String
  }]
}, {
  timestamps: true
});

// Indexes
orderSchema.index({ orderId: 1 });
orderSchema.index({ sellerId: 1 });
orderSchema.index({ productId: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ 'buyer.phone': 1 });
orderSchema.index({ createdAt: -1 });

// Generate order ID before saving
orderSchema.pre('save', function(next) {
  if (this.isNew && !this.orderId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.orderId = `SC${timestamp}${random}`.toUpperCase();
  }
  next();
});

// Add status update to timeline
orderSchema.methods.updateStatus = function(newStatus, note = '', updatedBy = 'system') {
  this.status = newStatus;
  this.timeline.push({
    status: newStatus,
    note,
    updatedBy,
    timestamp: new Date()
  });
  return this.save();
};

// Calculate total amount
orderSchema.methods.calculateTotal = function() {
  const subtotal = this.pricing.productPrice * this.pricing.quantity;
  const total = subtotal + this.pricing.shippingCharges + this.pricing.codCharges + this.pricing.platformFee;
  
  this.pricing.subtotal = subtotal;
  this.pricing.total = total;
  
  return total;
};

// Check if order can be cancelled
orderSchema.methods.canBeCancelled = function() {
  return ['pending', 'confirmed'].includes(this.status);
};

// Get order summary
orderSchema.methods.getSummary = function() {
  return {
    orderId: this.orderId,
    status: this.status,
    paymentStatus: this.payment.status,
    paymentMethod: this.payment.method,
    total: this.pricing.total,
    buyer: {
      name: this.buyer.name,
      phone: this.buyer.phone
    },
    product: this.product,
    createdAt: this.createdAt,
    canCancel: this.canBeCancelled()
  };
};

module.exports = mongoose.model('Order', orderSchema);
