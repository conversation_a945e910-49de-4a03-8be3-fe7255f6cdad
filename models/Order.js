const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  orderId: {
    type: DataTypes.STRING,
    unique: true,
    allowNull: false
  },
  sellerId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'sellers',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  buyerId: {
    type: DataTypes.UUID,
    allowNull: true, // Optional for backward compatibility
    references: {
      model: 'buyers',
      key: 'id'
    }
  },
  buyer: {
    type: DataTypes.JSONB,
    allowNull: false,
    validate: {
      hasRequiredFields(value) {
        if (!value.name || !value.phone || !value.address) {
          throw new Error('Buyer must have name, phone, and address');
        }
      }
    }
  },
  product: {
    type: DataTypes.JSONB,
    allowNull: false
  },
  pricing: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {
      productPrice: 0,
      quantity: 1,
      subtotal: 0,
      shippingCharges: 0,
      codCharges: 0,
      platformFee: 10,
      total: 0
    }
  },
  payment: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {
      method: 'cod',
      status: 'pending'
    }
  },
  status: {
    type: DataTypes.ENUM(
      'pending', 'confirmed', 'processing', 'shipped', 
      'delivered', 'cancelled', 'returned'
    ),
    defaultValue: 'pending'
  },
  fulfillment: {
    type: DataTypes.JSONB,
    defaultValue: {
      isShipped: false,
      isDelivered: false
    }
  },
  rto: {
    type: DataTypes.JSONB,
    defaultValue: {
      isRto: false,
      rtoCharges: 0
    }
  },
  buyerCredibility: {
    type: DataTypes.JSONB,
    defaultValue: {
      sibylScore: 50,
      previousOrders: 0,
      codAbuseFlags: 0
    }
  },
  notes: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  timeline: {
    type: DataTypes.JSONB,
    defaultValue: []
  }
}, {
  tableName: 'orders',
  timestamps: true,
  indexes: [
    { fields: ['orderId'] },
    { fields: ['sellerId'] },
    { fields: ['productId'] },
    { fields: ['status'] },
    { fields: ['createdAt'] }
  ]
});

// Generate order ID before creating
Order.beforeCreate((order) => {
  if (!order.orderId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    order.orderId = `SC${timestamp}${random}`.toUpperCase();
  }
});

// Instance methods
Order.prototype.updateStatus = async function(newStatus, note = '', updatedBy = 'system') {
  this.status = newStatus;
  const timeline = [...this.timeline];
  timeline.push({
    status: newStatus,
    note,
    updatedBy,
    timestamp: new Date()
  });
  this.timeline = timeline;
  await this.save();
  return this;
};

Order.prototype.calculateTotal = function() {
  const subtotal = this.pricing.productPrice * this.pricing.quantity;
  const total = subtotal + this.pricing.shippingCharges + this.pricing.codCharges + this.pricing.platformFee;
  
  const newPricing = { ...this.pricing };
  newPricing.subtotal = subtotal;
  newPricing.total = total;
  this.pricing = newPricing;
  
  return total;
};

Order.prototype.canBeCancelled = function() {
  return ['pending', 'confirmed'].includes(this.status);
};

Order.prototype.getSummary = function() {
  return {
    orderId: this.orderId,
    status: this.status,
    paymentStatus: this.payment.status,
    paymentMethod: this.payment.method,
    total: this.pricing.total,
    buyer: {
      name: this.buyer.name,
      phone: this.buyer.phone
    },
    product: this.product,
    createdAt: this.createdAt,
    canCancel: this.canBeCancelled()
  };
};

module.exports = Order;
