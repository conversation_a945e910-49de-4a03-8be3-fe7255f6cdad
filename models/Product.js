const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const productSchema = new mongoose.Schema({
  sellerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Seller',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  originalPrice: {
    type: Number,
    min: 0
  },
  currency: {
    type: String,
    default: 'INR'
  },
  images: [{
    url: String,
    alt: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  category: {
    type: String,
    trim: true
  },
  tags: [String],
  inventory: {
    quantity: {
      type: Number,
      required: true,
      min: 0,
      default: 0
    },
    trackInventory: {
      type: Boolean,
      default: true
    },
    lowStockThreshold: {
      type: Number,
      default: 5
    }
  },
  checkoutLink: {
    type: String,
    unique: true,
    required: true
  },
  shortCode: {
    type: String,
    unique: true,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isVisible: {
    type: Boolean,
    default: true
  },
  paymentOptions: {
    cod: {
      type: Boolean,
      default: true
    },
    prepaid: {
      type: Boolean,
      default: true
    },
    codCharges: {
      type: Number,
      default: 0
    }
  },
  shipping: {
    weight: Number,
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    },
    freeShipping: {
      type: Boolean,
      default: false
    },
    shippingCharges: {
      type: Number,
      default: 0
    }
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    slug: String
  },
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    clicks: {
      type: Number,
      default: 0
    },
    orders: {
      type: Number,
      default: 0
    },
    revenue: {
      type: Number,
      default: 0
    },
    conversionRate: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// Indexes for better performance
productSchema.index({ sellerId: 1 });
productSchema.index({ checkoutLink: 1 });
productSchema.index({ shortCode: 1 });
productSchema.index({ isActive: 1, isVisible: 1 });
productSchema.index({ 'inventory.quantity': 1 });

// Generate unique checkout link and short code before saving
productSchema.pre('save', function(next) {
  if (this.isNew) {
    this.shortCode = uuidv4().split('-')[0].toUpperCase();
    this.checkoutLink = `${process.env.BASE_URL || 'http://localhost:3000'}/checkout/${this.shortCode}`;
  }
  next();
});

// Check if product is in stock
productSchema.methods.isInStock = function(requestedQuantity = 1) {
  if (!this.inventory.trackInventory) return true;
  return this.inventory.quantity >= requestedQuantity;
};

// Update inventory after order
productSchema.methods.updateInventory = function(quantity) {
  if (this.inventory.trackInventory) {
    this.inventory.quantity = Math.max(0, this.inventory.quantity - quantity);
  }
  return this.save();
};

// Get product summary for listing
productSchema.methods.getSummary = function() {
  return {
    id: this._id,
    name: this.name,
    price: this.price,
    originalPrice: this.originalPrice,
    currency: this.currency,
    primaryImage: this.images.find(img => img.isPrimary)?.url || this.images[0]?.url,
    checkoutLink: this.checkoutLink,
    shortCode: this.shortCode,
    isInStock: this.isInStock(),
    inventory: this.inventory.quantity
  };
};

module.exports = mongoose.model('Product', productSchema);
