const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Buyer = sequelize.define('Buyer', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        is: /^[+]?[0-9]{10,15}$/
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true
      }
    },
    addresses: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of saved addresses'
    },
    preferences: {
      type: DataTypes.JSONB,
      defaultValue: {
        defaultAddressIndex: 0,
        communicationPreferences: {
          sms: true,
          email: true,
          whatsapp: false
        },
        paymentPreferences: {
          preferredMethod: 'cod',
          savedCards: []
        }
      }
    },
    profile: {
      type: DataTypes.JSONB,
      defaultValue: {
        totalOrders: 0,
        totalSpent: 0,
        averageOrderValue: 0,
        favoriteCategories: [],
        lastOrderDate: null,
        loyaltyPoints: 0,
        riskScore: 50, // 0-100, 50 is neutral
        tags: [] // VIP, frequent_buyer, high_value, etc.
      }
    },
    verification: {
      type: DataTypes.JSONB,
      defaultValue: {
        phoneVerified: false,
        emailVerified: false,
        lastOtpSentAt: null,
        otpAttempts: 0
      }
    },
    analytics: {
      type: DataTypes.JSONB,
      defaultValue: {
        firstOrderDate: null,
        lastActiveDate: null,
        sessionCount: 0,
        deviceInfo: {},
        locationHistory: []
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    isBlocked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    blockedReason: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    tableName: 'buyers',
    timestamps: true,
    indexes: [
      {
        fields: ['phone']
      },
      {
        fields: ['email']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['createdAt']
      }
    ]
  });

  // Instance methods
  Buyer.prototype.addAddress = function(address) {
    const addresses = [...this.addresses];
    addresses.push({
      id: Date.now().toString(),
      ...address,
      isDefault: addresses.length === 0,
      createdAt: new Date()
    });
    return this.update({ addresses });
  };

  Buyer.prototype.updateAddress = function(addressId, updates) {
    const addresses = this.addresses.map(addr => 
      addr.id === addressId ? { ...addr, ...updates } : addr
    );
    return this.update({ addresses });
  };

  Buyer.prototype.setDefaultAddress = function(addressId) {
    const addresses = this.addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === addressId
    }));
    const defaultIndex = addresses.findIndex(addr => addr.id === addressId);
    return this.update({ 
      addresses,
      preferences: {
        ...this.preferences,
        defaultAddressIndex: defaultIndex
      }
    });
  };

  Buyer.prototype.updateProfile = function(orderData) {
    const profile = { ...this.profile };
    profile.totalOrders += 1;
    profile.totalSpent += parseFloat(orderData.total || 0);
    profile.averageOrderValue = profile.totalSpent / profile.totalOrders;
    profile.lastOrderDate = new Date();
    
    // Update favorite categories
    if (orderData.category) {
      const categories = profile.favoriteCategories || [];
      const existingCategory = categories.find(cat => cat.name === orderData.category);
      if (existingCategory) {
        existingCategory.count += 1;
      } else {
        categories.push({ name: orderData.category, count: 1 });
      }
      profile.favoriteCategories = categories.sort((a, b) => b.count - a.count);
    }

    return this.update({ profile });
  };

module.exports = Buyer;
