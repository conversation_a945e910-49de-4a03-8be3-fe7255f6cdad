const mongoose = require('mongoose');

const requestSchema = new mongoose.Schema({
  requestId: {
    type: String,
    unique: true,
    required: true
  },
  type: {
    type: String,
    enum: ['checkout_request', 'api_request', 'webhook', 'notification'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'active', 'completed', 'failed', 'expired'],
    default: 'pending'
  },
  source: {
    ip: String,
    userAgent: String,
    referer: String,
    platform: String
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  metadata: {
    sellerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Seller'
    },
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    },
    orderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Order'
    },
    sessionId: String,
    userId: String
  },
  response: {
    statusCode: Number,
    message: String,
    data: mongoose.Schema.Types.Mixed,
    processingTime: Number // in milliseconds
  },
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    uniqueViews: {
      type: Number,
      default: 0
    },
    conversions: {
      type: Number,
      default: 0
    },
    bounceRate: {
      type: Number,
      default: 0
    }
  },
  expiresAt: {
    type: Date,
    default: function() {
      // Default expiry: 30 days from creation
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes
requestSchema.index({ requestId: 1 });
requestSchema.index({ type: 1 });
requestSchema.index({ status: 1 });
requestSchema.index({ 'metadata.sellerId': 1 });
requestSchema.index({ 'metadata.productId': 1 });
requestSchema.index({ expiresAt: 1 });
requestSchema.index({ createdAt: -1 });

// TTL index for automatic cleanup of expired requests
requestSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Update status method
requestSchema.methods.updateStatus = function(newStatus, responseData = {}) {
  this.status = newStatus;
  if (responseData) {
    this.response = {
      ...this.response,
      ...responseData,
      statusCode: responseData.statusCode || 200
    };
  }
  return this.save();
};

// Track analytics
requestSchema.methods.trackView = function(isUnique = false) {
  this.analytics.views += 1;
  if (isUnique) {
    this.analytics.uniqueViews += 1;
  }
  return this.save();
};

requestSchema.methods.trackConversion = function() {
  this.analytics.conversions += 1;
  return this.save();
};

// Check if request is expired
requestSchema.methods.isExpired = function() {
  return this.expiresAt < new Date();
};

// Get request summary
requestSchema.methods.getSummary = function() {
  return {
    requestId: this.requestId,
    type: this.type,
    status: this.status,
    analytics: this.analytics,
    createdAt: this.createdAt,
    expiresAt: this.expiresAt,
    isExpired: this.isExpired()
  };
};

module.exports = mongoose.model('Request', requestSchema);
