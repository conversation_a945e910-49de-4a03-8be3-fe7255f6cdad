{"id": "swiftcheckout-environment", "name": "SwiftCheckout Environment", "values": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "authToken", "value": "", "type": "secret", "enabled": true}, {"key": "sellerId", "value": "", "type": "default", "enabled": true}, {"key": "productId", "value": "", "type": "default", "enabled": true}, {"key": "shortCode", "value": "", "type": "default", "enabled": true}, {"key": "orderId", "value": "", "type": "default", "enabled": true}, {"key": "orderUuid", "value": "", "type": "default", "enabled": true}, {"key": "razorpayOrderId", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-07-15T16:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}