/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
import { XMLElement } from "xmlbuilder";
import TwiML from "./TwiML";
declare class VoiceResponse extends TwiML {
    /**
     * <Response> TwiML for Voice
     */
    constructor();
    /**
     * Comments in <Response>
     *
     * @param comment - XML Comment
     */
    comment(comment: string): XMLElement;
    /**
     * Comments after <Response>
     *
     * @param comment - XML Comment
     */
    commentAfter(comment: string): XMLElement;
    /**
     * Comments before <Response>
     *
     * @param comment - XML Comment
     */
    commentBefore(comment: string): XMLElement;
    /**
     * <Connect> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    connect(attributes?: VoiceResponse.ConnectAttributes): VoiceResponse.Connect;
    /**
     * <Dial> TwiML Verb
     *
     * @param attributes - TwiML attributes
     * @param number - Phone number to dial
     */
    dial(number?: string): VoiceResponse.Dial;
    dial(attributes?: VoiceResponse.DialAttributes, number?: string): VoiceResponse.Dial;
    /**
     * <Echo> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    echo(attributes?: object): VoiceResponse.Echo;
    /**
     * <Enqueue> TwiML Noun
     *
     * @param attributes - TwiML attributes
     * @param name - Friendly name
     */
    enqueue(name?: string): VoiceResponse.Enqueue;
    enqueue(attributes?: VoiceResponse.EnqueueAttributes, name?: string): VoiceResponse.Enqueue;
    /**
     * <Gather> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    gather(attributes?: VoiceResponse.GatherAttributes): VoiceResponse.Gather;
    /**
     * <Hangup> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    hangup(attributes?: object): VoiceResponse.Hangup;
    /**
     * <Leave> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    leave(attributes?: object): VoiceResponse.Leave;
    /**
     * <Pause> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    pause(attributes?: VoiceResponse.PauseAttributes): VoiceResponse.Pause;
    /**
     * <Pay> Twiml Verb
     *
     * @param attributes - TwiML attributes
     */
    pay(attributes?: VoiceResponse.PayAttributes): VoiceResponse.Pay;
    /**
     * <Play> TwiML Verb
     *
     * @param attributes - TwiML attributes
     * @param url - Media URL
     */
    play(url?: string): VoiceResponse.Play;
    play(attributes?: VoiceResponse.PlayAttributes, url?: string): VoiceResponse.Play;
    /**
     * <Prompt> Twiml Verb
     *
     * @param attributes - TwiML attributes
     */
    prompt(attributes?: VoiceResponse.PromptAttributes): VoiceResponse.Prompt;
    /**
     * <Queue> TwiML Noun
     *
     * @param attributes - TwiML attributes
     * @param name - Queue name
     */
    queue(name: string): VoiceResponse.Queue;
    queue(attributes: VoiceResponse.QueueAttributes, name: string): VoiceResponse.Queue;
    /**
     * <Record> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    record(attributes?: VoiceResponse.RecordAttributes): VoiceResponse.Record;
    /**
     * <Redirect> TwiML Verb
     *
     * @param attributes - TwiML attributes
     * @param url - Redirect URL
     */
    redirect(url: string): VoiceResponse.Redirect;
    redirect(attributes: VoiceResponse.RedirectAttributes, url: string): VoiceResponse.Redirect;
    /**
     * <Refer> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    refer(attributes?: VoiceResponse.ReferAttributes): VoiceResponse.Refer;
    /**
     * <Reject> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    reject(attributes?: VoiceResponse.RejectAttributes): VoiceResponse.Reject;
    /**
     * <Say> TwiML Verb
     *
     * @param attributes - TwiML attributes
     * @param message - Message to say
     */
    say(message: string): VoiceResponse.Say;
    say(attributes: VoiceResponse.SayAttributes, message: string): VoiceResponse.Say;
    /**
     * <Sms> TwiML Noun
     *
     * @param attributes - TwiML attributes
     * @param message - Message body
     */
    sms(message: string): VoiceResponse.Sms;
    sms(attributes: VoiceResponse.SmsAttributes, message: string): VoiceResponse.Sms;
    /**
     * <Start> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    start(attributes?: VoiceResponse.StartAttributes): VoiceResponse.Start;
    /**
     * <Stop> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    stop(attributes?: object): VoiceResponse.Stop;
}
declare namespace VoiceResponse {
    type ApplicationEvent = "initiated" | "ringing" | "answered" | "completed";
    type ClientEvent = "initiated" | "ringing" | "answered" | "completed";
    type ConferenceBeep = "true" | "false" | "onEnter" | "onExit";
    type ConferenceEvent = "start" | "end" | "join" | "leave" | "mute" | "hold" | "modify" | "speaker" | "announcement";
    type ConferenceJitterBufferSize = "large" | "medium" | "small" | "off";
    type ConferenceRecord = "do-not-record" | "record-from-start";
    type ConferenceRecordingEvent = "in-progress" | "completed" | "absent";
    type ConferenceRegion = "us1" | "us2" | "ie1" | "sg1" | "br1" | "au1" | "jp1" | "de1";
    type ConferenceTrim = "trim-silence" | "do-not-trim";
    type ConversationEvent = "call-initiated" | "call-ringing" | "call-answered" | "call-completed";
    type ConversationRecord = "do-not-record" | "record-from-answer" | "record-from-ringing" | "record-from-answer-dual" | "record-from-ringing-dual" | "true" | "false";
    type ConversationRecordingEvent = "in-progress" | "completed" | "absent";
    type ConversationTrim = "trim-silence" | "do-not-trim";
    type DialEvents = "call-progress-event";
    type DialRecord = "do-not-record" | "record-from-answer" | "record-from-ringing" | "record-from-answer-dual" | "record-from-ringing-dual";
    type DialRecordingEvent = "in-progress" | "completed" | "absent";
    type DialRecordingTrack = "both" | "inbound" | "outbound";
    type DialRingTone = "at" | "au" | "bg" | "br" | "be" | "ch" | "cl" | "cn" | "cz" | "de" | "dk" | "ee" | "es" | "fi" | "fr" | "gr" | "hu" | "il" | "in" | "it" | "lt" | "jp" | "mx" | "my" | "nl" | "no" | "nz" | "ph" | "pl" | "pt" | "ru" | "se" | "sg" | "th" | "uk" | "us" | "us-old" | "tw" | "ve" | "za";
    type DialTrim = "trim-silence" | "do-not-trim";
    type GatherInput = "dtmf" | "speech";
    type GatherLanguage = "af-ZA" | "am-ET" | "ar-AE" | "ar-BH" | "ar-DZ" | "ar-EG" | "ar-IL" | "ar-IQ" | "ar-JO" | "ar-KW" | "ar-LB" | "ar-MA" | "ar-OM" | "ar-PS" | "ar-QA" | "ar-SA" | "ar-TN" | "az-AZ" | "bg-BG" | "bn-BD" | "bn-IN" | "ca-ES" | "cs-CZ" | "da-DK" | "de-DE" | "el-GR" | "en-AU" | "en-CA" | "en-GB" | "en-GH" | "en-IE" | "en-IN" | "en-KE" | "en-NG" | "en-NZ" | "en-PH" | "en-SG" | "en-TZ" | "en-US" | "en-ZA" | "es-AR" | "es-BO" | "es-CL" | "es-CO" | "es-CR" | "es-DO" | "es-EC" | "es-ES" | "es-GT" | "es-HN" | "es-MX" | "es-NI" | "es-PA" | "es-PE" | "es-PR" | "es-PY" | "es-SV" | "es-US" | "es-UY" | "es-VE" | "et-EE" | "eu-ES" | "fa-IR" | "fi-FI" | "fil-PH" | "fr-CA" | "fr-FR" | "gl-ES" | "gu-IN" | "he-IL" | "hi-IN" | "hr-HR" | "hu-HU" | "hy-AM" | "id-ID" | "is-IS" | "it-IT" | "ja-JP" | "jv-ID" | "ka-GE" | "km-KH" | "kn-IN" | "ko-KR" | "lo-LA" | "lt-LT" | "lv-LV" | "mk-MK" | "ml-IN" | "mn-MN" | "mr-IN" | "ms-MY" | "my-MM" | "nar-IQ" | "nb-NO" | "ne-NP" | "nl-BE" | "nl-NL" | "pa-guru-IN" | "pl-PL" | "pt-BR" | "pt-PT" | "ro-RO" | "ru-RU" | "si-LK" | "sk-SK" | "sl-SI" | "sq-AL" | "sr-RS" | "su-ID" | "sv-SE" | "sw-KE" | "sw-TZ" | "ta-IN" | "ta-LK" | "ta-MY" | "ta-SG" | "te-IN" | "th-TH" | "tr-TR" | "uk-UA" | "ur-IN" | "ur-PK" | "uz-UZ" | "vi-VN" | "yue-Hant-HK" | "zh" | "cmn-Hans-CN" | "zh-TW" | "cmn-Hant-TW" | "zu-ZA";
    type NumberEvent = "initiated" | "ringing" | "answered" | "completed";
    type PayBankAccountType = "consumer-checking" | "consumer-savings" | "commercial-checking" | "commercial-savings";
    type PayInput = "dtmf";
    type PayLanguage = "de-DE" | "en-AU" | "en-CA" | "en-GB" | "en-IN" | "en-IE" | "en-NZ" | "en-PH" | "en-ZA" | "en-US" | "es-ES" | "es-US" | "fr-CA" | "fr-FR" | "it-IT";
    type PayPaymentMethod = "ach-debit" | "credit-card";
    type PayStatusCallbackMethod = "GET" | "POST";
    type PayTokenType = "one-time" | "reusable" | "payment-method";
    type PayValidCardTypes = "visa" | "mastercard" | "amex" | "maestro" | "discover" | "optima" | "jcb" | "diners-club" | "enroute";
    type PromptCardType = "visa" | "mastercard" | "amex" | "maestro" | "discover" | "optima" | "jcb" | "diners-club" | "enroute";
    type PromptErrorType = "timeout" | "invalid-card-number" | "invalid-card-type" | "invalid-date" | "invalid-security-code" | "internal-error" | "input-matching-failed";
    type PromptFor = "payment-card-number" | "expiration-date" | "security-code" | "postal-code" | "payment-processing" | "bank-account-number" | "bank-routing-number";
    type RecordRecordingEvent = "in-progress" | "completed" | "absent";
    type RecordTrim = "trim-silence" | "do-not-trim";
    type RejectReason = "rejected" | "busy";
    type SayLanguage = "af-ZA" | "am-ET" | "ar-AE" | "ar-XA" | "arb" | "bg-BG" | "bn-IN" | "ca-ES" | "cmn-CN" | "cmn-TW" | "cs-CZ" | "cy-GB" | "da-DK" | "de-AT" | "de-DE" | "el-GR" | "en-AU" | "en-CA" | "en-GB" | "en-GB-WLS" | "en-IE" | "en-IN" | "en-NZ" | "en-US" | "en-ZA" | "es-ES" | "es-MX" | "es-US" | "fi-FI" | "fil-PH" | "fr-CA" | "fr-FR" | "fr-BE" | "gu-IN" | "he-IL" | "hi-IN" | "hu-HU" | "id-ID" | "is-IS" | "it-IT" | "ja-JP" | "kn-IN" | "ko-KR" | "lv-LV" | "ml-IN" | "mr-IN" | "ms-MY" | "nb-NO" | "nl-BE" | "nl-NL" | "pa-IN" | "pl-PL" | "pt-BR" | "pt-PT" | "ro-RO" | "ru-RU" | "sk-SK" | "sr-RS" | "sv-SE" | "ta-IN" | "te-IN" | "th-TH" | "tr-TR" | "uk-UA" | "vi-VN" | "yue-HK" | "zh-CN" | "zh-HK" | "zh-TW";
    type SayVoice = "man" | "woman" | "alice" | "Google.af-ZA-Standard-A" | "Google.am-ET-Standard-A" | "Google.am-ET-Standard-B" | "Google.am-ET-Wavenet-A" | "Google.am-ET-Wavenet-B" | "Google.ar-XA-Standard-A" | "Google.ar-XA-Standard-B" | "Google.ar-XA-Standard-C" | "Google.ar-XA-Standard-D" | "Google.ar-XA-Wavenet-A" | "Google.ar-XA-Wavenet-B" | "Google.ar-XA-Wavenet-C" | "Google.ar-XA-Wavenet-D" | "Google.bg-BG-Standard-A" | "Google.bn-IN-Standard-C" | "Google.bn-IN-Standard-D" | "Google.bn-IN-Wavenet-C" | "Google.bn-IN-Wavenet-D" | "Google.ca-ES-Standard-A" | "Google.cmn-CN-Standard-A" | "Google.cmn-CN-Standard-B" | "Google.cmn-CN-Standard-C" | "Google.cmn-CN-Standard-D" | "Google.cmn-CN-Wavenet-A" | "Google.cmn-CN-Wavenet-B" | "Google.cmn-CN-Wavenet-C" | "Google.cmn-CN-Wavenet-D" | "Google.cmn-TW-Standard-A" | "Google.cmn-TW-Standard-B" | "Google.cmn-TW-Standard-C" | "Google.cmn-TW-Wavenet-A" | "Google.cmn-TW-Wavenet-B" | "Google.cmn-TW-Wavenet-C" | "Google.cs-CZ-Standard-A" | "Google.cs-CZ-Wavenet-A" | "Google.da-DK-Neural2-D" | "Google.da-DK-Neural2-F" | "Google.da-DK-Standard-A" | "Google.da-DK-Standard-C" | "Google.da-DK-Standard-D" | "Google.da-DK-Standard-E" | "Google.da-DK-Wavenet-A" | "Google.da-DK-Wavenet-C" | "Google.da-DK-Wavenet-D" | "Google.da-DK-Wavenet-E" | "Google.de-DE-Neural2-A" | "Google.de-DE-Neural2-B" | "Google.de-DE-Neural2-C" | "Google.de-DE-Neural2-D" | "Google.de-DE-Neural2-F" | "Google.de-DE-Standard-A" | "Google.de-DE-Standard-B" | "Google.de-DE-Standard-C" | "Google.de-DE-Standard-D" | "Google.de-DE-Standard-E" | "Google.de-DE-Standard-F" | "Google.de-DE-Wavenet-A" | "Google.de-DE-Wavenet-B" | "Google.de-DE-Wavenet-C" | "Google.de-DE-Wavenet-D" | "Google.de-DE-Wavenet-E" | "Google.de-DE-Wavenet-F" | "Google.el-GR-Standard-A" | "Google.el-GR-Wavenet-A" | "Google.en-AU-Neural2-A" | "Google.en-AU-Neural2-B" | "Google.en-AU-Neural2-C" | "Google.en-AU-Neural2-D" | "Google.en-AU-Standard-A" | "Google.en-AU-Standard-B" | "Google.en-AU-Standard-C" | "Google.en-AU-Standard-D" | "Google.en-AU-Wavenet-A" | "Google.en-AU-Wavenet-B" | "Google.en-AU-Wavenet-C" | "Google.en-AU-Wavenet-D" | "Google.en-GB-Neural2-A" | "Google.en-GB-Neural2-B" | "Google.en-GB-Neural2-C" | "Google.en-GB-Neural2-D" | "Google.en-GB-Neural2-F" | "Google.en-GB-Standard-A" | "Google.en-GB-Standard-B" | "Google.en-GB-Standard-C" | "Google.en-GB-Standard-D" | "Google.en-GB-Standard-F" | "Google.en-GB-Wavenet-A" | "Google.en-GB-Wavenet-B" | "Google.en-GB-Wavenet-C" | "Google.en-GB-Wavenet-D" | "Google.en-GB-Wavenet-F" | "Google.en-IN-Neural2-A" | "Google.en-IN-Neural2-B" | "Google.en-IN-Neural2-C" | "Google.en-IN-Neural2-D" | "Google.en-IN-Standard-A" | "Google.en-IN-Standard-B" | "Google.en-IN-Standard-C" | "Google.en-IN-Standard-D" | "Google.en-IN-Wavenet-A" | "Google.en-IN-Wavenet-B" | "Google.en-IN-Wavenet-C" | "Google.en-IN-Wavenet-D" | "Google.en-US-Neural2-A" | "Google.en-US-Neural2-C" | "Google.en-US-Neural2-D" | "Google.en-US-Neural2-E" | "Google.en-US-Neural2-F" | "Google.en-US-Neural2-G" | "Google.en-US-Neural2-H" | "Google.en-US-Neural2-I" | "Google.en-US-Neural2-J" | "Google.en-US-Standard-A" | "Google.en-US-Standard-B" | "Google.en-US-Standard-C" | "Google.en-US-Standard-D" | "Google.en-US-Standard-E" | "Google.en-US-Standard-F" | "Google.en-US-Standard-G" | "Google.en-US-Standard-H" | "Google.en-US-Standard-I" | "Google.en-US-Standard-J" | "Google.en-US-Wavenet-A" | "Google.en-US-Wavenet-B" | "Google.en-US-Wavenet-C" | "Google.en-US-Wavenet-D" | "Google.en-US-Wavenet-E" | "Google.en-US-Wavenet-F" | "Google.en-US-Wavenet-G" | "Google.en-US-Wavenet-H" | "Google.en-US-Wavenet-I" | "Google.en-US-Wavenet-J" | "Google.es-ES-Neural2-A" | "Google.es-ES-Neural2-B" | "Google.es-ES-Neural2-C" | "Google.es-ES-Neural2-D" | "Google.es-ES-Neural2-E" | "Google.es-ES-Neural2-F" | "Google.es-ES-Standard-B" | "Google.es-ES-Standard-C" | "Google.es-ES-Standard-D" | "Google.es-ES-Wavenet-B" | "Google.es-ES-Wavenet-C" | "Google.es-ES-Wavenet-D" | "Google.es-US-Neural2-A" | "Google.es-US-Neural2-B" | "Google.es-US-Neural2-C" | "Google.es-US-Standard-A" | "Google.es-US-Standard-B" | "Google.es-US-Standard-C" | "Google.es-US-Wavenet-A" | "Google.es-US-Wavenet-B" | "Google.es-US-Wavenet-C" | "Google.eu-ES-Standard-A" | "Google.fi-FI-Standard-A" | "Google.fi-FI-Wavenet-A" | "Google.fil-PH-Standard-A" | "Google.fil-PH-Standard-B" | "Google.fil-PH-Standard-C" | "Google.fil-PH-Standard-D" | "Google.fil-PH-Wavenet-A" | "Google.fil-PH-Wavenet-B" | "Google.fil-PH-Wavenet-C" | "Google.fil-PH-Wavenet-D" | "Google.fil-ph-Neural2-A" | "Google.fil-ph-Neural2-D" | "Google.fr-CA-Neural2-A" | "Google.fr-CA-Neural2-B" | "Google.fr-CA-Neural2-C" | "Google.fr-CA-Neural2-D" | "Google.fr-CA-Standard-A" | "Google.fr-CA-Standard-B" | "Google.fr-CA-Standard-C" | "Google.fr-CA-Standard-D" | "Google.fr-CA-Wavenet-A" | "Google.fr-CA-Wavenet-B" | "Google.fr-CA-Wavenet-C" | "Google.fr-CA-Wavenet-D" | "Google.fr-FR-Neural2-A" | "Google.fr-FR-Neural2-B" | "Google.fr-FR-Neural2-C" | "Google.fr-FR-Neural2-D" | "Google.fr-FR-Neural2-E" | "Google.fr-FR-Standard-A" | "Google.fr-FR-Standard-B" | "Google.fr-FR-Standard-C" | "Google.fr-FR-Standard-D" | "Google.fr-FR-Standard-E" | "Google.fr-FR-Wavenet-A" | "Google.fr-FR-Wavenet-B" | "Google.fr-FR-Wavenet-C" | "Google.fr-FR-Wavenet-D" | "Google.fr-FR-Wavenet-E" | "Google.gl-ES-Standard-A" | "Google.gu-IN-Standard-C" | "Google.gu-IN-Standard-D" | "Google.gu-IN-Wavenet-C" | "Google.gu-IN-Wavenet-D" | "Google.he-IL-Standard-A" | "Google.he-IL-Standard-B" | "Google.he-IL-Standard-C" | "Google.he-IL-Standard-D" | "Google.he-IL-Wavenet-A" | "Google.he-IL-Wavenet-B" | "Google.he-IL-Wavenet-C" | "Google.he-IL-Wavenet-D" | "Google.hi-IN-Neural2-A" | "Google.hi-IN-Neural2-B" | "Google.hi-IN-Neural2-C" | "Google.hi-IN-Neural2-D" | "Google.hi-IN-Standard-A" | "Google.hi-IN-Standard-B" | "Google.hi-IN-Standard-C" | "Google.hi-IN-Standard-D" | "Google.hi-IN-Wavenet-A" | "Google.hi-IN-Wavenet-B" | "Google.hi-IN-Wavenet-C" | "Google.hi-IN-Wavenet-D" | "Google.hu-HU-Standard-A" | "Google.hu-HU-Wavenet-A" | "Google.id-ID-Standard-A" | "Google.id-ID-Standard-B" | "Google.id-ID-Standard-C" | "Google.id-ID-Standard-D" | "Google.id-ID-Wavenet-A" | "Google.id-ID-Wavenet-B" | "Google.id-ID-Wavenet-C" | "Google.id-ID-Wavenet-D" | "Google.is-IS-Standard-A" | "Google.it-IT-Neural2-A" | "Google.it-IT-Neural2-C" | "Google.it-IT-Standard-B" | "Google.it-IT-Standard-C" | "Google.it-IT-Standard-D" | "Google.it-IT-Wavenet-B" | "Google.it-IT-Wavenet-C" | "Google.it-IT-Wavenet-D" | "Google.ja-JP-Neural2-B" | "Google.ja-JP-Neural2-C" | "Google.ja-JP-Neural2-D" | "Google.ja-JP-Standard-A" | "Google.ja-JP-Standard-B" | "Google.ja-JP-Standard-C" | "Google.ja-JP-Standard-D" | "Google.ja-JP-Wavenet-A" | "Google.ja-JP-Wavenet-B" | "Google.ja-JP-Wavenet-C" | "Google.ja-JP-Wavenet-D" | "Google.kn-IN-Standard-C" | "Google.kn-IN-Standard-D" | "Google.kn-IN-Wavenet-C" | "Google.kn-IN-Wavenet-D" | "Google.ko-KR-Neural2-A" | "Google.ko-KR-Neural2-B" | "Google.ko-KR-Neural2-C" | "Google.ko-KR-Standard-A" | "Google.ko-KR-Standard-B" | "Google.ko-KR-Standard-C" | "Google.ko-KR-Standard-D" | "Google.ko-KR-Wavenet-A" | "Google.ko-KR-Wavenet-B" | "Google.ko-KR-Wavenet-C" | "Google.ko-KR-Wavenet-D" | "Google.lt-LT-Standard-A" | "Google.lv-LV-Standard-A" | "Google.ml-IN-Wavenet-C" | "Google.ml-IN-Wavenet-D" | "Google.mr-IN-Standard-A" | "Google.mr-IN-Standard-B" | "Google.mr-IN-Standard-C" | "Google.mr-IN-Wavenet-A" | "Google.mr-IN-Wavenet-B" | "Google.mr-IN-Wavenet-C" | "Google.ms-MY-Standard-A" | "Google.ms-MY-Standard-B" | "Google.ms-MY-Standard-C" | "Google.ms-MY-Standard-D" | "Google.ms-MY-Wavenet-A" | "Google.ms-MY-Wavenet-B" | "Google.ms-MY-Wavenet-C" | "Google.ms-MY-Wavenet-D" | "Google.nb-NO-Standard-A" | "Google.nb-NO-Standard-B" | "Google.nb-NO-Standard-C" | "Google.nb-NO-Standard-D" | "Google.nb-NO-Standard-E" | "Google.nb-NO-Wavenet-A" | "Google.nb-NO-Wavenet-B" | "Google.nb-NO-Wavenet-C" | "Google.nb-NO-Wavenet-D" | "Google.nb-NO-Wavenet-E" | "Google.nl-BE-Standard-A" | "Google.nl-BE-Standard-B" | "Google.nl-BE-Wavenet-A" | "Google.nl-BE-Wavenet-B" | "Google.nl-NL-Standard-A" | "Google.nl-NL-Standard-B" | "Google.nl-NL-Standard-C" | "Google.nl-NL-Standard-D" | "Google.nl-NL-Standard-E" | "Google.nl-NL-Wavenet-A" | "Google.nl-NL-Wavenet-B" | "Google.nl-NL-Wavenet-C" | "Google.nl-NL-Wavenet-D" | "Google.nl-NL-Wavenet-E" | "Google.pa-IN-Standard-A" | "Google.pa-IN-Standard-B" | "Google.pa-IN-Standard-C" | "Google.pa-IN-Standard-D" | "Google.pa-IN-Wavenet-A" | "Google.pa-IN-Wavenet-B" | "Google.pa-IN-Wavenet-C" | "Google.pa-IN-Wavenet-D" | "Google.pl-PL-Standard-A" | "Google.pl-PL-Standard-B" | "Google.pl-PL-Standard-C" | "Google.pl-PL-Standard-D" | "Google.pl-PL-Standard-E" | "Google.pl-PL-Wavenet-A" | "Google.pl-PL-Wavenet-B" | "Google.pl-PL-Wavenet-C" | "Google.pl-PL-Wavenet-D" | "Google.pl-PL-Wavenet-E" | "Google.pt-BR-Neural2-A" | "Google.pt-BR-Neural2-B" | "Google.pt-BR-Neural2-C" | "Google.pt-BR-Standard-A" | "Google.pt-BR-Standard-B" | "Google.pt-BR-Standard-C" | "Google.pt-BR-Wavenet-A" | "Google.pt-BR-Wavenet-B" | "Google.pt-BR-Wavenet-C" | "Google.pt-PT-Standard-A" | "Google.pt-PT-Standard-B" | "Google.pt-PT-Standard-C" | "Google.pt-PT-Standard-D" | "Google.pt-PT-Wavenet-A" | "Google.pt-PT-Wavenet-B" | "Google.pt-PT-Wavenet-C" | "Google.pt-PT-Wavenet-D" | "Google.ro-RO-Standard-A" | "Google.ro-RO-Wavenet-A" | "Google.ru-RU-Standard-A" | "Google.ru-RU-Standard-B" | "Google.ru-RU-Standard-C" | "Google.ru-RU-Standard-D" | "Google.ru-RU-Standard-E" | "Google.ru-RU-Wavenet-A" | "Google.ru-RU-Wavenet-B" | "Google.ru-RU-Wavenet-C" | "Google.ru-RU-Wavenet-D" | "Google.ru-RU-Wavenet-E" | "Google.sk-SK-Standard-A" | "Google.sk-SK-Wavenet-A" | "Google.sr-RS-Standard-A" | "Google.sv-SE-Standard-A" | "Google.sv-SE-Standard-B" | "Google.sv-SE-Standard-C" | "Google.sv-SE-Standard-D" | "Google.sv-SE-Standard-E" | "Google.sv-SE-Wavenet-A" | "Google.sv-SE-Wavenet-B" | "Google.sv-SE-Wavenet-C" | "Google.sv-SE-Wavenet-D" | "Google.sv-SE-Wavenet-E" | "Google.ta-IN-Standard-A" | "Google.ta-IN-Standard-B" | "Google.ta-IN-Standard-C" | "Google.ta-IN-Standard-D" | "Google.ta-IN-Wavenet-A" | "Google.ta-IN-Wavenet-B" | "Google.ta-IN-Wavenet-C" | "Google.ta-IN-Wavenet-D" | "Google.te-IN-Standard-A" | "Google.te-IN-Standard-B" | "Google.th-TH-Neural2-C" | "Google.th-TH-Standard-A" | "Google.tr-TR-Standard-A" | "Google.tr-TR-Standard-B" | "Google.tr-TR-Standard-C" | "Google.tr-TR-Standard-D" | "Google.tr-TR-Standard-E" | "Google.tr-TR-Wavenet-A" | "Google.tr-TR-Wavenet-B" | "Google.tr-TR-Wavenet-C" | "Google.tr-TR-Wavenet-D" | "Google.tr-TR-Wavenet-E" | "Google.uk-UA-Standard-A" | "Google.uk-UA-Wavenet-A" | "Google.vi-VN-Neural2-A" | "Google.vi-VN-Neural2-D" | "Google.vi-VN-Standard-A" | "Google.vi-VN-Standard-B" | "Google.vi-VN-Standard-C" | "Google.vi-VN-Standard-D" | "Google.vi-VN-Wavenet-A" | "Google.vi-VN-Wavenet-B" | "Google.vi-VN-Wavenet-C" | "Google.vi-VN-Wavenet-D" | "Google.yue-HK-Standard-A" | "Google.yue-HK-Standard-B" | "Google.yue-HK-Standard-C" | "Google.yue-HK-Standard-D" | "Polly.Aditi" | "Polly.Amy" | "Polly.Astrid" | "Polly.Bianca" | "Polly.Brian" | "Polly.Camila" | "Polly.Carla" | "Polly.Carmen" | "Polly.Celine" | "Polly.Chantal" | "Polly.Conchita" | "Polly.Cristiano" | "Polly.Dora" | "Polly.Emma" | "Polly.Enrique" | "Polly.Ewa" | "Polly.Filiz" | "Polly.Geraint" | "Polly.Giorgio" | "Polly.Gwyneth" | "Polly.Hans" | "Polly.Ines" | "Polly.Ivy" | "Polly.Jacek" | "Polly.Jan" | "Polly.Joanna" | "Polly.Joey" | "Polly.Justin" | "Polly.Karl" | "Polly.Kendra" | "Polly.Kimberly" | "Polly.Lea" | "Polly.Liv" | "Polly.Lotte" | "Polly.Lucia" | "Polly.Lupe" | "Polly.Mads" | "Polly.Maja" | "Polly.Marlene" | "Polly.Mathieu" | "Polly.Matthew" | "Polly.Maxim" | "Polly.Mia" | "Polly.Miguel" | "Polly.Mizuki" | "Polly.Naja" | "Polly.Nicole" | "Polly.Penelope" | "Polly.Raveena" | "Polly.Ricardo" | "Polly.Ruben" | "Polly.Russell" | "Polly.Salli" | "Polly.Seoyeon" | "Polly.Takumi" | "Polly.Tatyana" | "Polly.Vicki" | "Polly.Vitoria" | "Polly.Zeina" | "Polly.Zhiyu" | "Polly.Adriano-Neural" | "Polly.Amy-Neural" | "Polly.Andres-Neural" | "Polly.Aria-Neural" | "Polly.Arlet-Neural" | "Polly.Arthur-Neural" | "Polly.Ayanda-Neural" | "Polly.Bianca-Neural" | "Polly.Brian-Neural" | "Polly.Camila-Neural" | "Polly.Daniel-Neural" | "Polly.Danielle-Neural" | "Polly.Elin-Neural" | "Polly.Emma-Neural" | "Polly.Gabrielle-Neural" | "Polly.Gregory-Neural" | "Polly.Hala-Neural" | "Polly.Hannah-Neural" | "Polly.Hiujin-Neural" | "Polly.Ida-Neural" | "Polly.Ines-Neural" | "Polly.Isabelle-Neural" | "Polly.Ivy-Neural" | "Polly.Joanna-Neural" | "Polly.Joey-Neural" | "Polly.Justin-Neural" | "Polly.Kajal-Neural" | "Polly.Kazuha-Neural" | "Polly.Kendra-Neural" | "Polly.Kevin-Neural" | "Polly.Kimberly-Neural" | "Polly.Laura-Neural" | "Polly.Lea-Neural" | "Polly.Liam-Neural" | "Polly.Lisa-Neural" | "Polly.Lucia-Neural" | "Polly.Lupe-Neural" | "Polly.Matthew-Neural" | "Polly.Mia-Neural" | "Polly.Niamh-Neural" | "Polly.Ola-Neural" | "Polly.Olivia-Neural" | "Polly.Pedro-Neural" | "Polly.Remi-Neural" | "Polly.Ruth-Neural" | "Polly.Salli-Neural" | "Polly.Seoyeon-Neural" | "Polly.Sergio-Neural" | "Polly.Sofie-Neural" | "Polly.Stephen-Neural" | "Polly.Suvi-Neural" | "Polly.Takumi-Neural" | "Polly.Tomoko-Neural" | "Polly.Thiago-Neural" | "Polly.Vicki-Neural" | "Polly.Vitoria-Neural" | "Polly.Zayd-Neural" | "Polly.Zhiyu-Neural";
    type SipEvent = "initiated" | "ringing" | "answered" | "completed";
    type SiprecStatusCallbackMethod = "GET" | "POST";
    type SiprecTrack = "inbound_track" | "outbound_track" | "both_tracks";
    type SsmlBreakStrength = "none" | "x-weak" | "weak" | "medium" | "strong" | "x-strong";
    type SsmlEmphasisLevel = "strong" | "moderate" | "reduced";
    type SsmlLangXmlLang = "arb" | "ar-AE" | "ca-ES" | "cmn-CN" | "cy-GB" | "da-DK" | "de-DE" | "de-AT" | "en-AU" | "en-GB" | "en-GB-WLS" | "en-IN" | "en-NZ" | "en-US" | "en-ZA" | "es-ES" | "es-MX" | "es-US" | "fi-FI" | "fr-CA" | "fr-FR" | "he-IL" | "hi-IN" | "is-IS" | "it-IT" | "ja-JP" | "ko-KR" | "nb-NO" | "nl-NL" | "pl-PL" | "pt-BR" | "pt-PT" | "ro-RO" | "ru-RU" | "sv-SE" | "tr-TR" | "yue-CN";
    type SsmlPhonemeAlphabet = "ipa" | "x-sampa" | "x-amazon-jyutping" | "x-amazon-pinyin" | "x-amazon-pron-kana" | "x-amazon-yomigana";
    type SsmlSayAsFormat = "mdy" | "dmy" | "ymd" | "md" | "dm" | "ym" | "my" | "d" | "m" | "y" | "yyyymmdd";
    type SsmlSayAsInterpretAs = "characters" | "spell-out" | "cardinal" | "number" | "ordinal" | "digits" | "fraction" | "unit" | "date" | "time" | "address" | "expletive" | "telephone";
    type StreamStatusCallbackMethod = "GET" | "POST";
    type StreamTrack = "inbound_track" | "outbound_track" | "both_tracks";
    type TranscriptionStatusCallbackMethod = "GET" | "POST";
    type TranscriptionTrack = "inbound_track" | "outbound_track" | "both_tracks";
    type WhatsAppEvent = "initiated" | "ringing" | "answered" | "completed";
    /**
     * Attributes to pass to connect
     */
    export interface ConnectAttributes {
        /** action - Action URL */
        action?: string;
        /** method - Action URL method */
        method?: string;
    }
    /**
     * Attributes to pass to dial
     */
    export interface DialAttributes {
        /** action - Action URL */
        action?: string;
        /** answerOnBridge - Preserve the ringing behavior of the inbound call until the Dialed call picks up */
        answerOnBridge?: boolean;
        /** callerId - Caller ID to display */
        callerId?: string;
        /** events - Subscription to events */
        events?: DialEvents;
        /** hangupOnStar - Hangup call on star press */
        hangupOnStar?: boolean;
        /** method - Action URL method */
        method?: string;
        /** record - Record the call */
        record?: DialRecord;
        /** recordingStatusCallback - Recording status callback URL */
        recordingStatusCallback?: string;
        /** recordingStatusCallbackEvent - Recording status callback events */
        recordingStatusCallbackEvent?: DialRecordingEvent[];
        /** recordingStatusCallbackMethod - Recording status callback URL method */
        recordingStatusCallbackMethod?: string;
        /** recordingTrack - To indicate which audio track should be recorded */
        recordingTrack?: DialRecordingTrack;
        /** referMethod - The HTTP method to use for the refer Webhook */
        referMethod?: string;
        /** referUrl - Webhook that will receive future SIP REFER requests */
        referUrl?: string;
        /** ringTone - Ringtone allows you to override the ringback tone that Twilio will play back to the caller while executing the Dial */
        ringTone?: DialRingTone;
        /** sequential - Used to determine if child TwiML nouns should be dialed in order, one after the other (sequential) or dial all at once (parallel). Default is false, parallel */
        sequential?: boolean;
        /** timeLimit - Max time length */
        timeLimit?: number;
        /** timeout - Time to wait for answer */
        timeout?: number;
        /** trim - Trim the recording */
        trim?: DialTrim;
    }
    /**
     * Attributes to pass to enqueue
     */
    export interface EnqueueAttributes {
        /** action - Action URL */
        action?: string;
        /** maxQueueSize - Maximum size of queue */
        maxQueueSize?: number;
        /** method - Action URL method */
        method?: string;
        /** waitUrl - Wait URL */
        waitUrl?: string;
        /** waitUrlMethod - Wait URL method */
        waitUrlMethod?: string;
        /** workflowSid - TaskRouter Workflow SID */
        workflowSid?: string;
    }
    /**
     * Attributes to pass to gather
     */
    export interface GatherAttributes {
        /** action - Action URL */
        action?: string;
        /** actionOnEmptyResult - Force webhook to the action URL event if there is no input */
        actionOnEmptyResult?: boolean;
        /** bargeIn - Stop playing media upon speech */
        bargeIn?: boolean;
        /** debug - Allow debug for gather */
        debug?: boolean;
        /** enhanced - Use enhanced speech model */
        enhanced?: boolean;
        /** finishOnKey - Finish gather on key */
        finishOnKey?: string;
        /** hints - Speech recognition hints */
        hints?: string;
        /** input - Input type Twilio should accept */
        input?: GatherInput[];
        /** language - Language to use */
        language?: GatherLanguage;
        /** maxSpeechTime - Max allowed time for speech input */
        maxSpeechTime?: number;
        /** method - Action URL method */
        method?: string;
        /** numDigits - Number of digits to collect */
        numDigits?: number;
        /** partialResultCallback - Partial result callback URL */
        partialResultCallback?: string;
        /** partialResultCallbackMethod - Partial result callback URL method */
        partialResultCallbackMethod?: string;
        /** profanityFilter - Profanity Filter on speech */
        profanityFilter?: boolean;
        /** speechModel - Specify the model that is best suited for your use case */
        speechModel?: string;
        /** speechTimeout - Time to wait to gather speech input and it should be either auto or a positive integer. */
        speechTimeout?: string;
        /** timeout - Time to wait to gather input */
        timeout?: number;
    }
    /**
     * Attributes to pass to pause
     */
    export interface PauseAttributes {
        /** length - Length in seconds to pause */
        length?: number;
    }
    /**
     * Attributes to pass to play
     */
    export interface PlayAttributes {
        /** digits - Play DTMF tones for digits */
        digits?: string;
        /** loop - Times to loop media */
        loop?: number;
    }
    /**
     * Attributes to pass to queue
     */
    export interface QueueAttributes {
        /** method - Action URL method */
        method?: string;
        /** postWorkActivitySid - TaskRouter Activity SID */
        postWorkActivitySid?: string;
        /** reservationSid - TaskRouter Reservation SID */
        reservationSid?: string;
        /** url - Action URL */
        url?: string;
    }
    /**
     * Attributes to pass to record
     */
    export interface RecordAttributes {
        /** action - Action URL */
        action?: string;
        /** finishOnKey - Finish recording on key */
        finishOnKey?: string;
        /** maxLength - Max time to record in seconds */
        maxLength?: number;
        /** method - Action URL method */
        method?: string;
        /** playBeep - Play beep */
        playBeep?: boolean;
        /** recordingStatusCallback - Status callback URL */
        recordingStatusCallback?: string;
        /** recordingStatusCallbackEvent - Recording status callback events */
        recordingStatusCallbackEvent?: RecordRecordingEvent[];
        /** recordingStatusCallbackMethod - Status callback URL method */
        recordingStatusCallbackMethod?: string;
        /** timeout - Timeout to begin recording */
        timeout?: number;
        /** transcribe - Transcribe the recording */
        transcribe?: boolean;
        /** transcribeCallback - Transcribe callback URL */
        transcribeCallback?: string;
        /** trim - Trim the recording */
        trim?: RecordTrim;
    }
    /**
     * Attributes to pass to redirect
     */
    export interface RedirectAttributes {
        /** method - Redirect URL method */
        method?: string;
    }
    /**
     * Attributes to pass to reject
     */
    export interface RejectAttributes {
        /** reason - Rejection reason */
        reason?: RejectReason;
    }
    /**
     * Attributes to pass to say
     */
    export interface SayAttributes {
        /** language - Message language */
        language?: SayLanguage;
        /** loop - Times to loop message */
        loop?: number;
        /** voice - Voice to use */
        voice?: SayVoice;
    }
    /**
     * Attributes to pass to sms
     */
    export interface SmsAttributes {
        /** action - Action URL */
        action?: string;
        /** from - Number to send message from */
        from?: string;
        /** method - Action URL method */
        method?: string;
        /** statusCallback - Status callback URL */
        statusCallback?: string;
        /** to - Number to send message to */
        to?: string;
    }
    /**
     * Attributes to pass to pay
     */
    export interface PayAttributes {
        /** action - Action URL */
        action?: string;
        /** bankAccountType - Bank account type for ach transactions. If set, payment method attribute must be provided and value should be set to ach-debit. defaults to consumer-checking */
        bankAccountType?: PayBankAccountType;
        /** chargeAmount - Amount to process. If value is greater than 0 then make the payment else create a payment token */
        chargeAmount?: string;
        /** currency - Currency of the amount attribute */
        currency?: string;
        /** description - Details regarding the payment */
        description?: string;
        /** input - Input type Twilio should accept */
        input?: PayInput;
        /** language - Language to use */
        language?: PayLanguage;
        /** maxAttempts - Maximum number of allowed retries when gathering input */
        maxAttempts?: number;
        /** minPostalCodeLength - Prompt for minimum postal code length */
        minPostalCodeLength?: number;
        /** paymentConnector - Unique name for payment connector */
        paymentConnector?: string;
        /** paymentMethod - Payment method to be used. defaults to credit-card */
        paymentMethod?: PayPaymentMethod;
        /** postalCode - Prompt for postal code and it should be true/false or default postal code */
        postalCode?: string;
        /** securityCode - Prompt for security code */
        securityCode?: boolean;
        /** statusCallback - Status callback URL */
        statusCallback?: string;
        /** statusCallbackMethod - Status callback method */
        statusCallbackMethod?: PayStatusCallbackMethod;
        /** timeout - Time to wait to gather input */
        timeout?: number;
        /** tokenType - Type of token */
        tokenType?: PayTokenType;
        /** validCardTypes - Comma separated accepted card types */
        validCardTypes?: PayValidCardTypes[];
    }
    /**
     * Attributes to pass to prompt
     */
    export interface PromptAttributes {
        /** attempt - Current attempt count */
        attempt?: number[];
        /** cardType - Type of the credit card */
        cardType?: PromptCardType[];
        /** errorType - Type of error */
        errorType?: PromptErrorType[];
        /** for - Name of the payment source data element */
        for?: PromptFor;
        /** requireMatchingInputs - Require customer to input requested information twice and verify matching. */
        requireMatchingInputs?: boolean;
    }
    /**
     * Attributes to pass to start
     */
    export interface StartAttributes {
        /** action - Action URL */
        action?: string;
        /** method - Action URL method */
        method?: string;
    }
    /**
     * Attributes to pass to refer
     */
    export interface ReferAttributes {
        /** action - Action URL */
        action?: string;
        /** method - Action URL method */
        method?: string;
    }
    /**
     * Attributes to pass to stream
     */
    export interface StreamAttributes {
        /** connectorName - Unique name for Stream Connector */
        connectorName?: string;
        /** name - Friendly name given to the Stream */
        name?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackMethod - Status Callback URL method */
        statusCallbackMethod?: StreamStatusCallbackMethod;
        /** track - Track to be streamed to remote service */
        track?: StreamTrack;
        /** url - URL of the remote service where the Stream is routed */
        url?: string;
    }
    /**
     * Attributes to pass to siprec
     */
    export interface SiprecAttributes {
        /** connectorName - Unique name for Connector */
        connectorName?: string;
        /** name - Friendly name given to SIPREC */
        name?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackMethod - Status Callback URL method */
        statusCallbackMethod?: SiprecStatusCallbackMethod;
        /** track - Track to be streamed to remote service */
        track?: SiprecTrack;
    }
    /**
     * Attributes to pass to transcription
     */
    export interface TranscriptionAttributes {
        /** enableAutomaticPunctuation - Enable Automatic Punctuation */
        enableAutomaticPunctuation?: boolean;
        /** hints - Hints to be provided to the transcription engine */
        hints?: string;
        /** inboundTrackLabel - Friendly name given to the Inbound Track */
        inboundTrackLabel?: string;
        /** intelligenceService - The SID or the unique name of the Intelligence Service to be used */
        intelligenceService?: string;
        /** languageCode - Language Code used by the transcription engine */
        languageCode?: string;
        /** name - Friendly name given to the Transcription */
        name?: string;
        /** outboundTrackLabel - Friendly name given to the Outbound Track Label */
        outboundTrackLabel?: string;
        /** partialResults - Indicates if partial results are going to be send to the customer */
        partialResults?: boolean;
        /** profanityFilter - Enable Profanity Filter */
        profanityFilter?: boolean;
        /** speechModel - Speech Model used by the transcription engine */
        speechModel?: string;
        /** statusCallbackMethod - Status Callback URL method */
        statusCallbackMethod?: TranscriptionStatusCallbackMethod;
        /** statusCallbackUrl - Status Callback URL */
        statusCallbackUrl?: string;
        /** track - Track to be analyze by the provider */
        track?: TranscriptionTrack;
        /** transcriptionEngine - Transcription Engine to be used */
        transcriptionEngine?: string;
    }
    /**
     * Attributes to pass to config
     */
    export interface ConfigAttributes {
        /** name - The name of the custom config */
        name?: string;
        /** value - The value of the custom config */
        value?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to stream
     */
    export interface StreamAttributes {
        /** connectorName - Unique name for Stream Connector */
        connectorName?: string;
        /** name - Friendly name given to the Stream */
        name?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackMethod - Status Callback URL method */
        statusCallbackMethod?: StreamStatusCallbackMethod;
        /** track - Track to be streamed to remote service */
        track?: StreamTrack;
        /** url - URL of the remote service where the Stream is routed */
        url?: string;
    }
    /**
     * Attributes to pass to siprec
     */
    export interface SiprecAttributes {
        /** connectorName - Unique name for Connector */
        connectorName?: string;
        /** name - Friendly name given to SIPREC */
        name?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackMethod - Status Callback URL method */
        statusCallbackMethod?: SiprecStatusCallbackMethod;
        /** track - Track to be streamed to remote service */
        track?: SiprecTrack;
    }
    /**
     * Attributes to pass to transcription
     */
    export interface TranscriptionAttributes {
        /** enableAutomaticPunctuation - Enable Automatic Punctuation */
        enableAutomaticPunctuation?: boolean;
        /** hints - Hints to be provided to the transcription engine */
        hints?: string;
        /** inboundTrackLabel - Friendly name given to the Inbound Track */
        inboundTrackLabel?: string;
        /** intelligenceService - The SID or the unique name of the Intelligence Service to be used */
        intelligenceService?: string;
        /** languageCode - Language Code used by the transcription engine */
        languageCode?: string;
        /** name - Friendly name given to the Transcription */
        name?: string;
        /** outboundTrackLabel - Friendly name given to the Outbound Track Label */
        outboundTrackLabel?: string;
        /** partialResults - Indicates if partial results are going to be send to the customer */
        partialResults?: boolean;
        /** profanityFilter - Enable Profanity Filter */
        profanityFilter?: boolean;
        /** speechModel - Speech Model used by the transcription engine */
        speechModel?: string;
        /** statusCallbackMethod - Status Callback URL method */
        statusCallbackMethod?: TranscriptionStatusCallbackMethod;
        /** statusCallbackUrl - Status Callback URL */
        statusCallbackUrl?: string;
        /** track - Track to be analyze by the provider */
        track?: TranscriptionTrack;
        /** transcriptionEngine - Transcription Engine to be used */
        transcriptionEngine?: string;
    }
    /**
     * Attributes to pass to say
     */
    export interface SayAttributes {
        /** language - Message language */
        language?: SayLanguage;
        /** loop - Times to loop message */
        loop?: number;
        /** voice - Voice to use */
        voice?: SayVoice;
    }
    /**
     * Attributes to pass to play
     */
    export interface PlayAttributes {
        /** digits - Play DTMF tones for digits */
        digits?: string;
        /** loop - Times to loop media */
        loop?: number;
    }
    /**
     * Attributes to pass to pause
     */
    export interface PauseAttributes {
        /** length - Length in seconds to pause */
        length?: number;
    }
    /**
     * Attributes to pass to break
     */
    export interface SsmlBreakAttributes {
        /** strength - Set a pause based on strength */
        strength?: SsmlBreakStrength;
        /** time - Set a pause to a specific length of time in seconds or milliseconds, available values: [number]s, [number]ms */
        time?: string;
    }
    /**
     * Attributes to pass to emphasis
     */
    export interface SsmlEmphasisAttributes {
        /** level - Specify the degree of emphasis */
        level?: SsmlEmphasisLevel;
    }
    /**
     * Attributes to pass to lang
     */
    export interface SsmlLangAttributes {
        /** xml:lang - Specify the language */
        "xml:lang"?: SsmlLangXmlLang;
    }
    /**
     * Attributes to pass to phoneme
     */
    export interface SsmlPhonemeAttributes {
        /** alphabet - Specify the phonetic alphabet */
        alphabet?: SsmlPhonemeAlphabet;
        /** ph - Specifiy the phonetic symbols for pronunciation */
        ph?: string;
    }
    /**
     * Attributes to pass to prosody
     */
    export interface SsmlProsodyAttributes {
        /** pitch - Specify the pitch, available values: default, x-low, low, medium, high, x-high, +n%, -n% */
        pitch?: string;
        /** rate - Specify the rate, available values: x-slow, slow, medium, fast, x-fast, n% */
        rate?: string;
        /** volume - Specify the volume, available values: default, silent, x-soft, soft, medium, loud, x-loud, +ndB, -ndB */
        volume?: string;
    }
    /**
     * Attributes to pass to sayAs
     */
    export interface SsmlSayAsAttributes {
        /** format - Specify the format of the date when interpret-as is set to date */
        format?: SsmlSayAsFormat;
        /** interpret-as - Specify the type of words are spoken */
        "interpret-as"?: SsmlSayAsInterpretAs;
    }
    /**
     * Attributes to pass to sub
     */
    export interface SsmlSubAttributes {
        /** alias - Substitute a different word (or pronunciation) for selected text such as an acronym or abbreviation */
        alias?: string;
    }
    /**
     * Attributes to pass to w
     */
    export interface SsmlWAttributes {
        /** role - Customize the pronunciation of words by specifying the word’s part of speech or alternate meaning */
        role?: string;
    }
    /**
     * Attributes to pass to break
     */
    export interface SsmlBreakAttributes {
        /** strength - Set a pause based on strength */
        strength?: SsmlBreakStrength;
        /** time - Set a pause to a specific length of time in seconds or milliseconds, available values: [number]s, [number]ms */
        time?: string;
    }
    /**
     * Attributes to pass to emphasis
     */
    export interface SsmlEmphasisAttributes {
        /** level - Specify the degree of emphasis */
        level?: SsmlEmphasisLevel;
    }
    /**
     * Attributes to pass to phoneme
     */
    export interface SsmlPhonemeAttributes {
        /** alphabet - Specify the phonetic alphabet */
        alphabet?: SsmlPhonemeAlphabet;
        /** ph - Specifiy the phonetic symbols for pronunciation */
        ph?: string;
    }
    /**
     * Attributes to pass to prosody
     */
    export interface SsmlProsodyAttributes {
        /** pitch - Specify the pitch, available values: default, x-low, low, medium, high, x-high, +n%, -n% */
        pitch?: string;
        /** rate - Specify the rate, available values: x-slow, slow, medium, fast, x-fast, n% */
        rate?: string;
        /** volume - Specify the volume, available values: default, silent, x-soft, soft, medium, loud, x-loud, +ndB, -ndB */
        volume?: string;
    }
    /**
     * Attributes to pass to sayAs
     */
    export interface SsmlSayAsAttributes {
        /** format - Specify the format of the date when interpret-as is set to date */
        format?: SsmlSayAsFormat;
        /** interpret-as - Specify the type of words are spoken */
        "interpret-as"?: SsmlSayAsInterpretAs;
    }
    /**
     * Attributes to pass to sub
     */
    export interface SsmlSubAttributes {
        /** alias - Substitute a different word (or pronunciation) for selected text such as an acronym or abbreviation */
        alias?: string;
    }
    /**
     * Attributes to pass to break
     */
    export interface SsmlBreakAttributes {
        /** strength - Set a pause based on strength */
        strength?: SsmlBreakStrength;
        /** time - Set a pause to a specific length of time in seconds or milliseconds, available values: [number]s, [number]ms */
        time?: string;
    }
    /**
     * Attributes to pass to emphasis
     */
    export interface SsmlEmphasisAttributes {
        /** level - Specify the degree of emphasis */
        level?: SsmlEmphasisLevel;
    }
    /**
     * Attributes to pass to lang
     */
    export interface SsmlLangAttributes {
        /** xml:lang - Specify the language */
        "xml:lang"?: SsmlLangXmlLang;
    }
    /**
     * Attributes to pass to phoneme
     */
    export interface SsmlPhonemeAttributes {
        /** alphabet - Specify the phonetic alphabet */
        alphabet?: SsmlPhonemeAlphabet;
        /** ph - Specifiy the phonetic symbols for pronunciation */
        ph?: string;
    }
    /**
     * Attributes to pass to prosody
     */
    export interface SsmlProsodyAttributes {
        /** pitch - Specify the pitch, available values: default, x-low, low, medium, high, x-high, +n%, -n% */
        pitch?: string;
        /** rate - Specify the rate, available values: x-slow, slow, medium, fast, x-fast, n% */
        rate?: string;
        /** volume - Specify the volume, available values: default, silent, x-soft, soft, medium, loud, x-loud, +ndB, -ndB */
        volume?: string;
    }
    /**
     * Attributes to pass to sayAs
     */
    export interface SsmlSayAsAttributes {
        /** format - Specify the format of the date when interpret-as is set to date */
        format?: SsmlSayAsFormat;
        /** interpret-as - Specify the type of words are spoken */
        "interpret-as"?: SsmlSayAsInterpretAs;
    }
    /**
     * Attributes to pass to sub
     */
    export interface SsmlSubAttributes {
        /** alias - Substitute a different word (or pronunciation) for selected text such as an acronym or abbreviation */
        alias?: string;
    }
    /**
     * Attributes to pass to w
     */
    export interface SsmlWAttributes {
        /** role - Customize the pronunciation of words by specifying the word’s part of speech or alternate meaning */
        role?: string;
    }
    /**
     * Attributes to pass to break
     */
    export interface SsmlBreakAttributes {
        /** strength - Set a pause based on strength */
        strength?: SsmlBreakStrength;
        /** time - Set a pause to a specific length of time in seconds or milliseconds, available values: [number]s, [number]ms */
        time?: string;
    }
    /**
     * Attributes to pass to emphasis
     */
    export interface SsmlEmphasisAttributes {
        /** level - Specify the degree of emphasis */
        level?: SsmlEmphasisLevel;
    }
    /**
     * Attributes to pass to lang
     */
    export interface SsmlLangAttributes {
        /** xml:lang - Specify the language */
        "xml:lang"?: SsmlLangXmlLang;
    }
    /**
     * Attributes to pass to phoneme
     */
    export interface SsmlPhonemeAttributes {
        /** alphabet - Specify the phonetic alphabet */
        alphabet?: SsmlPhonemeAlphabet;
        /** ph - Specifiy the phonetic symbols for pronunciation */
        ph?: string;
    }
    /**
     * Attributes to pass to prosody
     */
    export interface SsmlProsodyAttributes {
        /** pitch - Specify the pitch, available values: default, x-low, low, medium, high, x-high, +n%, -n% */
        pitch?: string;
        /** rate - Specify the rate, available values: x-slow, slow, medium, fast, x-fast, n% */
        rate?: string;
        /** volume - Specify the volume, available values: default, silent, x-soft, soft, medium, loud, x-loud, +ndB, -ndB */
        volume?: string;
    }
    /**
     * Attributes to pass to sayAs
     */
    export interface SsmlSayAsAttributes {
        /** format - Specify the format of the date when interpret-as is set to date */
        format?: SsmlSayAsFormat;
        /** interpret-as - Specify the type of words are spoken */
        "interpret-as"?: SsmlSayAsInterpretAs;
    }
    /**
     * Attributes to pass to sub
     */
    export interface SsmlSubAttributes {
        /** alias - Substitute a different word (or pronunciation) for selected text such as an acronym or abbreviation */
        alias?: string;
    }
    /**
     * Attributes to pass to w
     */
    export interface SsmlWAttributes {
        /** role - Customize the pronunciation of words by specifying the word’s part of speech or alternate meaning */
        role?: string;
    }
    /**
     * Attributes to pass to break
     */
    export interface SsmlBreakAttributes {
        /** strength - Set a pause based on strength */
        strength?: SsmlBreakStrength;
        /** time - Set a pause to a specific length of time in seconds or milliseconds, available values: [number]s, [number]ms */
        time?: string;
    }
    /**
     * Attributes to pass to emphasis
     */
    export interface SsmlEmphasisAttributes {
        /** level - Specify the degree of emphasis */
        level?: SsmlEmphasisLevel;
    }
    /**
     * Attributes to pass to lang
     */
    export interface SsmlLangAttributes {
        /** xml:lang - Specify the language */
        "xml:lang"?: SsmlLangXmlLang;
    }
    /**
     * Attributes to pass to phoneme
     */
    export interface SsmlPhonemeAttributes {
        /** alphabet - Specify the phonetic alphabet */
        alphabet?: SsmlPhonemeAlphabet;
        /** ph - Specifiy the phonetic symbols for pronunciation */
        ph?: string;
    }
    /**
     * Attributes to pass to prosody
     */
    export interface SsmlProsodyAttributes {
        /** pitch - Specify the pitch, available values: default, x-low, low, medium, high, x-high, +n%, -n% */
        pitch?: string;
        /** rate - Specify the rate, available values: x-slow, slow, medium, fast, x-fast, n% */
        rate?: string;
        /** volume - Specify the volume, available values: default, silent, x-soft, soft, medium, loud, x-loud, +ndB, -ndB */
        volume?: string;
    }
    /**
     * Attributes to pass to sayAs
     */
    export interface SsmlSayAsAttributes {
        /** format - Specify the format of the date when interpret-as is set to date */
        format?: SsmlSayAsFormat;
        /** interpret-as - Specify the type of words are spoken */
        "interpret-as"?: SsmlSayAsInterpretAs;
    }
    /**
     * Attributes to pass to sub
     */
    export interface SsmlSubAttributes {
        /** alias - Substitute a different word (or pronunciation) for selected text such as an acronym or abbreviation */
        alias?: string;
    }
    /**
     * Attributes to pass to w
     */
    export interface SsmlWAttributes {
        /** role - Customize the pronunciation of words by specifying the word’s part of speech or alternate meaning */
        role?: string;
    }
    /**
     * Attributes to pass to break
     */
    export interface SsmlBreakAttributes {
        /** strength - Set a pause based on strength */
        strength?: SsmlBreakStrength;
        /** time - Set a pause to a specific length of time in seconds or milliseconds, available values: [number]s, [number]ms */
        time?: string;
    }
    /**
     * Attributes to pass to emphasis
     */
    export interface SsmlEmphasisAttributes {
        /** level - Specify the degree of emphasis */
        level?: SsmlEmphasisLevel;
    }
    /**
     * Attributes to pass to lang
     */
    export interface SsmlLangAttributes {
        /** xml:lang - Specify the language */
        "xml:lang"?: SsmlLangXmlLang;
    }
    /**
     * Attributes to pass to phoneme
     */
    export interface SsmlPhonemeAttributes {
        /** alphabet - Specify the phonetic alphabet */
        alphabet?: SsmlPhonemeAlphabet;
        /** ph - Specifiy the phonetic symbols for pronunciation */
        ph?: string;
    }
    /**
     * Attributes to pass to prosody
     */
    export interface SsmlProsodyAttributes {
        /** pitch - Specify the pitch, available values: default, x-low, low, medium, high, x-high, +n%, -n% */
        pitch?: string;
        /** rate - Specify the rate, available values: x-slow, slow, medium, fast, x-fast, n% */
        rate?: string;
        /** volume - Specify the volume, available values: default, silent, x-soft, soft, medium, loud, x-loud, +ndB, -ndB */
        volume?: string;
    }
    /**
     * Attributes to pass to sayAs
     */
    export interface SsmlSayAsAttributes {
        /** format - Specify the format of the date when interpret-as is set to date */
        format?: SsmlSayAsFormat;
        /** interpret-as - Specify the type of words are spoken */
        "interpret-as"?: SsmlSayAsInterpretAs;
    }
    /**
     * Attributes to pass to sub
     */
    export interface SsmlSubAttributes {
        /** alias - Substitute a different word (or pronunciation) for selected text such as an acronym or abbreviation */
        alias?: string;
    }
    /**
     * Attributes to pass to w
     */
    export interface SsmlWAttributes {
        /** role - Customize the pronunciation of words by specifying the word’s part of speech or alternate meaning */
        role?: string;
    }
    /**
     * Attributes to pass to break
     */
    export interface SsmlBreakAttributes {
        /** strength - Set a pause based on strength */
        strength?: SsmlBreakStrength;
        /** time - Set a pause to a specific length of time in seconds or milliseconds, available values: [number]s, [number]ms */
        time?: string;
    }
    /**
     * Attributes to pass to emphasis
     */
    export interface SsmlEmphasisAttributes {
        /** level - Specify the degree of emphasis */
        level?: SsmlEmphasisLevel;
    }
    /**
     * Attributes to pass to lang
     */
    export interface SsmlLangAttributes {
        /** xml:lang - Specify the language */
        "xml:lang"?: SsmlLangXmlLang;
    }
    /**
     * Attributes to pass to phoneme
     */
    export interface SsmlPhonemeAttributes {
        /** alphabet - Specify the phonetic alphabet */
        alphabet?: SsmlPhonemeAlphabet;
        /** ph - Specifiy the phonetic symbols for pronunciation */
        ph?: string;
    }
    /**
     * Attributes to pass to prosody
     */
    export interface SsmlProsodyAttributes {
        /** pitch - Specify the pitch, available values: default, x-low, low, medium, high, x-high, +n%, -n% */
        pitch?: string;
        /** rate - Specify the rate, available values: x-slow, slow, medium, fast, x-fast, n% */
        rate?: string;
        /** volume - Specify the volume, available values: default, silent, x-soft, soft, medium, loud, x-loud, +ndB, -ndB */
        volume?: string;
    }
    /**
     * Attributes to pass to sayAs
     */
    export interface SsmlSayAsAttributes {
        /** format - Specify the format of the date when interpret-as is set to date */
        format?: SsmlSayAsFormat;
        /** interpret-as - Specify the type of words are spoken */
        "interpret-as"?: SsmlSayAsInterpretAs;
    }
    /**
     * Attributes to pass to sub
     */
    export interface SsmlSubAttributes {
        /** alias - Substitute a different word (or pronunciation) for selected text such as an acronym or abbreviation */
        alias?: string;
    }
    /**
     * Attributes to pass to w
     */
    export interface SsmlWAttributes {
        /** role - Customize the pronunciation of words by specifying the word’s part of speech or alternate meaning */
        role?: string;
    }
    /**
     * Attributes to pass to prompt
     */
    export interface PromptAttributes {
        /** attempt - Current attempt count */
        attempt?: number[];
        /** cardType - Type of the credit card */
        cardType?: PromptCardType[];
        /** errorType - Type of error */
        errorType?: PromptErrorType[];
        /** for - Name of the payment source data element */
        for?: PromptFor;
        /** requireMatchingInputs - Require customer to input requested information twice and verify matching. */
        requireMatchingInputs?: boolean;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to say
     */
    export interface SayAttributes {
        /** language - Message language */
        language?: SayLanguage;
        /** loop - Times to loop message */
        loop?: number;
        /** voice - Voice to use */
        voice?: SayVoice;
    }
    /**
     * Attributes to pass to pause
     */
    export interface PauseAttributes {
        /** length - Length in seconds to pause */
        length?: number;
    }
    /**
     * Attributes to pass to play
     */
    export interface PlayAttributes {
        /** digits - Play DTMF tones for digits */
        digits?: string;
        /** loop - Times to loop media */
        loop?: number;
    }
    /**
     * Attributes to pass to task
     */
    export interface TaskAttributes {
        /** priority - Task priority */
        priority?: number;
        /** timeout - Timeout associated with task */
        timeout?: number;
    }
    /**
     * Attributes to pass to client
     */
    export interface ClientAttributes {
        /** method - Client URL Method */
        method?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackEvent - Events to trigger status callback */
        statusCallbackEvent?: ClientEvent[];
        /** statusCallbackMethod - Status Callback URL Method */
        statusCallbackMethod?: string;
        /** url - Client URL */
        url?: string;
    }
    /**
     * Attributes to pass to conference
     */
    export interface ConferenceAttributes {
        /** beep - Play beep when joining */
        beep?: ConferenceBeep;
        /** coach - Call coach */
        coach?: string;
        /** endConferenceOnExit - End the conferenceon exit */
        endConferenceOnExit?: boolean;
        /** eventCallbackUrl - Event callback URL */
        eventCallbackUrl?: string;
        /** jitterBufferSize - Size of jitter buffer for participant */
        jitterBufferSize?: ConferenceJitterBufferSize;
        /** maxParticipants - Maximum number of participants */
        maxParticipants?: number;
        /** muted - Join the conference muted */
        muted?: boolean;
        /** participantLabel - A label for participant */
        participantLabel?: string;
        /** record - Record the conference */
        record?: ConferenceRecord;
        /** recordingStatusCallback - Recording status callback URL */
        recordingStatusCallback?: string;
        /** recordingStatusCallbackEvent - Recording status callback events */
        recordingStatusCallbackEvent?: ConferenceRecordingEvent[];
        /** recordingStatusCallbackMethod - Recording status callback URL method */
        recordingStatusCallbackMethod?: string;
        /** region - Conference region */
        region?: ConferenceRegion;
        /** startConferenceOnEnter - Start the conference on enter */
        startConferenceOnEnter?: boolean;
        /** statusCallback - Status callback URL */
        statusCallback?: string;
        /** statusCallbackEvent - Events to call status callback URL */
        statusCallbackEvent?: ConferenceEvent[];
        /** statusCallbackMethod - Status callback URL method */
        statusCallbackMethod?: string;
        /** trim - Trim the conference recording */
        trim?: ConferenceTrim;
        /** waitMethod - Wait URL method */
        waitMethod?: string;
        /** waitUrl - Wait URL */
        waitUrl?: string;
    }
    /**
     * Attributes to pass to number
     */
    export interface NumberAttributes {
        /** amdStatusCallback - The URL we should call to send amd status information to your application */
        amdStatusCallback?: string;
        /** amdStatusCallbackMethod - HTTP Method to use with amd_status_callback */
        amdStatusCallbackMethod?: string;
        /** byoc - BYOC trunk SID (Beta) */
        byoc?: string;
        /** machineDetection - Enable machine detection or end of greeting detection */
        machineDetection?: string;
        /** machineDetectionSilenceTimeout - Number of milliseconds of initial silence */
        machineDetectionSilenceTimeout?: number;
        /** machineDetectionSpeechEndThreshold - Number of milliseconds of silence after speech activity */
        machineDetectionSpeechEndThreshold?: number;
        /** machineDetectionSpeechThreshold - Number of milliseconds for measuring stick for the length of the speech activity */
        machineDetectionSpeechThreshold?: number;
        /** machineDetectionTimeout - Number of seconds to wait for machine detection */
        machineDetectionTimeout?: number;
        /** method - TwiML URL method */
        method?: string;
        /** sendDigits - DTMF tones to play when the call is answered */
        sendDigits?: string;
        /** statusCallback - Status callback URL */
        statusCallback?: string;
        /** statusCallbackEvent - Events to call status callback */
        statusCallbackEvent?: NumberEvent[];
        /** statusCallbackMethod - Status callback URL method */
        statusCallbackMethod?: string;
        /** url - TwiML URL */
        url?: string;
    }
    /**
     * Attributes to pass to queue
     */
    export interface QueueAttributes {
        /** method - Action URL method */
        method?: string;
        /** postWorkActivitySid - TaskRouter Activity SID */
        postWorkActivitySid?: string;
        /** reservationSid - TaskRouter Reservation SID */
        reservationSid?: string;
        /** url - Action URL */
        url?: string;
    }
    /**
     * Attributes to pass to sip
     */
    export interface SipAttributes {
        /** amdStatusCallback - The URL we should call to send amd status information to your application */
        amdStatusCallback?: string;
        /** amdStatusCallbackMethod - HTTP Method to use with amd_status_callback */
        amdStatusCallbackMethod?: string;
        /** machineDetection - Enable machine detection or end of greeting detection */
        machineDetection?: string;
        /** machineDetectionSilenceTimeout - Number of milliseconds of initial silence */
        machineDetectionSilenceTimeout?: number;
        /** machineDetectionSpeechEndThreshold - Number of milliseconds of silence after speech activity */
        machineDetectionSpeechEndThreshold?: number;
        /** machineDetectionSpeechThreshold - Number of milliseconds for measuring stick for the length of the speech activity */
        machineDetectionSpeechThreshold?: number;
        /** machineDetectionTimeout - Number of seconds to wait for machine detection */
        machineDetectionTimeout?: number;
        /** method - Action URL method */
        method?: string;
        /** password - SIP Password */
        password?: string;
        /** statusCallback - Status callback URL */
        statusCallback?: string;
        /** statusCallbackEvent - Status callback events */
        statusCallbackEvent?: SipEvent[];
        /** statusCallbackMethod - Status callback URL method */
        statusCallbackMethod?: string;
        /** url - Action URL */
        url?: string;
        /** username - SIP Username */
        username?: string;
    }
    /**
     * Attributes to pass to application
     */
    export interface ApplicationAttributes {
        /** copyParentTo - Copy parent call To field to called application side, otherwise use the application sid as To field */
        copyParentTo?: boolean;
        /** customerId - Identity of the customer calling application */
        customerId?: string;
        /** method - TwiML URL Method */
        method?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackEvent - Events to trigger status callback */
        statusCallbackEvent?: ApplicationEvent[];
        /** statusCallbackMethod - Status Callback URL Method */
        statusCallbackMethod?: string;
        /** url - TwiML URL */
        url?: string;
    }
    /**
     * Attributes to pass to whatsApp
     */
    export interface WhatsAppAttributes {
        /** method - TwiML URL Method */
        method?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackEvent - Events to trigger status callback */
        statusCallbackEvent?: WhatsAppEvent[];
        /** statusCallbackMethod - Status Callback URL Method */
        statusCallbackMethod?: string;
        /** url - TwiML URL */
        url?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to room
     */
    export interface RoomAttributes {
        /** participantIdentity - Participant identity when connecting to the Room */
        participantIdentity?: string;
    }
    /**
     * Attributes to pass to stream
     */
    export interface StreamAttributes {
        /** connectorName - Unique name for Stream Connector */
        connectorName?: string;
        /** name - Friendly name given to the Stream */
        name?: string;
        /** statusCallback - Status Callback URL */
        statusCallback?: string;
        /** statusCallbackMethod - Status Callback URL method */
        statusCallbackMethod?: StreamStatusCallbackMethod;
        /** track - Track to be streamed to remote service */
        track?: StreamTrack;
        /** url - URL of the remote service where the Stream is routed */
        url?: string;
    }
    /**
     * Attributes to pass to virtualAgent
     */
    export interface VirtualAgentAttributes {
        /** connectorName - Defines the conversation profile Dialogflow needs to use */
        connectorName?: string;
        /** language - Language to be used by Dialogflow to transcribe speech */
        language?: string;
        /** sentimentAnalysis - Whether sentiment analysis needs to be enabled or not */
        sentimentAnalysis?: boolean;
        /** statusCallback - URL to post status callbacks from Twilio */
        statusCallback?: string;
        /** statusCallbackMethod - HTTP method to use when requesting the status callback URL */
        statusCallbackMethod?: string;
    }
    /**
     * Attributes to pass to conversation
     */
    export interface ConversationAttributes {
        /** inboundAutocreation - Inbound autocreation */
        inboundAutocreation?: boolean;
        /** inboundTimeout - Inbound timeout */
        inboundTimeout?: number;
        /** method - TwiML URL method */
        method?: string;
        /** record - Record */
        record?: ConversationRecord;
        /** recordingStatusCallback - Recording status callback URL */
        recordingStatusCallback?: string;
        /** recordingStatusCallbackEvent - Recording status callback events */
        recordingStatusCallbackEvent?: ConversationRecordingEvent[];
        /** recordingStatusCallbackMethod - Recording status callback URL method */
        recordingStatusCallbackMethod?: string;
        /** routingAssignmentTimeout - Routing assignment timeout */
        routingAssignmentTimeout?: number;
        /** serviceInstanceSid - Service instance Sid */
        serviceInstanceSid?: string;
        /** statusCallback - Status callback URL */
        statusCallback?: string;
        /** statusCallbackEvent - Events to call status callback URL */
        statusCallbackEvent?: ConversationEvent[];
        /** statusCallbackMethod - Status callback URL method */
        statusCallbackMethod?: string;
        /** trim - Trim */
        trim?: ConversationTrim;
        /** url - TwiML URL */
        url?: string;
    }
    /**
     * Attributes to pass to conversationRelay
     */
    export interface ConversationRelayAttributes {
        /** debug - Multiple debug options to be used for troubleshooting */
        debug?: string;
        /** dtmfDetection - Whether DTMF tones should be detected and reported in speech transcription */
        dtmfDetection?: boolean;
        /** elevenlabsTextNormalization - When using ElevenLabs as TTS provider, this parameter allows you to enable or disable its text normalization feature */
        elevenlabsTextNormalization?: string;
        /** hints - Phrases to help better accuracy in speech recognition of these pharases */
        hints?: string;
        /** intelligenceService - The Conversational Intelligence Service id or unique name to be used for the session */
        intelligenceService?: string;
        /** interruptSensitivity - Set the sensitivity of the interrupt feature for speech. The value can be low, medium, or high */
        interruptSensitivity?: string;
        /** interruptible - Whether and how the input from a caller, such as speaking or DTMF can interrupt the play of text-to-speech */
        interruptible?: string;
        /** language - Language to be used for both text-to-speech and transcription */
        language?: string;
        /** partialPrompts - Whether partial prompts should be reported to WebSocket server before the caller finishes speaking */
        partialPrompts?: boolean;
        /** preemptible - Whether subsequent text-to-speech or play media can interrupt the on-going play of text-to-speech or media */
        preemptible?: boolean;
        /** profanityFilter - Whether profanities should be filtered out of the speech transcription */
        profanityFilter?: boolean;
        /** reportInputDuringAgentSpeech - Whether prompts should be reported to WebSocket server when text-to-speech playing and interrupt is disabled */
        reportInputDuringAgentSpeech?: boolean;
        /** speechModel - Speech model to be used for transcription */
        speechModel?: string;
        /** transcriptionLanguage - Language to be used for transcription */
        transcriptionLanguage?: string;
        /** transcriptionProvider - Provider to be used for transcription */
        transcriptionProvider?: string;
        /** ttsLanguage - Language to be used for text-to-speech */
        ttsLanguage?: string;
        /** ttsProvider - Provider to be used for text-to-speech */
        ttsProvider?: string;
        /** url - URL of the remote service where the session is connected to */
        url?: string;
        /** voice - Voice to be used for text-to-speech */
        voice?: string;
        /** welcomeGreeting - The sentence to be played automatically when the session is connected */
        welcomeGreeting?: string;
        /** welcomeGreetingInterruptible - "Whether and how the input from a caller, such as speaking or DTMF can interrupt the welcome greeting */
        welcomeGreetingInterruptible?: string;
    }
    /**
     * Attributes to pass to assistant
     */
    export interface AssistantAttributes {
        /** debug - Multiple debug options to be used for troubleshooting */
        debug?: string;
        /** dtmfDetection - Whether DTMF tones should be detected and reported in speech transcription */
        dtmfDetection?: boolean;
        /** elevenlabsTextNormalization - When using ElevenLabs as TTS provider, this parameter allows you to enable or disable its text normalization feature */
        elevenlabsTextNormalization?: string;
        /** hints - Phrases to help better accuracy in speech recognition of these pharases */
        hints?: string;
        /** id - The assistant ID of the AI Assistant */
        id?: string;
        /** intelligenceService - The Conversational Intelligence Service id or unique name to be used for the session */
        intelligenceService?: string;
        /** interruptSensitivity - Set the sensitivity of the interrupt feature for speech. The value can be low, medium, or high */
        interruptSensitivity?: string;
        /** interruptible - Whether and how the input from a caller, such as speaking or DTMF can interrupt the play of text-to-speech */
        interruptible?: string;
        /** language - Language to be used for both text-to-speech and transcription */
        language?: string;
        /** partialPrompts - Whether partial prompts should be reported to WebSocket server before the caller finishes speaking */
        partialPrompts?: boolean;
        /** preemptible - Whether subsequent text-to-speech or play media can interrupt the on-going play of text-to-speech or media */
        preemptible?: boolean;
        /** profanityFilter - Whether profanities should be filtered out of the speech transcription */
        profanityFilter?: boolean;
        /** reportInputDuringAgentSpeech - Whether prompts should be reported to WebSocket server when text-to-speech playing and interrupt is disabled */
        reportInputDuringAgentSpeech?: boolean;
        /** speechModel - Speech model to be used for transcription */
        speechModel?: string;
        /** transcriptionLanguage - Language to be used for transcription */
        transcriptionLanguage?: string;
        /** transcriptionProvider - Provider to be used for transcription */
        transcriptionProvider?: string;
        /** ttsLanguage - Language to be used for text-to-speech */
        ttsLanguage?: string;
        /** ttsProvider - Provider to be used for text-to-speech */
        ttsProvider?: string;
        /** voice - Voice to be used for text-to-speech */
        voice?: string;
        /** welcomeGreeting - The sentence to be played automatically when the session is connected */
        welcomeGreeting?: string;
        /** welcomeGreetingInterruptible - "Whether and how the input from a caller, such as speaking or DTMF can interrupt the welcome greeting */
        welcomeGreetingInterruptible?: string;
    }
    /**
     * Attributes to pass to language
     */
    export interface LanguageAttributes {
        /** code - Language code of this language setting is for */
        code?: string;
        /** speechModel - Speech model to be used for transcription of this language */
        speechModel?: string;
        /** transcriptionProvider - Provider to be used for transcription of this language */
        transcriptionProvider?: string;
        /** ttsProvider - Provider to be used for text-to-speech of this language */
        ttsProvider?: string;
        /** voice - Voice to be used for text-to-speech of this language */
        voice?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to language
     */
    export interface LanguageAttributes {
        /** code - Language code of this language setting is for */
        code?: string;
        /** speechModel - Speech model to be used for transcription of this language */
        speechModel?: string;
        /** transcriptionProvider - Provider to be used for transcription of this language */
        transcriptionProvider?: string;
        /** ttsProvider - Provider to be used for text-to-speech of this language */
        ttsProvider?: string;
        /** voice - Voice to be used for text-to-speech of this language */
        voice?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    /**
     * Attributes to pass to config
     */
    export interface ConfigAttributes {
        /** name - The name of the custom config */
        name?: string;
        /** value - The value of the custom config */
        value?: string;
    }
    /**
     * Attributes to pass to parameter
     */
    export interface ParameterAttributes {
        /** name - The name of the custom parameter */
        name?: string;
        /** value - The value of the custom parameter */
        value?: string;
    }
    export class Application extends TwiML {
        application: XMLElement;
        /**
         * <Application> TwiML Noun
         */
        constructor(application: XMLElement);
        /**
         * <ApplicationSid> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param sid - Application sid to dial
         */
        applicationSid(sid: string): VoiceResponse.ApplicationSid;
        applicationSid(attributes: object, sid: string): VoiceResponse.ApplicationSid;
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class ApplicationSid extends TwiML {
        applicationSid: XMLElement;
        /**
         * <ApplicationSid> TwiML Noun
         */
        constructor(applicationSid: XMLElement);
    }
    export class Assistant extends TwiML {
        assistant: XMLElement;
        /**
         * <Assistant> TwiML Noun
         */
        constructor(assistant: XMLElement);
        /**
         * <Language> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        language(attributes?: VoiceResponse.LanguageAttributes): VoiceResponse.Language;
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class Autopilot extends TwiML {
        autopilot: XMLElement;
        /**
         * <Autopilot> TwiML Noun
         */
        constructor(autopilot: XMLElement);
    }
    export class Client extends TwiML {
        client: XMLElement;
        /**
         * <Client> TwiML Noun
         */
        constructor(client: XMLElement);
        /**
         * <Identity> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param clientIdentity - Identity of the client to dial
         */
        identity(clientIdentity: string): VoiceResponse.Identity;
        identity(attributes: object, clientIdentity: string): VoiceResponse.Identity;
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class Conference extends TwiML {
        conference: XMLElement;
        /**
         * <Conference> TwiML Noun
         */
        constructor(conference: XMLElement);
    }
    export class Config extends TwiML {
        config: XMLElement;
        /**
         * <Config> TwiML Noun
         */
        constructor(config: XMLElement);
    }
    export class Connect extends TwiML {
        connect: XMLElement;
        /**
         * <Connect> TwiML Verb
         */
        constructor(connect: XMLElement);
        /**
         * <Assistant> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        assistant(attributes?: VoiceResponse.AssistantAttributes): VoiceResponse.Assistant;
        /**
         * <Autopilot> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param name - Autopilot assistant sid or unique name
         */
        autopilot(name: string): VoiceResponse.Autopilot;
        autopilot(attributes: object, name: string): VoiceResponse.Autopilot;
        /**
         * <Conversation> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        conversation(attributes?: VoiceResponse.ConversationAttributes): VoiceResponse.Conversation;
        /**
         * <ConversationRelay> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        conversationRelay(attributes?: VoiceResponse.ConversationRelayAttributes): VoiceResponse.ConversationRelay;
        /**
         * <Room> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param name - Room name
         */
        room(name: string): VoiceResponse.Room;
        room(attributes: VoiceResponse.RoomAttributes, name: string): VoiceResponse.Room;
        /**
         * <Stream> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        stream(attributes?: VoiceResponse.StreamAttributes): VoiceResponse.Stream;
        /**
         * <VirtualAgent> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        virtualAgent(attributes?: VoiceResponse.VirtualAgentAttributes): VoiceResponse.VirtualAgent;
    }
    export class Conversation extends TwiML {
        conversation: XMLElement;
        /**
         * <Conversation> TwiML Noun
         */
        constructor(conversation: XMLElement);
    }
    export class ConversationRelay extends TwiML {
        conversationRelay: XMLElement;
        /**
         * <ConversationRelay> TwiML Noun
         */
        constructor(conversationRelay: XMLElement);
        /**
         * <Language> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        language(attributes?: VoiceResponse.LanguageAttributes): VoiceResponse.Language;
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class Dial extends TwiML {
        dial: XMLElement;
        /**
         * <Dial> TwiML Verb
         */
        constructor(dial: XMLElement);
        /**
         * <Application> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param applicationSid - Application sid
         */
        application(applicationSid?: string): VoiceResponse.Application;
        application(attributes?: VoiceResponse.ApplicationAttributes, applicationSid?: string): VoiceResponse.Application;
        /**
         * <Client> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param identity - Client identity
         */
        client(identity?: string): VoiceResponse.Client;
        client(attributes?: VoiceResponse.ClientAttributes, identity?: string): VoiceResponse.Client;
        /**
         * <Conference> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param name - Conference name
         */
        conference(name: string): VoiceResponse.Conference;
        conference(attributes: VoiceResponse.ConferenceAttributes, name: string): VoiceResponse.Conference;
        /**
         * <Number> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param phoneNumber - Phone Number to dial
         */
        number(phoneNumber: string): VoiceResponse.Number;
        number(attributes: VoiceResponse.NumberAttributes, phoneNumber: string): VoiceResponse.Number;
        /**
         * <Queue> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param name - Queue name
         */
        queue(name: string): VoiceResponse.Queue;
        queue(attributes: VoiceResponse.QueueAttributes, name: string): VoiceResponse.Queue;
        /**
         * <Sim> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param simSid - SIM SID
         */
        sim(simSid: string): VoiceResponse.Sim;
        sim(attributes: object, simSid: string): VoiceResponse.Sim;
        /**
         * <Sip> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param sipUrl - SIP URL
         */
        sip(sipUrl: string): VoiceResponse.Sip;
        sip(attributes: VoiceResponse.SipAttributes, sipUrl: string): VoiceResponse.Sip;
        /**
         * <WhatsApp> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param phoneNumber - WhatsApp Phone Number to dial
         */
        whatsApp(phoneNumber: string): VoiceResponse.WhatsApp;
        whatsApp(attributes: VoiceResponse.WhatsAppAttributes, phoneNumber: string): VoiceResponse.WhatsApp;
    }
    export class Echo extends TwiML {
        echo: XMLElement;
        /**
         * <Echo> TwiML Verb
         */
        constructor(echo: XMLElement);
    }
    export class Enqueue extends TwiML {
        enqueue: XMLElement;
        /**
         * <Enqueue> TwiML Noun
         */
        constructor(enqueue: XMLElement);
        /**
         * <Task> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param body - TaskRouter task attributes
         */
        task(body: string): VoiceResponse.Task;
        task(attributes: VoiceResponse.TaskAttributes, body: string): VoiceResponse.Task;
    }
    export class Gather extends TwiML {
        gather: XMLElement;
        /**
         * <Gather> TwiML Verb
         */
        constructor(gather: XMLElement);
        /**
         * <Pause> TwiML Verb
         *
         * @param attributes - TwiML attributes
         */
        pause(attributes?: VoiceResponse.PauseAttributes): VoiceResponse.Pause;
        /**
         * <Play> TwiML Verb
         *
         * @param attributes - TwiML attributes
         * @param url - Media URL
         */
        play(url?: string): VoiceResponse.Play;
        play(attributes?: VoiceResponse.PlayAttributes, url?: string): VoiceResponse.Play;
        /**
         * <Say> TwiML Verb
         *
         * @param attributes - TwiML attributes
         * @param message - Message to say
         */
        say(message: string): VoiceResponse.Say;
        say(attributes: VoiceResponse.SayAttributes, message: string): VoiceResponse.Say;
    }
    export class Hangup extends TwiML {
        hangup: XMLElement;
        /**
         * <Hangup> TwiML Verb
         */
        constructor(hangup: XMLElement);
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class Identity extends TwiML {
        identity: XMLElement;
        /**
         * <Identity> TwiML Noun
         */
        constructor(identity: XMLElement);
    }
    export class Language extends TwiML {
        language: XMLElement;
        /**
         * <Language> TwiML Noun
         */
        constructor(language: XMLElement);
    }
    export class Leave extends TwiML {
        leave: XMLElement;
        /**
         * <Leave> TwiML Verb
         */
        constructor(leave: XMLElement);
    }
    export class Number extends TwiML {
        number: XMLElement;
        /**
         * <Number> TwiML Noun
         */
        constructor(number: XMLElement);
    }
    export class Parameter extends TwiML {
        parameter: XMLElement;
        /**
         * <Parameter> TwiML Noun
         */
        constructor(parameter: XMLElement);
    }
    export class Pause extends TwiML {
        pause: XMLElement;
        /**
         * <Pause> TwiML Verb
         */
        constructor(pause: XMLElement);
    }
    export class Pay extends TwiML {
        pay: XMLElement;
        /**
         * <Pay> Twiml Verb
         */
        constructor(pay: XMLElement);
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
        /**
         * <Prompt> Twiml Verb
         *
         * @param attributes - TwiML attributes
         */
        prompt(attributes?: VoiceResponse.PromptAttributes): VoiceResponse.Prompt;
    }
    export class Play extends TwiML {
        play: XMLElement;
        /**
         * <Play> TwiML Verb
         */
        constructor(play: XMLElement);
    }
    export class Prompt extends TwiML {
        prompt: XMLElement;
        /**
         * <Prompt> Twiml Verb
         */
        constructor(prompt: XMLElement);
        /**
         * <Pause> TwiML Verb
         *
         * @param attributes - TwiML attributes
         */
        pause(attributes?: VoiceResponse.PauseAttributes): VoiceResponse.Pause;
        /**
         * <Play> TwiML Verb
         *
         * @param attributes - TwiML attributes
         * @param url - Media URL
         */
        play(url?: string): VoiceResponse.Play;
        play(attributes?: VoiceResponse.PlayAttributes, url?: string): VoiceResponse.Play;
        /**
         * <Say> TwiML Verb
         *
         * @param attributes - TwiML attributes
         * @param message - Message to say
         */
        say(message: string): VoiceResponse.Say;
        say(attributes: VoiceResponse.SayAttributes, message: string): VoiceResponse.Say;
    }
    export class Queue extends TwiML {
        queue: XMLElement;
        /**
         * <Queue> TwiML Noun
         */
        constructor(queue: XMLElement);
    }
    export class Record extends TwiML {
        record: XMLElement;
        /**
         * <Record> TwiML Verb
         */
        constructor(record: XMLElement);
    }
    export class Redirect extends TwiML {
        redirect: XMLElement;
        /**
         * <Redirect> TwiML Verb
         */
        constructor(redirect: XMLElement);
    }
    export class Refer extends TwiML {
        refer: XMLElement;
        /**
         * <Refer> TwiML Verb
         */
        constructor(refer: XMLElement);
        /**
         * <Sip> TwiML Noun used in <Refer>
         *
         * @param attributes - TwiML attributes
         * @param sipUrl - SIP URL
         */
        sip(sipUrl: string): VoiceResponse.ReferSip;
        sip(attributes: object, sipUrl: string): VoiceResponse.ReferSip;
    }
    export class ReferSip extends TwiML {
        referSip: XMLElement;
        /**
         * <Sip> TwiML Noun used in <Refer>
         */
        constructor(referSip: XMLElement);
    }
    export class Reject extends TwiML {
        reject: XMLElement;
        /**
         * <Reject> TwiML Verb
         */
        constructor(reject: XMLElement);
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class Room extends TwiML {
        room: XMLElement;
        /**
         * <Room> TwiML Noun
         */
        constructor(room: XMLElement);
    }
    export class Say extends TwiML {
        say: XMLElement;
        /**
         * <Say> TwiML Verb
         */
        constructor(say: XMLElement);
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes?: VoiceResponse.SsmlBreakAttributes): VoiceResponse.SsmlBreak;
        /**
         * Emphasizing Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to emphasize
         */
        emphasis(words: string): VoiceResponse.SsmlEmphasis;
        emphasis(attributes: VoiceResponse.SsmlEmphasisAttributes, words: string): VoiceResponse.SsmlEmphasis;
        /**
         * Specifying Another Language for Specific Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        lang(words: string): VoiceResponse.SsmlLang;
        lang(attributes: VoiceResponse.SsmlLangAttributes, words: string): VoiceResponse.SsmlLang;
        /**
         * Adding a Pause Between Paragraphs in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        p(words: string): VoiceResponse.SsmlP;
        p(attributes: object, words: string): VoiceResponse.SsmlP;
        /**
         * Using Phonetic Pronunciation in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        phoneme(words: string): VoiceResponse.SsmlPhoneme;
        phoneme(attributes: VoiceResponse.SsmlPhonemeAttributes, words: string): VoiceResponse.SsmlPhoneme;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        prosody(words: string): VoiceResponse.SsmlProsody;
        prosody(attributes: VoiceResponse.SsmlProsodyAttributes, words: string): VoiceResponse.SsmlProsody;
        /**
         * Adding A Pause Between Sentences in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        s(words: string): VoiceResponse.SsmlS;
        s(attributes: object, words: string): VoiceResponse.SsmlS;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be interpreted
         */
        sayAs(words: string): VoiceResponse.SsmlSayAs;
        sayAs(attributes: VoiceResponse.SsmlSayAsAttributes, words: string): VoiceResponse.SsmlSayAs;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be substituted
         */
        sub(words: string): VoiceResponse.SsmlSub;
        sub(attributes: VoiceResponse.SsmlSubAttributes, words: string): VoiceResponse.SsmlSub;
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        w(words: string): VoiceResponse.SsmlW;
        w(attributes: VoiceResponse.SsmlWAttributes, words: string): VoiceResponse.SsmlW;
    }
    export class Sim extends TwiML {
        sim: XMLElement;
        /**
         * <Sim> TwiML Noun
         */
        constructor(sim: XMLElement);
    }
    export class Sip extends TwiML {
        sip: XMLElement;
        /**
         * <Sip> TwiML Noun
         */
        constructor(sip: XMLElement);
    }
    export class Siprec extends TwiML {
        siprec: XMLElement;
        /**
         * <Siprec> TwiML Noun
         */
        constructor(siprec: XMLElement);
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class Sms extends TwiML {
        sms: XMLElement;
        /**
         * <Sms> TwiML Noun
         */
        constructor(sms: XMLElement);
    }
    export class SsmlBreak extends TwiML {
        ssmlBreak: XMLElement;
        /**
         * Adding a Pause in <Say>
         */
        constructor(ssmlBreak: XMLElement);
    }
    export class SsmlEmphasis extends TwiML {
        ssmlEmphasis: XMLElement;
        /**
         * Emphasizing Words in <Say>
         */
        constructor(ssmlEmphasis: XMLElement);
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes?: VoiceResponse.SsmlBreakAttributes): VoiceResponse.SsmlBreak;
        /**
         * Emphasizing Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to emphasize
         */
        emphasis(words: string): VoiceResponse.SsmlEmphasis;
        emphasis(attributes: VoiceResponse.SsmlEmphasisAttributes, words: string): VoiceResponse.SsmlEmphasis;
        /**
         * Specifying Another Language for Specific Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        lang(words: string): VoiceResponse.SsmlLang;
        lang(attributes: VoiceResponse.SsmlLangAttributes, words: string): VoiceResponse.SsmlLang;
        /**
         * Using Phonetic Pronunciation in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        phoneme(words: string): VoiceResponse.SsmlPhoneme;
        phoneme(attributes: VoiceResponse.SsmlPhonemeAttributes, words: string): VoiceResponse.SsmlPhoneme;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        prosody(words: string): VoiceResponse.SsmlProsody;
        prosody(attributes: VoiceResponse.SsmlProsodyAttributes, words: string): VoiceResponse.SsmlProsody;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be interpreted
         */
        sayAs(words: string): VoiceResponse.SsmlSayAs;
        sayAs(attributes: VoiceResponse.SsmlSayAsAttributes, words: string): VoiceResponse.SsmlSayAs;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be substituted
         */
        sub(words: string): VoiceResponse.SsmlSub;
        sub(attributes: VoiceResponse.SsmlSubAttributes, words: string): VoiceResponse.SsmlSub;
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        w(words: string): VoiceResponse.SsmlW;
        w(attributes: VoiceResponse.SsmlWAttributes, words: string): VoiceResponse.SsmlW;
    }
    export class SsmlLang extends TwiML {
        ssmlLang: XMLElement;
        /**
         * Specifying Another Language for Specific Words in <Say>
         */
        constructor(ssmlLang: XMLElement);
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes?: VoiceResponse.SsmlBreakAttributes): VoiceResponse.SsmlBreak;
        /**
         * Emphasizing Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to emphasize
         */
        emphasis(words: string): VoiceResponse.SsmlEmphasis;
        emphasis(attributes: VoiceResponse.SsmlEmphasisAttributes, words: string): VoiceResponse.SsmlEmphasis;
        /**
         * Specifying Another Language for Specific Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        lang(words: string): VoiceResponse.SsmlLang;
        lang(attributes: VoiceResponse.SsmlLangAttributes, words: string): VoiceResponse.SsmlLang;
        /**
         * Adding a Pause Between Paragraphs in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        p(words: string): VoiceResponse.SsmlP;
        p(attributes: object, words: string): VoiceResponse.SsmlP;
        /**
         * Using Phonetic Pronunciation in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        phoneme(words: string): VoiceResponse.SsmlPhoneme;
        phoneme(attributes: VoiceResponse.SsmlPhonemeAttributes, words: string): VoiceResponse.SsmlPhoneme;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        prosody(words: string): VoiceResponse.SsmlProsody;
        prosody(attributes: VoiceResponse.SsmlProsodyAttributes, words: string): VoiceResponse.SsmlProsody;
        /**
         * Adding A Pause Between Sentences in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        s(words: string): VoiceResponse.SsmlS;
        s(attributes: object, words: string): VoiceResponse.SsmlS;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be interpreted
         */
        sayAs(words: string): VoiceResponse.SsmlSayAs;
        sayAs(attributes: VoiceResponse.SsmlSayAsAttributes, words: string): VoiceResponse.SsmlSayAs;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be substituted
         */
        sub(words: string): VoiceResponse.SsmlSub;
        sub(attributes: VoiceResponse.SsmlSubAttributes, words: string): VoiceResponse.SsmlSub;
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        w(words: string): VoiceResponse.SsmlW;
        w(attributes: VoiceResponse.SsmlWAttributes, words: string): VoiceResponse.SsmlW;
    }
    export class SsmlP extends TwiML {
        ssmlP: XMLElement;
        /**
         * Adding a Pause Between Paragraphs in <Say>
         */
        constructor(ssmlP: XMLElement);
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes?: VoiceResponse.SsmlBreakAttributes): VoiceResponse.SsmlBreak;
        /**
         * Emphasizing Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to emphasize
         */
        emphasis(words: string): VoiceResponse.SsmlEmphasis;
        emphasis(attributes: VoiceResponse.SsmlEmphasisAttributes, words: string): VoiceResponse.SsmlEmphasis;
        /**
         * Specifying Another Language for Specific Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        lang(words: string): VoiceResponse.SsmlLang;
        lang(attributes: VoiceResponse.SsmlLangAttributes, words: string): VoiceResponse.SsmlLang;
        /**
         * Using Phonetic Pronunciation in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        phoneme(words: string): VoiceResponse.SsmlPhoneme;
        phoneme(attributes: VoiceResponse.SsmlPhonemeAttributes, words: string): VoiceResponse.SsmlPhoneme;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        prosody(words: string): VoiceResponse.SsmlProsody;
        prosody(attributes: VoiceResponse.SsmlProsodyAttributes, words: string): VoiceResponse.SsmlProsody;
        /**
         * Adding A Pause Between Sentences in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        s(words: string): VoiceResponse.SsmlS;
        s(attributes: object, words: string): VoiceResponse.SsmlS;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be interpreted
         */
        sayAs(words: string): VoiceResponse.SsmlSayAs;
        sayAs(attributes: VoiceResponse.SsmlSayAsAttributes, words: string): VoiceResponse.SsmlSayAs;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be substituted
         */
        sub(words: string): VoiceResponse.SsmlSub;
        sub(attributes: VoiceResponse.SsmlSubAttributes, words: string): VoiceResponse.SsmlSub;
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        w(words: string): VoiceResponse.SsmlW;
        w(attributes: VoiceResponse.SsmlWAttributes, words: string): VoiceResponse.SsmlW;
    }
    export class SsmlPhoneme extends TwiML {
        ssmlPhoneme: XMLElement;
        /**
         * Using Phonetic Pronunciation in <Say>
         */
        constructor(ssmlPhoneme: XMLElement);
    }
    export class SsmlProsody extends TwiML {
        ssmlProsody: XMLElement;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         */
        constructor(ssmlProsody: XMLElement);
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes?: VoiceResponse.SsmlBreakAttributes): VoiceResponse.SsmlBreak;
        /**
         * Emphasizing Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to emphasize
         */
        emphasis(words: string): VoiceResponse.SsmlEmphasis;
        emphasis(attributes: VoiceResponse.SsmlEmphasisAttributes, words: string): VoiceResponse.SsmlEmphasis;
        /**
         * Specifying Another Language for Specific Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        lang(words: string): VoiceResponse.SsmlLang;
        lang(attributes: VoiceResponse.SsmlLangAttributes, words: string): VoiceResponse.SsmlLang;
        /**
         * Adding a Pause Between Paragraphs in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        p(words: string): VoiceResponse.SsmlP;
        p(attributes: object, words: string): VoiceResponse.SsmlP;
        /**
         * Using Phonetic Pronunciation in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        phoneme(words: string): VoiceResponse.SsmlPhoneme;
        phoneme(attributes: VoiceResponse.SsmlPhonemeAttributes, words: string): VoiceResponse.SsmlPhoneme;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        prosody(words: string): VoiceResponse.SsmlProsody;
        prosody(attributes: VoiceResponse.SsmlProsodyAttributes, words: string): VoiceResponse.SsmlProsody;
        /**
         * Adding A Pause Between Sentences in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        s(words: string): VoiceResponse.SsmlS;
        s(attributes: object, words: string): VoiceResponse.SsmlS;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be interpreted
         */
        sayAs(words: string): VoiceResponse.SsmlSayAs;
        sayAs(attributes: VoiceResponse.SsmlSayAsAttributes, words: string): VoiceResponse.SsmlSayAs;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be substituted
         */
        sub(words: string): VoiceResponse.SsmlSub;
        sub(attributes: VoiceResponse.SsmlSubAttributes, words: string): VoiceResponse.SsmlSub;
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        w(words: string): VoiceResponse.SsmlW;
        w(attributes: VoiceResponse.SsmlWAttributes, words: string): VoiceResponse.SsmlW;
    }
    export class SsmlS extends TwiML {
        ssmlS: XMLElement;
        /**
         * Adding A Pause Between Sentences in <Say>
         */
        constructor(ssmlS: XMLElement);
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes?: VoiceResponse.SsmlBreakAttributes): VoiceResponse.SsmlBreak;
        /**
         * Emphasizing Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to emphasize
         */
        emphasis(words: string): VoiceResponse.SsmlEmphasis;
        emphasis(attributes: VoiceResponse.SsmlEmphasisAttributes, words: string): VoiceResponse.SsmlEmphasis;
        /**
         * Specifying Another Language for Specific Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        lang(words: string): VoiceResponse.SsmlLang;
        lang(attributes: VoiceResponse.SsmlLangAttributes, words: string): VoiceResponse.SsmlLang;
        /**
         * Using Phonetic Pronunciation in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        phoneme(words: string): VoiceResponse.SsmlPhoneme;
        phoneme(attributes: VoiceResponse.SsmlPhonemeAttributes, words: string): VoiceResponse.SsmlPhoneme;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        prosody(words: string): VoiceResponse.SsmlProsody;
        prosody(attributes: VoiceResponse.SsmlProsodyAttributes, words: string): VoiceResponse.SsmlProsody;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be interpreted
         */
        sayAs(words: string): VoiceResponse.SsmlSayAs;
        sayAs(attributes: VoiceResponse.SsmlSayAsAttributes, words: string): VoiceResponse.SsmlSayAs;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be substituted
         */
        sub(words: string): VoiceResponse.SsmlSub;
        sub(attributes: VoiceResponse.SsmlSubAttributes, words: string): VoiceResponse.SsmlSub;
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        w(words: string): VoiceResponse.SsmlW;
        w(attributes: VoiceResponse.SsmlWAttributes, words: string): VoiceResponse.SsmlW;
    }
    export class SsmlSayAs extends TwiML {
        ssmlSayAs: XMLElement;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         */
        constructor(ssmlSayAs: XMLElement);
    }
    export class SsmlSub extends TwiML {
        ssmlSub: XMLElement;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         */
        constructor(ssmlSub: XMLElement);
    }
    export class SsmlW extends TwiML {
        ssmlW: XMLElement;
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         */
        constructor(ssmlW: XMLElement);
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes?: VoiceResponse.SsmlBreakAttributes): VoiceResponse.SsmlBreak;
        /**
         * Emphasizing Words in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to emphasize
         */
        emphasis(words: string): VoiceResponse.SsmlEmphasis;
        emphasis(attributes: VoiceResponse.SsmlEmphasisAttributes, words: string): VoiceResponse.SsmlEmphasis;
        /**
         * Using Phonetic Pronunciation in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        phoneme(words: string): VoiceResponse.SsmlPhoneme;
        phoneme(attributes: VoiceResponse.SsmlPhonemeAttributes, words: string): VoiceResponse.SsmlPhoneme;
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to speak
         */
        prosody(words: string): VoiceResponse.SsmlProsody;
        prosody(attributes: VoiceResponse.SsmlProsodyAttributes, words: string): VoiceResponse.SsmlProsody;
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be interpreted
         */
        sayAs(words: string): VoiceResponse.SsmlSayAs;
        sayAs(attributes: VoiceResponse.SsmlSayAsAttributes, words: string): VoiceResponse.SsmlSayAs;
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         *
         * @param attributes - TwiML attributes
         * @param words - Words to be substituted
         */
        sub(words: string): VoiceResponse.SsmlSub;
        sub(attributes: VoiceResponse.SsmlSubAttributes, words: string): VoiceResponse.SsmlSub;
    }
    export class Start extends TwiML {
        start: XMLElement;
        /**
         * <Start> TwiML Verb
         */
        constructor(start: XMLElement);
        /**
         * <Siprec> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        siprec(attributes?: VoiceResponse.SiprecAttributes): VoiceResponse.Siprec;
        /**
         * <Stream> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        stream(attributes?: VoiceResponse.StreamAttributes): VoiceResponse.Stream;
        /**
         * <Transcription> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        transcription(attributes?: VoiceResponse.TranscriptionAttributes): VoiceResponse.Transcription;
    }
    export class Stop extends TwiML {
        stop: XMLElement;
        /**
         * <Stop> TwiML Verb
         */
        constructor(stop: XMLElement);
        /**
         * <Siprec> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        siprec(attributes?: VoiceResponse.SiprecAttributes): VoiceResponse.Siprec;
        /**
         * <Stream> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        stream(attributes?: VoiceResponse.StreamAttributes): VoiceResponse.Stream;
        /**
         * <Transcription> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        transcription(attributes?: VoiceResponse.TranscriptionAttributes): VoiceResponse.Transcription;
    }
    export class Stream extends TwiML {
        stream: XMLElement;
        /**
         * <Stream> TwiML Noun
         */
        constructor(stream: XMLElement);
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class Task extends TwiML {
        task: XMLElement;
        /**
         * <Task> TwiML Noun
         */
        constructor(task: XMLElement);
    }
    export class Transcription extends TwiML {
        transcription: XMLElement;
        /**
         * <Transcription> TwiML Noun
         */
        constructor(transcription: XMLElement);
        /**
         * <Config> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        config(attributes?: VoiceResponse.ConfigAttributes): VoiceResponse.Config;
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class VirtualAgent extends TwiML {
        virtualAgent: XMLElement;
        /**
         * <VirtualAgent> TwiML Noun
         */
        constructor(virtualAgent: XMLElement);
        /**
         * <Config> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        config(attributes?: VoiceResponse.ConfigAttributes): VoiceResponse.Config;
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes?: VoiceResponse.ParameterAttributes): VoiceResponse.Parameter;
    }
    export class WhatsApp extends TwiML {
        whatsApp: XMLElement;
        /**
         * <WhatsApp> TwiML Noun
         */
        constructor(whatsApp: XMLElement);
    }
    export {};
}
export = VoiceResponse;
