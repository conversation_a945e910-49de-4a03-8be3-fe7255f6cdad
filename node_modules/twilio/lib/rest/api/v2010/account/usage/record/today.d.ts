/// <reference types="node" />
import { inspect, InspectOptions } from "util";
import Page, { TwilioResponsePayload } from "../../../../../../base/Page";
import Response from "../../../../../../http/response";
import V2010 from "../../../../V2010";
/**
 * The category of usage. For more information, see [Usage Categories](https://www.twilio.com/docs/usage/api/usage-record#usage-categories).
 */
export type TodayCategory = "a2p-10dlc-registrationfees-brandregistration" | "a2p-10dlc-registrationfees-bv" | "a2p-10dlc-registrationfees-campaigncharges" | "a2p-10dlc-registrationfees-campaignregistration" | "a2p-10dlc-registrationfees-campaignvetting" | "a2p-10dlc-registrationfees-monthly" | "a2p-10dlc-registrationfees-onetime" | "a2p-registration-fees" | "account-security" | "agent-conference" | "agent-copilot" | "agent-copilot-messages" | "agent-copilot-participant-minutes" | "ai-assistants" | "ai-assistants-voice" | "amazon-polly" | "answering-machine-detection" | "assets" | "audience-minutes" | "audience-minutes-audio" | "authy-authentications" | "authy-calls-outbound" | "authy-email-authentications" | "authy-monthly-fees" | "authy-outbound-email" | "authy-phone-intelligence" | "authy-phone-verifications" | "authy-sms-outbound" | "authy-verify-email-verifications" | "authy-verify-outbound-email" | "autopilot" | "autopilot-home-assistants" | "autopilot-messaging" | "autopilot-other" | "autopilot-voice" | "basic-peer-to-peer-rooms-participant-minutes" | "branded-calling" | "bundle-sms-bucket" | "bundle-subscription-fees" | "call-forwarding-lookups" | "call-progess-events" | "calleridlookups" | "calls" | "calls-client" | "calls-emergency" | "calls-globalconference" | "calls-inbound" | "calls-inbound-local" | "calls-inbound-mobile" | "calls-inbound-tollfree" | "calls-inbound-tollfree-local" | "calls-inbound-tollfree-mobile" | "calls-media-stream-minutes" | "calls-outbound" | "calls-pay-verb-transactions" | "calls-recordings" | "calls-sip" | "calls-sip-inbound" | "calls-sip-outbound" | "calls-text-to-speech" | "calls-transfers" | "carrier-lookups" | "category" | "channels" | "channels-messaging" | "channels-messaging-inbound" | "channels-messaging-outbound" | "channels-whatsapp" | "channels-whatsapp-conversation-authentication" | "channels-whatsapp-conversation-free" | "channels-whatsapp-conversation-marketing" | "channels-whatsapp-conversation-service" | "channels-whatsapp-conversation-utility" | "channels-whatsapp-inbound" | "channels-whatsapp-outbound" | "chat-virtual-agent" | "conversation-relay" | "conversations" | "conversations-api-requests" | "conversations-conversation-events" | "conversations-endpoint-connectivity" | "conversations-events" | "conversations-participant-events" | "conversations-participants" | "cps" | "credit-transfer" | "email" | "emerging-tech" | "engagement-suite-packaged-plans" | "enhanced-line-type-lookups" | "enterprise" | "events" | "experiment-france-sms" | "experiment-india-sms" | "experiment-uk-sms" | "failed-message-processing-fee" | "flex" | "flex-active-user-hours" | "flex-concurrent-users" | "flex-conversational-insights" | "flex-conversational-insights-messages" | "flex-conversational-insights-voice-minutes" | "flex-email-usage" | "flex-messaging-usage" | "flex-partner-spinsci" | "flex-partner-xcelerate" | "flex-reseller-ecosystem" | "flex-unique-user" | "flex-usage" | "flex-users" | "flex-voice-minute" | "flex-ytica" | "fraud-lookups" | "frontline" | "frontline-users" | "functions" | "generic-pay-transactions" | "group-rooms" | "group-rooms-data-track" | "group-rooms-encrypted-media-recorded" | "group-rooms-media-downloaded" | "group-rooms-media-recorded" | "group-rooms-media-routed" | "group-rooms-media-stored" | "group-rooms-participant-minutes" | "group-rooms-recorded-minutes" | "ip-messaging" | "ip-messaging-commands" | "ip-messaging-data-storage" | "ip-messaging-data-transfer" | "ip-messaging-endpoint-connectivity" | "ivr-virtual-agent-custom-voices" | "ivr-virtual-agent-genai" | "line-status-lookups" | "live-activity-lookups" | "lookup-bucket-adjustment" | "lookup-identity-match" | "lookups" | "marketplace" | "marketplace-algorithmia-named-entity-recognition" | "marketplace-cadence-transcription" | "marketplace-cadence-translation" | "marketplace-capio-speech-to-text" | "marketplace-convriza-ababa" | "marketplace-deepgram-phrase-detector" | "marketplace-deepgram-transcription" | "marketplace-deepgram-transcription-base" | "marketplace-deepgram-transscription-enhanced" | "marketplace-digital-segment-business-info" | "marketplace-facebook-offline-conversions" | "marketplace-google-speech-to-text" | "marketplace-ibm-watson-message-insights" | "marketplace-ibm-watson-message-sentiment" | "marketplace-ibm-watson-recording-analysis" | "marketplace-ibm-watson-tone-analyzer" | "marketplace-icehook-systems-scout" | "marketplace-infogroup-dataaxle-bizinfo" | "marketplace-keen-io-contact-center-analytics" | "marketplace-marchex-cleancall" | "marketplace-marchex-recording-analysis" | "marketplace-marchex-sentiment-analysis-for-sms" | "marketplace-marketplace-nextcaller-social-id" | "marketplace-mobile-commons-opt-out-classifier" | "marketplace-nexiwave-voicemail-to-text" | "marketplace-nextcaller-advanced-caller-identification" | "marketplace-nomorobo-spam-score" | "marketplace-pay-addons" | "marketplace-pay-addons-basecommerce-pay-connector" | "marketplace-pay-addons-braintree-pay-connector" | "marketplace-pay-addons-cardconnect-pay-connector" | "marketplace-pay-addons-chase-pay-connector" | "marketplace-pay-addons-shuttle-pay-connector" | "marketplace-pay-addons-stripe-pay-connector" | "marketplace-payfone-tcpa-compliance" | "marketplace-poly-ai-connector" | "marketplace-realphonevalidation" | "marketplace-remeeting-automatic-speech-recognition" | "marketplace-spoke-phone-license-pro" | "marketplace-spoke-phone-license-standard" | "marketplace-tcpa-defense-solutions-blacklist-feed" | "marketplace-telo-opencnam" | "marketplace-trestle-solutions-caller-identification" | "marketplace-truecnam-true-spam" | "marketplace-twilio-caller-name-lookup-us" | "marketplace-twilio-carrier-information-lookup" | "marketplace-voicebase-pci" | "marketplace-voicebase-transcription" | "marketplace-voicebase-transcription-custom-vocabulary" | "marketplace-web-purify-profanity-filter" | "marketplace-whitepages-pro-caller-identification" | "marketplace-whitepages-pro-phone-intelligence" | "marketplace-whitepages-pro-phone-reputation" | "marketplace-wolfarm-spoken-results" | "marketplace-wolfram-short-answer" | "marketplace-ytica-contact-center-reporting-analytics" | "marketplay-pay-addons-shuttle-pay-connector" | "media-composer-minutes" | "mediastorage" | "min-spend-adjustments" | "mms" | "mms-inbound" | "mms-inbound-longcode" | "mms-inbound-shortcode" | "mms-inbound-toll-free" | "mms-messages-carrierfees" | "mms-outbound" | "mms-outbound-longcode" | "mms-outbound-shortcode" | "mms-outbound-tollfree" | "monitor" | "monitor-reads" | "monitor-storage" | "monitor-writes" | "notify" | "notify-actions-attempts" | "notify-channels" | "number-format-lookups" | "pchat" | "pchat-actions" | "pchat-aps" | "pchat-conv-med-storage" | "pchat-messages" | "pchat-notifications" | "pchat-reads" | "pchat-users" | "peer-to-peer-rooms-participant-minutes" | "pfax" | "pfax-minutes" | "pfax-minutes-inbound" | "pfax-minutes-outbound" | "pfax-pages" | "phone-quality-score-lookups" | "phonenumbers" | "phonenumbers-cps" | "phonenumbers-emergency" | "phonenumbers-local" | "phonenumbers-mobile" | "phonenumbers-porting" | "phonenumbers-setups" | "phonenumbers-tollfree" | "premiumsupport" | "premiumsupport-percentage-spend" | "programmablevoice-platform" | "programmablevoiceconn-clientsdk" | "programmablevoiceconn-clientsdk-inbound" | "programmablevoiceconn-clientsdk-outbound" | "programmablevoiceconn-onnet" | "programmablevoiceconn-onnet-inbound" | "programmablevoiceconn-onnet-outbound" | "programmablevoiceconn-sip" | "programmablevoiceconn-sip-inbound" | "programmablevoiceconn-sip-outbound" | "programmablevoiceconnectivity" | "proxy" | "proxy-active-sessions" | "proxy-bucket-adjustment" | "proxy-licenses" | "pstnconnectivity" | "pstnconnectivity-inbound" | "pstnconnectivity-outbound" | "pv" | "pv-basic-rooms" | "pv-composition-media-downloaded" | "pv-composition-media-encrypted" | "pv-composition-media-stored" | "pv-composition-minutes" | "pv-recording-compositions" | "pv-room-participants" | "pv-room-participants-au1" | "pv-room-participants-br1" | "pv-room-participants-ie1" | "pv-room-participants-jp1" | "pv-room-participants-sg1" | "pv-room-participants-us1" | "pv-room-participants-us2" | "pv-rooms" | "pv-sip-endpoint-registrations" | "rcs-messages" | "reassigned-number" | "recordings" | "recordingstorage" | "shortcodes" | "shortcodes-customerowned" | "shortcodes-mms-enablement" | "shortcodes-mps" | "shortcodes-random" | "shortcodes-setup-fees" | "shortcodes-uk" | "shortcodes-vanity" | "sim-swap-lookups" | "sip-secure-media" | "small-group-rooms" | "small-group-rooms-data-track" | "small-group-rooms-participant-minutes" | "sms" | "sms-inbound" | "sms-inbound-longcode" | "sms-inbound-shortcode" | "sms-inbound-tollfree" | "sms-messages-carrierfees" | "sms-messages-features" | "sms-messages-features-engagement-suite" | "sms-messages-features-message-redaction" | "sms-messages-features-senderid" | "sms-mps" | "sms-mps-shortcode" | "sms-mps-tollfree" | "sms-mps-tollfree-setup" | "sms-national-regulatory-protection" | "sms-outbound" | "sms-outbound-content-inspection" | "sms-outbound-longcode" | "sms-outbound-shortcode" | "sms-outbound-tollfree" | "sms-pumping-protection" | "sms-pumping-risk" | "smsmessages-bucket-adjustments" | "smsmessages-outbound-domestic" | "speech-recognition" | "studio-engagements" | "sync" | "sync-actions" | "sync-endpoint-hours" | "sync-endpoint-hours-above-daily-cap" | "taskrouter-tasks" | "totalprice" | "transcriptions" | "trunking-cps" | "trunking-emergency-calls" | "trunking-origination" | "trunking-origination-local" | "trunking-origination-mobile" | "trunking-origination-tollfree" | "trunking-recordings" | "trunking-secure" | "trunking-termination" | "tts-google" | "turnmegabytes" | "turnmegabytes-australia" | "turnmegabytes-brasil" | "turnmegabytes-germany" | "turnmegabytes-india" | "turnmegabytes-ireland" | "turnmegabytes-japan" | "turnmegabytes-singapore" | "turnmegabytes-useast" | "turnmegabytes-uswest" | "twilio-for-salesforce" | "twilio-for-salesforce-licenses" | "twilio-interconnect" | "twiml" | "usage-flex-video" | "usage-functions" | "usage-rcs-basic-messages-outbound" | "usage-rcs-messages" | "usage-rcs-messages-inbound" | "usage-rcs-messaging-carrier-fees" | "usage-rcs-single-messages-outbound" | "verify-package-plans" | "verify-push" | "verify-sna" | "verify-totp" | "verify-voice-sms" | "verify-whatsapp-conversations-business-initiated" | "video-recordings" | "video-rooms-turn-megabytes" | "virtual-agent" | "voice-insights" | "voice-insights-client-insights-on-demand-minute" | "voice-insights-ptsn-insights-on-demand-minute" | "voice-insights-sip-interface-insights-on-demand-minute" | "voice-insights-sip-trunking-insights-on-demand-minute" | "voice-intelligence" | "voice-intelligence-eip-operators" | "voice-intelligence-operators" | "voice-intelligence-transcription" | "wds" | "wireless" | "wireless-data" | "wireless-data-payg" | "wireless-data-payg-africa" | "wireless-data-payg-asia" | "wireless-data-payg-centralandsouthamerica" | "wireless-data-payg-europe" | "wireless-data-payg-northamerica" | "wireless-data-payg-oceania" | "wireless-data-quota1" | "wireless-data-quota1-africa" | "wireless-data-quota1-asia" | "wireless-data-quota1-centralandsouthamerica" | "wireless-data-quota1-europe" | "wireless-data-quota1-northamerica" | "wireless-data-quota1-oceania" | "wireless-data-quota10" | "wireless-data-quota10-africa" | "wireless-data-quota10-asia" | "wireless-data-quota10-centralandsouthamerica" | "wireless-data-quota10-europe" | "wireless-data-quota10-northamerica" | "wireless-data-quota10-oceania" | "wireless-data-quota50" | "wireless-data-quota50-africa" | "wireless-data-quota50-asia" | "wireless-data-quota50-centralandsouthamerica" | "wireless-data-quota50-europe" | "wireless-data-quota50-northamerica" | "wireless-data-quota50-oceania" | "wireless-data-quotacustom" | "wireless-data-quotacustom-africa" | "wireless-data-quotacustom-asia" | "wireless-data-quotacustom-centralandsouthamerica" | "wireless-data-quotacustom-europe" | "wireless-data-quotacustom-northamerica" | "wireless-data-quotacustom-oceania" | "wireless-mrc-payg" | "wireless-mrc-quota1" | "wireless-mrc-quota10" | "wireless-mrc-quota50" | "wireless-mrc-quotacustom" | "wireless-orders" | "wireless-orders-artwork" | "wireless-orders-bulk" | "wireless-orders-esim" | "wireless-orders-starter" | "wireless-quotas" | "wireless-sms-africa" | "wireless-sms-asia" | "wireless-sms-centralandsouthamerica" | "wireless-sms-europe" | "wireless-sms-northamerica" | "wireless-sms-oceania" | "wireless-super-sim" | "wireless-super-sim-data" | "wireless-super-sim-data-north-america-usa" | "wireless-super-sim-data-payg" | "wireless-super-sim-data-payg-europe" | "wireless-super-sim-data-payg-north-america" | "wireless-super-sim-hardware" | "wireless-super-sim-hardware-bulk" | "wireless-super-sim-smscommands" | "wireless-super-sim-smscommands-africa" | "wireless-super-sim-smscommands-asia" | "wireless-super-sim-smscommands-cent-and-south-america" | "wireless-super-sim-smscommands-europe" | "wireless-super-sim-smscommands-north-america" | "wireless-super-sim-smscommands-oceania" | "wireless-super-sim-subscription" | "wireless-super-sim-subscription-payg" | "wireless-usage" | "wireless-usage-commands" | "wireless-usage-commands-africa" | "wireless-usage-commands-asia" | "wireless-usage-commands-centralandsouthamerica" | "wireless-usage-commands-europe" | "wireless-usage-commands-home" | "wireless-usage-commands-northamerica" | "wireless-usage-commands-oceania" | "wireless-usage-commands-roaming" | "wireless-usage-data" | "wireless-usage-data-africa" | "wireless-usage-data-asia" | "wireless-usage-data-centralandsouthamerica" | "wireless-usage-data-custom-additionalmb" | "wireless-usage-data-custom-first5mb" | "wireless-usage-data-domestic-roaming" | "wireless-usage-data-europe" | "wireless-usage-data-individual-additionalgb" | "wireless-usage-data-individual-firstgb" | "wireless-usage-data-international-roaming-canada" | "wireless-usage-data-international-roaming-india" | "wireless-usage-data-international-roaming-mexico" | "wireless-usage-data-northamerica" | "wireless-usage-data-oceania" | "wireless-usage-data-pooled" | "wireless-usage-data-pooled-downlink" | "wireless-usage-data-pooled-uplink" | "wireless-usage-mrc" | "wireless-usage-mrc-custom" | "wireless-usage-mrc-individual" | "wireless-usage-mrc-pooled" | "wireless-usage-mrc-suspended" | "wireless-usage-sms" | "wireless-usage-voice" | "a2p-fast-track-onboarding" | "advisory-services" | "advisory-services-billed" | "advisory-services-call-tracking" | "advisory-services-data-services" | "advisory-services-expenses" | "advisory-services-sip-trunking" | "assets-requests" | "audience-minutes-video" | "authy-bucket-adjustment" | "authy-software" | "calleridlookups-api" | "calleridlookups-programmablevoice" | "calleridlookups-trunking" | "calls-trunking-inbound-tollfree-local" | "calls-trunking-inbound-tollfree-mobile" | "channels-whatsapp-conversation-free-1" | "conference" | "conversational-insights" | "conversational-insights-messages" | "conversational-insights-voice-minutes" | "demo" | "demo-uc-script-test" | "elastic-sip-trunking" | "elastic-sip-trunking-call-transfers" | "enterprise-hippa" | "flex-named-users" | "flex-spinsci" | "flex-users-1" | "flex-wfo-premium-speech-analytics" | "flex-xcelerate" | "functions-rollup" | "imp-v1-usage" | "ip-messaging-addons" | "ivr" | "ivr-conversational" | "ivr-dtmf" | "ivr-virtualagent" | "live" | "live-media-recording-minutes" | "longcode-mps" | "marketplace-analytics-addons" | "marketplace-isv-addons" | "marketplace-messaging-addons" | "marketplace-phonenumbers-addons" | "marketplace-recording-addons" | "marketplace-virtualagent-addons" | "marketplay-pay-addons-shuttle-pay-connector-1" | "marketplay-pay-addons-stripe-pay-connector" | "mms-inbound-longcode-canada" | "mms-inbound-longcode-unitedstates" | "mms-outbound-longcode-canada" | "mms-outbound-longcode-unitedstates" | "mms-outbound-toll-free" | "notify-chatappsandotherchannels" | "notify-notifyservices" | "notify-pushnotifications" | "payment-gateway-connectors" | "payment-solutions" | "pchat-bucket-adjustment" | "phonenumbers-numbers" | "prog-voice-client-android" | "prog-voice-client-android-inbound" | "prog-voice-client-android-outbound" | "prog-voice-client-ios" | "prog-voice-client-ios-inbound" | "prog-voice-client-ios-outbound" | "prog-voice-client-sdk" | "prog-voice-client-web" | "prog-voice-client-web-inbound" | "prog-voice-client-web-outbound" | "programmablevoiceconnectivity-media-streams" | "pstnconnectivity-byoc" | "pstnconnectivity-emergency" | "pstnconnectivity-minutes" | "pstnconnectivity-minutes-1" | "pstnconnectivity-minutesinboundlocal" | "pstnconnectivity-minutesinboundmobile" | "pstnconnectivity-minutesinboundtollfree" | "pstnconnectivity-minutesinboundtollfreelocal" | "pstnconnectivity-minutesinboundtollfreemobile" | "pv-room-hours" | "pv-room-simultaneous-participant-connections" | "pvideo-room-hours-au1" | "pvideo-room-hours-br1" | "pvideo-room-hours-ie1" | "pvideo-room-hours-jp1" | "pvideo-room-hours-sg1" | "pvideo-room-hours-us1" | "pvideo-room-hours-us2" | "recordings-encrypted" | "short-code-setup-fees" | "shortcodes-messages-inbound" | "shortcodes-messages-outbound" | "sms-messages-registrationfees" | "sms-mms-penalty-fees" | "sms-mms-penalty-fees-1" | "sms-pumping-protection-non-usca" | "sms-pumping-protection-usca" | "studio" | "studio-monthly-fees" | "supersim" | "task-router" | "task-router-workers" | "test-quota-buckets" | "test-uc-script-1" | "test-uc-script-demo-2" | "text-to-speech" | "tme" | "tts-basic" | "twilio-editions" | "twilio-interconnect-california" | "twilio-interconnect-california-monthly" | "twilio-interconnect-california-setup" | "twilio-interconnect-frankfurt" | "twilio-interconnect-frankfurt-mo" | "twilio-interconnect-frankfurt-setup" | "twilio-interconnect-london" | "twilio-interconnect-london-mo" | "twilio-interconnect-london-setup" | "twilio-interconnect-sao-paulo" | "twilio-interconnect-sao-paulo-monthly" | "twilio-interconnect-sao-paulo-setup" | "twilio-interconnect-singapore" | "twilio-interconnect-singapore-mo" | "twilio-interconnect-singapore-setup" | "twilio-interconnect-sydney" | "twilio-interconnect-sydney-mo" | "twilio-interconnect-sydney-setup" | "twilio-interconnect-tokyo" | "twilio-interconnect-tokyo-mo" | "twilio-interconnect-tokyo-setup" | "twilio-interconnect-va" | "twilio-interconnect-va-mo" | "twilio-interconnect-va-setup" | "twiml-verbs" | "twiml-verbs-say" | "usage-programmable-messaging-engagement-suite" | "usage-programmable-messaging-fees-services" | "verify-outbound-email" | "verify-packaged-plans" | "verify-silent-network-auth" | "verify-voice-and-sms" | "voice-insights-client-insights-monthy-commit" | "wireless-data-payg-asia-afg" | "wireless-multi-imsi-sim-commands" | "wireless-multi-imsi-sim-commands-usa" | "wireless-multi-imsi-sim-data" | "wireless-multi-imsi-sim-data-eu28" | "wireless-multi-imsi-sim-data-usa" | "wireless-multi-imsi-sim-monthly-fees" | "wireless-multi-imsi-sim-usage" | "wireless-super-sim-data-north-america" | "wireless-super-sim-usage";
/**
 * Options to pass to each
 */
export interface TodayListInstanceEachOptions {
    /** The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved. */
    category?: TodayCategory;
    /** Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date. */
    startDate?: Date;
    /** Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date. */
    endDate?: Date;
    /** Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account. */
    includeSubaccounts?: boolean;
    /** How many resources to return in each list page. The default is 50, and the maximum is 1000. */
    pageSize?: number;
    /** Function to process each record. If this and a positional callback are passed, this one will be used */
    callback?: (item: TodayInstance, done: (err?: Error) => void) => void;
    /** Function to be called upon completion of streaming */
    done?: Function;
    /** Upper limit for the number of records to return. each() guarantees never to return more than limit. Default is no limit */
    limit?: number;
}
/**
 * Options to pass to list
 */
export interface TodayListInstanceOptions {
    /** The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved. */
    category?: TodayCategory;
    /** Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date. */
    startDate?: Date;
    /** Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date. */
    endDate?: Date;
    /** Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account. */
    includeSubaccounts?: boolean;
    /** How many resources to return in each list page. The default is 50, and the maximum is 1000. */
    pageSize?: number;
    /** Upper limit for the number of records to return. list() guarantees never to return more than limit. Default is no limit */
    limit?: number;
}
/**
 * Options to pass to page
 */
export interface TodayListInstancePageOptions {
    /** The [usage category](https://www.twilio.com/docs/usage/api/usage-record#usage-categories) of the UsageRecord resources to read. Only UsageRecord resources in the specified category are retrieved. */
    category?: TodayCategory;
    /** Only include usage that has occurred on or after this date. Specify the date in GMT and format as `YYYY-MM-DD`. You can also specify offsets from the current date, such as: `-30days`, which will set the start date to be 30 days before the current date. */
    startDate?: Date;
    /** Only include usage that occurred on or before this date. Specify the date in GMT and format as `YYYY-MM-DD`.  You can also specify offsets from the current date, such as: `+30days`, which will set the end date to 30 days from the current date. */
    endDate?: Date;
    /** Whether to include usage from the master account and all its subaccounts. Can be: `true` (the default) to include usage from the master account and all subaccounts or `false` to retrieve usage from only the specified account. */
    includeSubaccounts?: boolean;
    /** How many resources to return in each list page. The default is 50, and the maximum is 1000. */
    pageSize?: number;
    /** Page Number, this value is simply for client state */
    pageNumber?: number;
    /** PageToken provided by the API */
    pageToken?: string;
}
export interface TodaySolution {
    accountSid: string;
}
export interface TodayListInstance {
    _version: V2010;
    _solution: TodaySolution;
    _uri: string;
    /**
     * Streams TodayInstance records from the API.
     *
     * This operation lazily loads records as efficiently as possible until the limit
     * is reached.
     *
     * The results are passed into the callback function, so this operation is memory
     * efficient.
     *
     * If a function is passed as the first argument, it will be used as the callback
     * function.
     *
     * @param { TodayListInstanceEachOptions } [params] - Options for request
     * @param { function } [callback] - Function to process each record
     */
    each(callback?: (item: TodayInstance, done: (err?: Error) => void) => void): void;
    each(params: TodayListInstanceEachOptions, callback?: (item: TodayInstance, done: (err?: Error) => void) => void): void;
    /**
     * Retrieve a single target page of TodayInstance records from the API.
     *
     * The request is executed immediately.
     *
     * @param { string } [targetUrl] - API-generated URL for the requested results page
     * @param { function } [callback] - Callback to handle list of records
     */
    getPage(targetUrl: string, callback?: (error: Error | null, items: TodayPage) => any): Promise<TodayPage>;
    /**
     * Lists TodayInstance records from the API as a list.
     *
     * If a function is passed as the first argument, it will be used as the callback
     * function.
     *
     * @param { TodayListInstanceOptions } [params] - Options for request
     * @param { function } [callback] - Callback to handle list of records
     */
    list(callback?: (error: Error | null, items: TodayInstance[]) => any): Promise<TodayInstance[]>;
    list(params: TodayListInstanceOptions, callback?: (error: Error | null, items: TodayInstance[]) => any): Promise<TodayInstance[]>;
    /**
     * Retrieve a single page of TodayInstance records from the API.
     *
     * The request is executed immediately.
     *
     * If a function is passed as the first argument, it will be used as the callback
     * function.
     *
     * @param { TodayListInstancePageOptions } [params] - Options for request
     * @param { function } [callback] - Callback to handle list of records
     */
    page(callback?: (error: Error | null, items: TodayPage) => any): Promise<TodayPage>;
    page(params: TodayListInstancePageOptions, callback?: (error: Error | null, items: TodayPage) => any): Promise<TodayPage>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export declare function TodayListInstance(version: V2010, accountSid: string): TodayListInstance;
interface TodayPayload extends TwilioResponsePayload {
    usage_records: TodayResource[];
}
interface TodayResource {
    account_sid: string;
    api_version: string;
    as_of: string;
    category: TodayCategory;
    count: string;
    count_unit: string;
    description: string;
    end_date: Date;
    price: number;
    price_unit: string;
    start_date: Date;
    subresource_uris: Record<string, string>;
    uri: string;
    usage: string;
    usage_unit: string;
}
export declare class TodayInstance {
    protected _version: V2010;
    constructor(_version: V2010, payload: TodayResource, accountSid: string);
    /**
     * The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that accrued the usage.
     */
    accountSid: string;
    /**
     * The API version used to create the resource.
     */
    apiVersion: string;
    /**
     * Usage records up to date as of this timestamp, formatted as YYYY-MM-DDTHH:MM:SS+00:00. All timestamps are in GMT
     */
    asOf: string;
    category: TodayCategory;
    /**
     * The number of usage events, such as the number of calls.
     */
    count: string;
    /**
     * The units in which `count` is measured, such as `calls` for calls or `messages` for SMS.
     */
    countUnit: string;
    /**
     * A plain-language description of the usage category.
     */
    description: string;
    /**
     * The last date for which usage is included in the UsageRecord. The date is specified in GMT and formatted as `YYYY-MM-DD`.
     */
    endDate: Date;
    /**
     * The total price of the usage in the currency specified in `price_unit` and associated with the account.
     */
    price: number;
    /**
     * The currency in which `price` is measured, in [ISO 4127](https://www.iso.org/iso/home/<USER>/currency_codes.htm) format, such as `usd`, `eur`, and `jpy`.
     */
    priceUnit: string;
    /**
     * The first date for which usage is included in this UsageRecord. The date is specified in GMT and formatted as `YYYY-MM-DD`.
     */
    startDate: Date;
    /**
     * A list of related resources identified by their URIs. For more information, see [List Subresources](https://www.twilio.com/docs/usage/api/usage-record#list-subresources).
     */
    subresourceUris: Record<string, string>;
    /**
     * The URI of the resource, relative to `https://api.twilio.com`.
     */
    uri: string;
    /**
     * The amount used to bill usage and measured in units described in `usage_unit`.
     */
    usage: string;
    /**
     * The units in which `usage` is measured, such as `minutes` for calls or `messages` for SMS.
     */
    usageUnit: string;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): {
        accountSid: string;
        apiVersion: string;
        asOf: string;
        category: TodayCategory;
        count: string;
        countUnit: string;
        description: string;
        endDate: Date;
        price: number;
        priceUnit: string;
        startDate: Date;
        subresourceUris: Record<string, string>;
        uri: string;
        usage: string;
        usageUnit: string;
    };
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
export declare class TodayPage extends Page<V2010, TodayPayload, TodayResource, TodayInstance> {
    /**
     * Initialize the TodayPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version: V2010, response: Response<string>, solution: TodaySolution);
    /**
     * Build an instance of TodayInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload: TodayResource): TodayInstance;
    [inspect.custom](depth: any, options: InspectOptions): string;
}
export {};
