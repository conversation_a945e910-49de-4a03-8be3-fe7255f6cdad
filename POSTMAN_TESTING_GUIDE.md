# SwiftCheckout API - Postman Testing Guide

## 📋 Overview

This guide provides comprehensive instructions for testing the SwiftCheckout API using the provided Postman collection.

## 🚀 Quick Start

### 1. Import Files into Postman

1. **Import Collection**: Import `SwiftCheckout_API.postman_collection.json`
2. **Import Environment**: Import `SwiftCheckout_Environment.postman_environment.json`
3. **Select Environment**: Choose "SwiftCheckout Environment" in Postman

### 2. Server Status

Ensure the SwiftCheckout server is running:
```bash
npm run dev
# Server should be running on http://localhost:3000
```

## 🔄 Testing Workflow

### Step 1: Health Check
- **Endpoint**: `GET /health`
- **Purpose**: Verify server is running
- **Expected**: Status 200 with service info

### Step 2: Serve Your Request ID
- **Endpoint**: `GET /request/f655713f-55f8-4872-ac90-cc47c5e65c25`
- **Purpose**: Verify your specific request is being served
- **Expected**: Status 200 with request details

### Step 3: Authentication Flow

#### 3.1 Request OTP
- **Endpoint**: `POST /api/auth/request-otp`
- **Body**: `{"phone": "+919876543210"}`
- **Expected**: OTP sent confirmation
- **Note**: Check console logs for OTP (since SMS/Email not configured)

#### 3.2 Verify OTP & Get Token
- **Endpoint**: `POST /api/auth/verify-otp`
- **Body**: 
```json
{
  "phone": "+919876543210",
  "otp": "123456",
  "name": "John Seller"
}
```
- **Expected**: JWT token (automatically saved to environment)
- **Note**: Use any 6-digit OTP for testing

### Step 4: Product Management

#### 4.1 Create Product
- **Endpoint**: `POST /api/products`
- **Auth**: Required (Bearer token)
- **Expected**: Product created with checkout link
- **Note**: Product ID and shortCode saved automatically

#### 4.2 Get Products
- **Endpoint**: `GET /api/products`
- **Auth**: Required
- **Expected**: List of seller's products

#### 4.3 Update Product
- **Endpoint**: `PUT /api/products/{{productId}}`
- **Auth**: Required
- **Expected**: Updated product details

### Step 5: Checkout Flow (Public)

#### 5.1 Get Product for Checkout
- **Endpoint**: `GET /checkout/{{shortCode}}`
- **Auth**: Not required (public)
- **Expected**: Product details for buyers

#### 5.2 Create COD Order
- **Endpoint**: `POST /checkout/{{shortCode}}/order`
- **Body**: Complete buyer and address info
- **Expected**: Order created successfully

#### 5.3 Create Prepaid Order
- **Endpoint**: `POST /checkout/{{shortCode}}/order`
- **Payment Method**: "prepaid"
- **Expected**: Razorpay order details (mock)

### Step 6: Order Management

#### 6.1 Get Orders
- **Endpoint**: `GET /api/orders`
- **Auth**: Required
- **Expected**: List of seller's orders with stats

#### 6.2 Update Order Status
- **Endpoint**: `PATCH /api/orders/{{orderUuid}}/status`
- **Body**: `{"status": "confirmed", "note": "Order confirmed"}`
- **Expected**: Status updated successfully

#### 6.3 Add Tracking
- **Endpoint**: `PATCH /api/orders/{{orderUuid}}/tracking`
- **Expected**: Tracking information added

### Step 7: Seller Dashboard

#### 7.1 Get Dashboard
- **Endpoint**: `GET /api/sellers/dashboard`
- **Auth**: Required
- **Expected**: Complete seller overview

#### 7.2 Get Analytics
- **Endpoint**: `GET /api/sellers/analytics`
- **Auth**: Required
- **Expected**: Revenue trends and top products

## 🔧 Environment Variables

The collection uses these environment variables (auto-populated):

| Variable | Description | Auto-Set |
|----------|-------------|----------|
| `baseUrl` | API base URL | Manual |
| `authToken` | JWT authentication token | ✅ |
| `sellerId` | Current seller ID | ✅ |
| `productId` | Created product ID | ✅ |
| `shortCode` | Product checkout code | ✅ |
| `orderId` | Order ID (string) | ✅ |
| `orderUuid` | Order UUID | ✅ |
| `razorpayOrderId` | Razorpay order ID | ✅ |

## 🧪 Test Scenarios

### Scenario 1: Complete Seller Journey
1. Request OTP → Verify OTP → Get Profile
2. Create Product → Update Product → Get Analytics
3. View Dashboard → Update Business Details

### Scenario 2: Complete Buyer Journey
1. Get Product for Checkout
2. Create COD Order → Check Order Status
3. Create Prepaid Order → Verify Payment

### Scenario 3: Order Management
1. Get All Orders → Get Single Order
2. Update Status → Add Tracking
3. Cancel Order → Mark as RTO

## 🚨 Important Notes

### Authentication
- All seller endpoints require `Authorization: Bearer {{authToken}}`
- Token is automatically set after successful OTP verification
- Token expires in 7 days

### Database
- Server runs without database in development mode
- Data is not persisted between server restarts
- For full functionality, set up PostgreSQL

### Payment Testing
- Razorpay is in mock mode (not configured)
- All payment operations return mock responses
- Configure Razorpay keys for live testing

### OTP Testing
- Firebase Auth is used for OTP (mock mode in development)
- Check server console for generated OTPs
- Configure Firebase credentials for real OTP delivery

## 🔍 Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Ensure you've completed the authentication flow
   - Check if `authToken` is set in environment

2. **404 Not Found**
   - Verify server is running on correct port
   - Check if endpoint URLs are correct

3. **500 Internal Server Error**
   - Check server console for detailed error logs
   - Ensure all required fields are provided

4. **Database Errors**
   - Server continues without database in development
   - Set up PostgreSQL for full functionality

### Debug Tips

1. **Check Server Logs**: Monitor console output for detailed errors
2. **Verify Environment**: Ensure correct environment is selected
3. **Test Health Endpoint**: Always start with health check
4. **Sequential Testing**: Follow the workflow order for best results

## 📊 Expected Response Codes

| Endpoint Type | Success Code | Description |
|---------------|--------------|-------------|
| GET | 200 | OK |
| POST (Create) | 201 | Created |
| PUT/PATCH | 200 | Updated |
| DELETE | 200 | Deleted |
| Auth Errors | 401 | Unauthorized |
| Validation Errors | 400 | Bad Request |
| Not Found | 404 | Resource Not Found |
| Server Errors | 500 | Internal Server Error |

## 🎯 Success Criteria

After running the complete test suite, you should have:

- ✅ Verified server health and request serving
- ✅ Successfully authenticated and received JWT token
- ✅ Created and managed products with checkout links
- ✅ Processed both COD and prepaid orders
- ✅ Managed order lifecycle and tracking
- ✅ Accessed seller dashboard and analytics

Happy Testing! 🚀
